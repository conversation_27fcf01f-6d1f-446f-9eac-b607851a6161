import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    GetAggregatedParentTopicMentionsSummaryBodyDto,
    GetParentTopicReviewsWithAnalysesBodyDto,
    ParentTopicMentionsSummaryDto,
    ParentTopicReviewsWithAnalysesDto,
    SegmentAnalysisParentTopicDto,
} from '@malou-io/package-dto';
import { ApiResultV2, ReviewAnalysisTag } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

@Injectable({ providedIn: 'root' })
export class SegmentAnalysisParentTopicsService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/segment-analysis-parent-topics`;

    constructor(private readonly _http: HttpClient) {}

    create(params: {
        name: string;
        category: ReviewAnalysisTag;
        restaurantId: string;
    }): Observable<ApiResultV2<SegmentAnalysisParentTopicDto>> {
        return this._http.post<ApiResultV2<SegmentAnalysisParentTopicDto>>(`${this.API_BASE_URL}`, params);
    }

    update(id: string, params: { isFavorite: boolean }): Observable<ApiResultV2<SegmentAnalysisParentTopicDto>> {
        return this._http.put<ApiResultV2<SegmentAnalysisParentTopicDto>>(`${this.API_BASE_URL}/${id}`, params);
    }

    merge({
        topicIdToKeep,
        topicIdsToMerge,
        restaurantId,
    }: {
        topicIdToKeep: string;
        topicIdsToMerge: string[];
        restaurantId: string;
    }): Observable<ApiResultV2<void>> {
        const params = {
            topicIdToKeep,
            topicIdsToMerge,
            restaurantId,
        };
        return this._http.put<ApiResultV2<void>>(`${this.API_BASE_URL}/merge`, params);
    }

    delete(id: string): Observable<ApiResultV2<void>> {
        return this._http.delete<ApiResultV2<void>>(`${this.API_BASE_URL}/${id}`);
    }

    getAggregatedMentionsSummary(params: GetAggregatedParentTopicMentionsSummaryBodyDto): Observable<ParentTopicMentionsSummaryDto[]> {
        return this._http
            .post<ApiResultV2<ParentTopicMentionsSummaryDto[]>>(`${this.API_BASE_URL}/aggregated-mentions-summary`, params)
            .pipe(map((response) => response.data));
    }

    getParentTopicReviewsWithAnalyses(params: GetParentTopicReviewsWithAnalysesBodyDto): Observable<ParentTopicReviewsWithAnalysesDto[]> {
        return this._http
            .post<ApiResultV2<ParentTopicReviewsWithAnalysesDto[]>>(`${this.API_BASE_URL}/parent-topic-reviews-with-analyses`, params)
            .pipe(map((response) => response.data));
    }
}
