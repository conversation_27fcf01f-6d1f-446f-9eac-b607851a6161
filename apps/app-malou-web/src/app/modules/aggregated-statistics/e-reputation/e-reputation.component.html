<div class="flex h-full flex-col gap-y-4">
    <div class="flex h-full flex-col gap-y-4">
        <mat-tab-group
            class="malou-tab-group h-full"
            [selectedIndex]="selectedIndex()"
            [disableRipple]="true"
            (selectedIndexChange)="handleTabChange($event)">
            <mat-tab label="{{ 'statistics.e_reputation.tabs.reviews' | translate }}">
                <ng-container
                    [ngTemplateOutlet]="scrollableTab"
                    [ngTemplateOutletContext]="{
                        template: reviewsTemplate,
                    }">
                </ng-container>
            </mat-tab>
            <mat-tab>
                <ng-template mat-tab-label>
                    <span>{{ 'statistics.e_reputation.tabs.semantic_analysis' | translate }}</span>
                    @if (isNewAggregatedSemanticAnalysisFeatureEnabledForDate()) {
                        <div class="malou-chip malou-chip--purple !ml-[10px] !h-[20px] !w-[40px] justify-center !p-0">
                            <span>{{ 'common.new' | translate }}</span>
                        </div>
                    }
                </ng-template>
                <ng-container
                    [ngTemplateOutlet]="scrollableTab"
                    [ngTemplateOutletContext]="{
                        template: reviewAnalysesTemplate,
                    }">
                </ng-container>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>

<ng-template let-template="template" #scrollableTab>
    <div class="flex h-full flex-col gap-4 px-8.5 py-4 sm:px-4">
        @if (!screenSizeService.isPhoneScreen) {
            <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
        }
        <div class="h-full overflow-y-auto">
            <div class="flex flex-col gap-4">
                @if (screenSizeService.isPhoneScreen) {
                    <ng-container [ngTemplateOutlet]="filtersTemplate"></ng-container>
                }
                @if (hasEnoughRestaurantsSelected() && hasPlatformsSelected()) {
                    <ng-container [ngTemplateOutlet]="template"></ng-container>
                } @else if (!hasEnoughRestaurantsSelected()) {
                    <ng-container
                        [ngTemplateOutlet]="noDataTemplate"
                        [ngTemplateOutletContext]="{
                            text: 'aggregated_statistics.errors.select_at_least_2_businesses' | translate,
                        }">
                    </ng-container>
                } @else {
                    <ng-container
                        [ngTemplateOutlet]="noDataTemplate"
                        [ngTemplateOutletContext]="{
                            text: 'aggregated_statistics.errors.platforms_not_selected' | translate,
                        }">
                    </ng-container>
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template #filtersTemplate>
    <div class="flex items-end gap-4 px-8.5 md:flex-col md:items-center md:px-0">
        <div class="flex-1 sm:w-full">
            <app-aggregated-statistics-filters [page]="PlatformFilterPage.E_REPUTATION"></app-aggregated-statistics-filters>
        </div>

        <app-menu-button-v2 [text]="'statistics.common.download_statistics' | translate" [size]="MenuButtonSize.LARGE">
            <button
                class="flex !h-12 items-center justify-center !px-5"
                mat-menu-item
                [disabled]="!hasEnoughRestaurantsSelected() || isLoading()"
                (click)="openDownloadStatisticsModal()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ InsightsTab.E_REPUTATION | enumTranslate: 'insights_tab_name' }}
                </span>
            </button>
            <button
                class="flex !h-12 items-center justify-center !px-5"
                mat-menu-item
                [disabled]="isLoading()"
                (click)="downloadInsightsSummary()">
                <span class="malou-text-14--regular text-malou-color-text-2">
                    {{ 'statistics.common.download_statistics_sub_text' | translate }}
                </span>
            </button>
        </app-menu-button-v2>
    </div>
</ng-template>

<ng-template #reviewsTemplate>
    <app-reviews-insights
        (tableSortOptionsChange)="onTableSortOptionsChange($event.chart, $event.value)"
        (sortByChange)="onSortByChange($event.chart, $event.value)"
        (viewModeChange)="onViewModeChange($event.chart, $event.value)"
        (isLoadingEvent)="areReviewsLoading.set($event)"></app-reviews-insights>
</ng-template>

<ng-template #reviewAnalysesTemplate>
    @if (isNewAggregatedSemanticAnalysisFeatureEnabledForDate()) {
        @if (isAggregatedSemanticAnalysisV2Enabled()) {
            <app-semantic-analysis-v2> </app-semantic-analysis-v2>
        } @else {
            <app-aggregated-semantic-analysis
                (isLoadingEvent)="isReviewsAnalysesLoading.set($event)"
                (tagsEvolutionSortByChange)="onSortByChange(InsightsChart.AGGREGATED_REVIEW_ANALYSES_TAG_EVOLUTION, $event)">
            </app-aggregated-semantic-analysis>
        }
    } @else {
        <app-review-analyses-v2
            (tagsEvolutionSortByChange)="onSortByChange(InsightsChart.AGGREGATED_REVIEW_ANALYSES_TAG_EVOLUTION, $event)"
            (isLoadingEvent)="isReviewsAnalysesLoading.set($event)"></app-review-analyses-v2>
    }
</ng-template>

<ng-template let-text="text" #noDataTemplate>
    <div class="flex flex-col items-center py-6">
        <img class="mb-6 h-20 w-20" alt="Taster illustration" [src]="Illustration.Taster | illustrationPathResolver" />
        <span class="malou-text-14--bold mb-2">{{ 'common.no_data' | translate }}</span>
        <span class="malou-text-10--regular">{{ text }}</span>
    </div>
</ng-template>
