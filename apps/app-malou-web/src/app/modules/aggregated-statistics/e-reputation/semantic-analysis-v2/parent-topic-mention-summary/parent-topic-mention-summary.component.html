<div
    class="malou-simple-card mb-4 flex w-full break-inside-avoid flex-col gap-3 px-6 py-6 pb-6 md:flex-col md:px-2"
    [ngClass]="{
        'max-h-[550px]': !isPdfDownload(),
    }">
    <div class="flex items-center justify-between gap-1">
        <div class="malou-text-section-title malou-color-text-1">
            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.title' | translate }}
        </div>
        <app-search [placeholder]="'common.search' | translate" (searchChange)="onSearchValueChange($event)"></app-search>
    </div>
    <div class="justify-content-between flex overflow-auto md:flex-col-reverse">
        <div class="min-w-0 flex-1">
            <ng-container [ngTemplateOutlet]="tableTemplate"></ng-container>
        </div>
    </div>
</div>

<ng-template #tableTemplate>
    <div class="hidden w-full md:flex">
        <app-sort-by-filters
            class="w-full"
            [sortOptions]="SORT_OPTIONS"
            [sortOrder]="-1"
            [sortBy]="'mentions'"
            (changeSortBy)="onSortByChange($event)"
            (toggleSortOrder)="onSortOrderChange()">
        </app-sort-by-filters>
    </div>
    <mat-table
        class="malou-mat-table w-full"
        matSort
        [dataSource]="dataSource()"
        [matSortActive]="sort().active"
        [matSortDirection]="sort().direction"
        #table="matTable">
        <ng-container [matColumnDef]="TableColumns.EVOLUTION">
            <mat-header-cell *matHeaderCellDef class="!bg-white" mat-sort-header>
                <span [position]="TooltipPosition.BOTTOM" [appCustomTooltip]="evolutionInfoTooltipTemplate">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.evolution' | translate }}
                </span>
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.evolution' | translate }}
                </span>
                <div
                    class="malou-text-13--bold my-1 w-16 rounded-[10px] p-4 text-center text-malou-color-text-1"
                    [position]="TooltipPosition.BOTTOM"
                    [appCustomTooltip]="evolutionTooltipTemplate"
                    [ngClass]="{
                        'border-color-dark border bg-white':
                            element.negativeMentionsEvolution === 0 || element.negativeMentionsEvolution === null,
                        'bg-malou-color-chart-pink--pale':
                            element.negativeMentionsEvolution !== null && element.negativeMentionsEvolution > 0,
                        'bg-malou-color-state-success--light':
                            element.negativeMentionsEvolution !== null && element.negativeMentionsEvolution < 0,
                    }">
                    {{ element.negativeMentionsEvolution !== null && element.negativeMentionsEvolution > 0 ? '+' : '' }}
                    {{ element.negativeMentionsEvolution ?? '-' }}
                </div>

                <ng-template #evolutionTooltipTemplate>
                    <span> {{ getTopicNegativeMentionsEvolutionTooltip | applyPure: element }} </span>
                </ng-template>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableColumns.TOPIC_NAME">
            <mat-header-cell *matHeaderCellDef class="!bg-white text-malou-color-text-2" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.topics' | translate }}
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.topic' | translate }}
                </span>
                <span class="malou-text-13--semibold text-malou-color-text-1">
                    {{ element.topicName }}
                </span>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableColumns.MENTIONS">
            <mat-header-cell *matHeaderCellDef class="!bg-white text-malou-color-text-2" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.mentions' | translate }}
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.mentions' | translate }}
                </span>
                <div class="mentions-gauge-container">
                    <div class="mentions-gauge">
                        <div class="negative-section">
                            <div
                                class="negative-bar bg-malou-color-chart-pink--accent"
                                [position]="TooltipPosition.BOTTOM"
                                [appCustomTooltip]="currentTopicTooltip"
                                [style.width.%]="
                                    calculateBarWidth
                                        | applyPure: element.negativeMentions : element.positiveMentions : ReviewAnalysisSentiment.NEGATIVE
                                ">
                                @if (element.negativeMentions > 0) {
                                    <span class="mention-count">{{ element.negativeMentions }}</span>
                                }
                            </div>
                        </div>

                        <div class="separator"></div>

                        <div class="positive-section">
                            <div
                                class="positive-bar bg-malou-color-background-success-dark"
                                [position]="TooltipPosition.BOTTOM"
                                [appCustomTooltip]="currentTopicTooltip"
                                [style.width.%]="
                                    calculateBarWidth
                                        | applyPure: element.negativeMentions : element.positiveMentions : ReviewAnalysisSentiment.POSITIVE
                                ">
                                @if (element.positiveMentions > 0) {
                                    <span class="mention-count">{{ element.positiveMentions }}</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <ng-template #currentTopicTooltip>
                    <div class="flex flex-col gap-1">
                        <span class="malou-text-9 flex items-center gap-1">
                            {{ element.negativeMentions }}
                            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.mentions.negative' | translate }}
                            @if (element.negativeEvolutionIcon) {
                                <mat-icon class="!h-3 !w-3" [svgIcon]="element.negativeEvolutionIcon"></mat-icon>
                            }
                            @if (element.negativeMentionsEvolution !== null) {
                                ({{ element.negativeMentionsEvolution }})
                            }
                        </span>
                        <span class="malou-text-9 flex items-center gap-1">
                            {{ element.positiveMentions }}
                            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.mentions.positive' | translate }}
                            @if (element.positiveEvolutionIcon) {
                                <mat-icon class="!h-3 !w-3" [svgIcon]="element.positiveEvolutionIcon"></mat-icon>
                            }
                            @if (element.positiveMentionsEvolution !== null) {
                                ({{ element.positiveMentionsEvolution }})
                            }
                        </span>
                    </div>
                </ng-template>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableColumns.RESTAURANTS_COUNT">
            <mat-header-cell *matHeaderCellDef class="!bg-white text-malou-color-text-2" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.locations' | translate }}
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.locations' | translate }}
                </span>
                <span class="malou-text-13--regular mr-6 text-malou-color-text-1">
                    {{ element.restaurantsCount }}
                </span>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableColumns.SCORE">
            <mat-header-cell *matHeaderCellDef mat-sort-header>
                <div
                    class="flex items-center gap-1 !bg-white text-malou-color-text-2"
                    [position]="TooltipPosition.BOTTOM"
                    [appCustomTooltip]="scoreTooltipTemplate">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.score' | translate }}
                    <mat-icon class="!h-4 !w-4" [svgIcon]="SvgIcon.INFO_ROUND"></mat-icon>
                </div>
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.score' | translate }}
                </span>
                <div
                    class="malou-chip !h-8"
                    [ngClass]="{
                        'malou-chip--pink': element.score === ParentTopicScore.URGENT,
                        'malou-chip--yellow': element.score === ParentTopicScore.WORRYING,
                        'malou-chip--green': element.score === ParentTopicScore.STABLE,
                    }">
                    {{ parentTopicScoreLabelMap[element.score] }}
                </div>
            </mat-cell>
        </ng-container>

        <ng-container [matColumnDef]="TableColumns.ANALYZE">
            <mat-header-cell *matHeaderCellDef class="!bg-white text-malou-color-text-2" mat-sort-header>
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.analysis' | translate }}
            </mat-header-cell>

            <mat-cell *matCellDef="let element; table: table">
                <span class="malou-text-13--bold hidden text-malou-color-text-2 md:flex">
                    {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.analysis' | translate }}
                </span>
                <button class="malou-btn-flat btn-sm !px-0" mat-button (click)="openParentTopicDetailsModal(element)">
                    {{ 'common.see' | translate }}
                </button>
            </mat-cell>
        </ng-container>

        <mat-header-row *matHeaderRowDef="displayedColumns(); sticky: true" class="!bg-white"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns(); table: table; trackBy: trackByTopicName"></mat-row>
    </mat-table>
</ng-template>

<ng-template #scoreTooltipTemplate>
    <div class="flex flex-col gap-1">
        <span class="malou-text-10--semibold">
            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.score_calculation' | translate }}
        </span>
        <span class="malou-text-9">
            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.score_description' | translate }}
        </span>
        <div class="flex flex-col">
            <span class="malou-text-9">
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.score_factor_1' | translate }}
            </span>
            <span class="malou-text-9">
                {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.score_factor_2' | translate }}
            </span>
        </div>
        <span class="malou-text-9">
            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.score_attention' | translate }}
        </span>
    </div>
</ng-template>

<ng-template #evolutionInfoTooltipTemplate>
    <div class="flex flex-col">
        <span>{{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.evolution_info_line_1' | translate }}</span>
        <span>{{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.evolution_info_line_2' | translate }}</span>
    </div>
</ng-template>
