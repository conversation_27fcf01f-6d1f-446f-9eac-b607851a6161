@use '_malou_mixins.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_variables.scss' as *;

.mat-column-evolution {
    flex: 0.5;
    justify-content: start;
}
.mat-column-topicName {
    flex: 0.5;
    justify-content: start;
}
.mat-column-mentions {
    flex: 2;
    justify-content: center;
}
.mat-column-restaurantsCount {
    flex: 0.5;
    justify-content: end;
}
.mat-column-score {
    flex: 0.5;
    justify-content: center;
}
.mat-column-analyze {
    flex: 0.5;
    justify-content: center;
}

@include malou-respond-to('medium') {
    .mat-mdc-header-row {
        display: none;
    }

    .mat-mdc-cell {
        display: flex !important;
        min-height: unset !important;
        padding-right: 0 !important;
        justify-content: start !important;
        gap: 16px !important;
    }

    .mat-mdc-row {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        padding: 16px 24px !important;
        gap: 12px !important;
    }

    .malou-mat-table.mat-mdc-table mat-cell:first-of-type {
        padding-left: 0;
    }
}

// Mentions gauge styles
.mentions-gauge-container {
    width: 100%;
    padding: 0 8px;
}

.mentions-gauge {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 0;
}

.negative-section,
.positive-section {
    flex: 1;
    display: flex;
    min-width: 0;
}

.negative-section {
    justify-content: flex-end;
}

.positive-section {
    justify-content: flex-start;
}

.negative-bar,
.positive-bar {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.mention-count {
    font-size: 11px;
    font-weight: 500;
    color: #ffffff;
    white-space: nowrap;
}

.separator {
    width: 2px;
    height: 24px;
    background-color: #1f2937;
    flex-shrink: 0;
}
