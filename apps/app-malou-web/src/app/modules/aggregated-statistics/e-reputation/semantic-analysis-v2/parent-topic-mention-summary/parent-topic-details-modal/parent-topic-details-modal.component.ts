import { Date<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, Signal, signal, WritableSignal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { SegmentAnalysisParentTopicsService } from ':core/services/segment-analysis-parent-topics.service';
import { ParentTopicMentionSummaryContext } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.context';
import {
    ParentTopicDetailsModalData,
    ParentTopicMentionData,
    ParentTopicReviewsWithAnalyses,
    ParentTopicScore,
} from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.model';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

import { ParentTopicLocationsComponent } from './parent-topic-locations/parent-topic-locations.component';
import { ParentTopicReviewsComponent } from './parent-topic-reviews/parent-topic-reviews.component';

enum ParentTopicDetailsTabs {
    ANALYSIS,
    EVOLUTION,
    REVIEWS,
}
@Component({
    selector: 'app-parent-topic-details-modal',
    imports: [
        MatIcon,
        NgClass,
        DatePipe,
        ParentTopicLocationsComponent,
        NgTemplateOutlet,
        MatTabsModule,
        ParentTopicReviewsComponent,
        TranslateModule,
    ],
    templateUrl: './parent-topic-details-modal.component.html',
    styleUrl: './parent-topic-details-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentTopicDetailsModalComponent implements OnInit {
    private readonly _dialogRef = inject(MatDialogRef<ParentTopicDetailsModalComponent>);
    public readonly data: ParentTopicDetailsModalData = inject(MAT_DIALOG_DATA);

    private readonly _parentTopicMentionSummaryContext = inject(ParentTopicMentionSummaryContext);
    private readonly _translate = inject(TranslateService);
    private readonly _segmentAnalysisParentTopicsService = inject(SegmentAnalysisParentTopicsService);

    readonly SvgIcon = SvgIcon;
    readonly ParentTopicScore = ParentTopicScore;
    readonly parentTopicScoreLabelMap: Record<ParentTopicScore, string> = {
        [ParentTopicScore.URGENT]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.urgent'
        ),
        [ParentTopicScore.WORRYING]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.worrying'
        ),
        [ParentTopicScore.STABLE]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.stable'
        ),
    };

    readonly currentTopicData: WritableSignal<ParentTopicMentionData | undefined> = signal(
        this._parentTopicMentionSummaryContext.parentTopicReviewsByTopic().getTopic(this.data.topicName)
    );
    readonly selectedTabIndex: WritableSignal<number> = signal(ParentTopicDetailsTabs.REVIEWS);

    // first location in the list selected by default
    readonly selectedLocationId: WritableSignal<string> = signal(
        this._parentTopicMentionSummaryContext.getTopicFirstLocationId(this.data.topicName) || this.data.restaurants[0].id
    );
    readonly selectedLocationReviews: Signal<ParentTopicReviewsWithAnalyses[]> = computed(() => {
        const currentTopicData = this.currentTopicData();
        const locationId = this.selectedLocationId();
        if (!currentTopicData || !locationId) {
            return [];
        }
        return currentTopicData.reviewsWithAnalyses[locationId] || [];
    });

    ngOnInit(): void {
        const hasTopicData = this._parentTopicMentionSummaryContext.parentTopicReviewsByTopic().hasTopic(this.data.topicName);
        if (!hasTopicData) {
            this._segmentAnalysisParentTopicsService
                .getParentTopicReviewsWithAnalyses({
                    topicName: this.data.topicName,
                    restaurantIds: this.data.restaurants.map((restaurant) => restaurant.id),
                    startDate: this.data.period.startDate,
                    endDate: this.data.period.endDate,
                    platformKeys: this.data.platformKeys,
                })
                .subscribe({
                    next: (res) => {
                        this._parentTopicMentionSummaryContext.parentTopicReviewsByTopic().setTopic({
                            topicName: this.data.topicName,
                            score: this.data.score,
                            restaurants: this.data.restaurants,
                            parentTopicReviewsWithAnalyses: res,
                        });

                        this.currentTopicData.set(
                            this._parentTopicMentionSummaryContext.parentTopicReviewsByTopic().getTopic(this.data.topicName)
                        );
                        this._initializeSelectedLocation();
                    },
                    error: (err) => {
                        console.warn('Error fetching parent topic reviews with analyses:', err);
                    },
                });
        }
    }

    onSelectedLocationChange(locationId: string): void {
        this.selectedLocationId.set(locationId);
    }

    handleTabChange(index: number): void {
        this.selectedTabIndex.set(index);
    }

    close(): void {
        this._dialogRef.close();
    }

    private _initializeSelectedLocation(): void {
        const currentTopicData = this.currentTopicData();
        const firstLocationId = currentTopicData?.restaurants[0]?.id;
        if (firstLocationId) {
            this.selectedLocationId.set(firstLocationId);
        }
    }
}
