<div class="malou-dialog overflow-y-hidden">
    <div class="malou-dialog__header border-malou-color-border-1 gap-x-5 border-b-2 pb-3">
        <div class="flex flex-col">
            <div class="flex items-center gap-2">
                <span>{{
                    'aggregated_statistics.e_reputation.parent_topic_mention_summary.modal.title' | translate: { topicName: data.topicName }
                }}</span>
                <div
                    class="malou-chip !h-8"
                    [ngClass]="{
                        'malou-chip--pink': data.score === ParentTopicScore.URGENT,
                        'malou-chip--yellow': data.score === ParentTopicScore.WORRYING,
                        'malou-chip--green': data.score === ParentTopicScore.STABLE,
                    }">
                    {{ parentTopicScoreLabelMap[data.score] }}
                </div>
            </div>
            <div class="flex items-center gap-2">
                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CALENDAR"></mat-icon>
                <span class="malou-text-13--regular malou-color-text-2">
                    {{ data.period.startDate | date: 'dd/MM/yyyy' }} {{ 'common.to' | translate }}
                    {{ data.period.endDate | date: 'dd/MM/yyyy' }}
                </span>
            </div>
        </div>
        <div class="flex">
            <button class="malou-btn-icon" mat-icon-button (click)="close()">
                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
            </button>
        </div>
    </div>
    <div class="malou-dialog__body !ml-0 overflow-y-hidden !pl-0">
        @if (currentTopicData(); as topicData) {
            <div class="flex h-full">
                <div class="border-malou-color-border-1 flex h-full w-1/5 border-r-2">
                    <app-parent-topic-locations
                        class="w-full"
                        [locationsData]="topicData.restaurants"
                        [selectedLocationId]="selectedLocationId()"
                        [allRestaurantsNegativeMentionsCount]="topicData.allRestaurantsNegativeMentionsCount"
                        [allRestaurantsPositiveMentionsCount]="topicData.allRestaurantsPositiveMentionsCount"
                        (selectLocationChange)="onSelectedLocationChange($event)">
                    </app-parent-topic-locations>
                </div>
                <div class="flex h-full w-4/5">
                    <ng-container
                        *ngTemplateOutlet="
                            mainTemplate;
                            context: {
                                locationId: selectedLocationId(),
                                topicData: topicData,
                            }
                        ">
                    </ng-container>
                </div>
            </div>
        }
    </div>
</div>

<ng-template let-locationId="locationId" let-topicData="topicData" #mainTemplate>
    <div class="flex h-full w-full flex-col gap-y-4">
        <div class="flex h-full flex-col">
            <mat-tab-group
                class="malou-tab-group h-full"
                [disableRipple]="true"
                [selectedIndex]="selectedTabIndex()"
                (selectedIndexChange)="handleTabChange($event)">
                <mat-tab label="{{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.modal.analysis' | translate }}">
                    <div>Analysis</div>
                </mat-tab>
                <mat-tab label="{{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.modal.evolution' | translate }}">
                    <div>Evolution</div>
                </mat-tab>
                <mat-tab label="{{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.modal.reviews' | translate }}">
                    <app-parent-topic-reviews
                        class="h-full"
                        [reviewsWithAnalyses]="selectedLocationReviews()"
                        [locationId]="selectedLocationId()">
                    </app-parent-topic-reviews>
                </mat-tab>
            </mat-tab-group>
        </div>
    </div>
</ng-template>
