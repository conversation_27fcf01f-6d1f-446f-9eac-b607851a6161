import { NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, input, signal, WritableSignal } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { uniqBy } from 'lodash';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { ParentTopicReviewsWithAnalyses } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.model';
import { DisplayMenuItemsPipe } from ':modules/reviews/pipe/display-menu-items.pipe';
import { GetMenuItemReviewsPipe } from ':modules/reviews/pipe/get-menu-item-reviews.pipe';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { StarGaugeComponent } from ':shared/components/star-gauge/star-gauge.component';
import { SemanticAnalysisInsightsReview } from ':shared/models';
import { SegmentAnalysis } from ':shared/models/segment-analysis';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { AvatarPipe } from ':shared/pipes/avatar.pipe';
import { DateToStringPipe } from ':shared/pipes/date.pipe';

enum ReviewSentimentFilterBy {
    ALL = 'ALL',
    POSITIVE = 'POSITIVE',
    NEGATIVE = 'NEGATIVE',
}

@Component({
    selector: 'app-parent-topic-reviews',
    imports: [
        SelectComponent,
        PlatformLogoComponent,
        NgTemplateOutlet,
        MatIconModule,
        AvatarPipe,
        DateToStringPipe,
        LazyLoadImageModule,
        TranslateModule,
        NgClass,
        StarGaugeComponent,
        ApplySelfPurePipe,
        GetMenuItemReviewsPipe,
        DisplayMenuItemsPipe,
    ],
    templateUrl: './parent-topic-reviews.component.html',
    styleUrl: './parent-topic-reviews.component.scss',
})
export class ParentTopicReviewsComponent {
    readonly reviewsWithAnalyses = input.required<ParentTopicReviewsWithAnalyses[]>();
    readonly locationId = input.required<string>();

    private readonly _translate = inject(TranslateService);

    readonly MAX_MENU_ITEM_SHOWN = 3;
    readonly SvgIcon = SvgIcon;

    readonly reviewSentimentFilterByOptions = Object.values(ReviewSentimentFilterBy);
    readonly reviewSentimentFilterBy: WritableSignal<ReviewSentimentFilterBy> = signal(ReviewSentimentFilterBy.ALL);
    readonly reviewSentimentFilterByControl: FormControl<ReviewSentimentFilterBy> = new FormControl<ReviewSentimentFilterBy>(
        ReviewSentimentFilterBy.ALL
    ) as FormControl<ReviewSentimentFilterBy>;

    private readonly _reviewsWithPositiveSegments = computed<SemanticAnalysisInsightsReview[]>(() => {
        const reviews = this.reviewsWithAnalyses();

        return reviews
            .filter((review) => review.segmentAnalyses.some((analysis) => analysis.sentiment === ReviewAnalysisSentiment.POSITIVE))
            .map((review) => this._fromParentTopicReviewsWithAnalyses(review, ReviewAnalysisSentiment.POSITIVE));
    });

    private readonly _reviewsWithNegativeSegments = computed<SemanticAnalysisInsightsReview[]>(() => {
        const reviews = this.reviewsWithAnalyses();

        return reviews
            .filter((review) => review.segmentAnalyses.some((analysis) => analysis.sentiment === ReviewAnalysisSentiment.NEGATIVE))
            .map((review) => this._fromParentTopicReviewsWithAnalyses(review, ReviewAnalysisSentiment.NEGATIVE));
    });

    readonly filteredReviewsWithAnalyses = computed<SemanticAnalysisInsightsReview[]>(() => {
        const filterBy = this.reviewSentimentFilterBy();
        if (filterBy === ReviewSentimentFilterBy.POSITIVE) {
            return this._reviewsWithPositiveSegments();
        }

        if (filterBy === ReviewSentimentFilterBy.NEGATIVE) {
            return this._reviewsWithNegativeSegments();
        }

        const allReviews = [...this._reviewsWithPositiveSegments(), ...this._reviewsWithNegativeSegments()];

        return uniqBy(allReviews, (review) => review.key + review.socialId);
    });

    onReviewSentimentFilterByChange(option: ReviewSentimentFilterBy): void {
        this.reviewSentimentFilterBy.set(option);
    }

    reviewSentimentFilterByDisplayFn = (option: ReviewSentimentFilterBy): string => {
        switch (option) {
            case ReviewSentimentFilterBy.ALL:
                return this._translate.instant('common.all');
            case ReviewSentimentFilterBy.POSITIVE:
                return this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.reviews.positive');
            case ReviewSentimentFilterBy.NEGATIVE:
                return this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.reviews.negative');
            default:
                return '';
        }
    };

    private _fromParentTopicReviewsWithAnalyses(
        review: ParentTopicReviewsWithAnalyses,
        sentiment: ReviewAnalysisSentiment
    ): SemanticAnalysisInsightsReview {
        return new SemanticAnalysisInsightsReview({
            ...review,
            restaurantId: this.locationId(),
            socialCreatedAt: new Date(review.socialCreatedAt),
            sentiment,
            semanticAnalysisSegments: review.segmentAnalyses.map(
                (segment) =>
                    new SegmentAnalysis({
                        platformKey: review.key,
                        reviewSocialId: review.socialId || '',
                        platformSocialId: '**', // TODO: get real platform social id
                        category: segment.category,
                        aiFoundSegment: segment.aiFoundSegment,
                        sentiment: segment.sentiment,
                        segment: segment.segment,
                    })
            ),
        });
    }
}
