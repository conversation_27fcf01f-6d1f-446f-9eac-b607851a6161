import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';

import { ExperimentationService } from ':core/services/experimentation.service';
import { ParentTopicMentionSummaryComponent } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.component';

@Component({
    selector: 'app-semantic-analysis-v2',
    imports: [ParentTopicMentionSummaryComponent],
    templateUrl: './semantic-analysis-v2.component.html',
    styleUrl: './semantic-analysis-v2.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SemanticAnalysisV2Component {
    private readonly _experimentationService = inject(ExperimentationService);

    readonly isAggregatedSemanticAnalysisTopicSummaryEnabled = toSignal(
        this._experimentationService.isFeatureEnabled$('release-aggregated-semantic-analysis-topic-summary')
    );
}
