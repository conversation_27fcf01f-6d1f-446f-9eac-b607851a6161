import { Injectable, signal, WritableSignal } from '@angular/core';

import { ParentTopicReviewsByTopic } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.model';

@Injectable({
    providedIn: 'root',
})
export class ParentTopicMentionSummaryContext {
    readonly parentTopicReviewsByTopic: WritableSignal<ParentTopicReviewsByTopic> = signal(new ParentTopicReviewsByTopic());

    clearParentTopicReviewsByTopic(): void {
        this.parentTopicReviewsByTopic().clear();
    }

    getTopicFirstLocationId(topicName: string): string | null {
        const topicData = this.parentTopicReviewsByTopic().getTopic(topicName);
        if (!topicData) {
            return null;
        }
        return topicData.restaurants.length > 0 ? topicData.restaurants[0].id : null;
    }
}
