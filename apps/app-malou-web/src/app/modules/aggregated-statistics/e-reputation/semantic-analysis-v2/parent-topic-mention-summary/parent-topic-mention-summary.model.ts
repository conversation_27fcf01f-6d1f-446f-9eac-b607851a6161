import { ParentTopicReviewsWithAnalysesDto } from '@malou-io/package-dto';
import { PlatformKey, ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { LightRestaurant } from ':shared/models';

export enum ParentTopicScore {
    URGENT,
    WORRYING,
    STABLE,
}

export interface ParentTopicDetailsModalData {
    topicName: string;
    restaurants: LightRestaurant[];
    negativeMentionsCount: number;
    positiveMentionsCount: number;
    score: ParentTopicScore;
    period: { startDate: Date; endDate: Date };
    platformKeys: PlatformKey[];
}

export class RestaurantWithMentions extends LightRestaurant {
    negativeMentionsCount: number;
    positiveMentionsCount: number;

    constructor({
        lightRestaurant,
        negativeMentionsCount,
        positiveMentionsCount,
    }: {
        lightRestaurant: LightRestaurant;
        negativeMentionsCount: number;
        positiveMentionsCount: number;
    }) {
        super(lightRestaurant);
        this.negativeMentionsCount = negativeMentionsCount ?? 0;
        this.positiveMentionsCount = positiveMentionsCount ?? 0;
    }
}

export interface ParentTopicMentionData {
    restaurants: RestaurantWithMentions[];
    score: ParentTopicScore;
    allRestaurantsNegativeMentionsCount: number;
    allRestaurantsPositiveMentionsCount: number;
    reviewsWithAnalyses: {
        [restaurantId: string]: ParentTopicReviewsWithAnalysesDto['reviewsWithAnalyses'];
    };
}

export type ParentTopicReviewsWithAnalyses = ParentTopicReviewsWithAnalysesDto['reviewsWithAnalyses'][number];

export class ParentTopicReviewsByTopic {
    private _topics: Map<string, ParentTopicMentionData> = new Map();

    hasTopic(topicName: string): boolean {
        return this._topics.has(topicName);
    }

    setTopic({
        topicName,
        restaurants,
        score,
        parentTopicReviewsWithAnalyses,
    }: {
        topicName: string;
        restaurants: LightRestaurant[];
        score: ParentTopicScore;
        parentTopicReviewsWithAnalyses: ParentTopicReviewsWithAnalysesDto[];
    }): void {
        const reviewsMap: { [restaurantId: string]: ParentTopicReviewsWithAnalysesDto['reviewsWithAnalyses'] } = {};
        parentTopicReviewsWithAnalyses.forEach((item) => {
            reviewsMap[item.restaurantId] = item.reviewsWithAnalyses;
        });

        const restaurantsWithMentions: RestaurantWithMentions[] = restaurants
            .map((restaurant) => {
                const reviewsForRestaurant = reviewsMap[restaurant.id] || [];

                let negativeMentionsCount = 0;
                let positiveMentionsCount = 0;

                reviewsForRestaurant.forEach((review) => {
                    const hasNegativeSegment = review.segmentAnalyses.some(
                        (analysis) => analysis.sentiment === ReviewAnalysisSentiment.NEGATIVE
                    );
                    const hasPositiveSegment = review.segmentAnalyses.some(
                        (analysis) => analysis.sentiment === ReviewAnalysisSentiment.POSITIVE
                    );

                    if (hasNegativeSegment) {
                        negativeMentionsCount++;
                    }
                    if (hasPositiveSegment) {
                        positiveMentionsCount++;
                    }
                });

                return new RestaurantWithMentions({
                    lightRestaurant: restaurant,
                    negativeMentionsCount,
                    positiveMentionsCount,
                });
            })
            .sort((a, b) => b.negativeMentionsCount - a.negativeMentionsCount);

        const totalNegativeMentions = restaurantsWithMentions.reduce((acc, restaurant) => acc + restaurant.negativeMentionsCount, 0);
        const totalPositiveMentions = restaurantsWithMentions.reduce((acc, restaurant) => acc + restaurant.positiveMentionsCount, 0);

        this._topics.set(topicName, {
            restaurants: restaurantsWithMentions,
            score,
            reviewsWithAnalyses: reviewsMap,
            allRestaurantsNegativeMentionsCount: totalNegativeMentions,
            allRestaurantsPositiveMentionsCount: totalPositiveMentions,
        });
    }

    getRestaurantNegativeReviewsByTopic(restaurantId: string, topicName: string): ParentTopicReviewsWithAnalyses[] {
        const topicData = this._topics.get(topicName);
        if (!topicData) {
            return [];
        }
        return topicData.reviewsWithAnalyses[restaurantId] || [];
    }

    getTopic(topicName: string): ParentTopicMentionData | undefined {
        return this._topics.get(topicName);
    }

    getAllTopics(): Map<string, ParentTopicMentionData> {
        return this._topics;
    }

    clear(): void {
        this._topics.clear();
    }
}
