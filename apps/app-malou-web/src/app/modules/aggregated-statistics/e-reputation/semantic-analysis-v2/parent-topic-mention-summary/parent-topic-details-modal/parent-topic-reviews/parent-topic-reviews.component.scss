@use '_malou_variables.scss' as *;
@use '_malou_scrollbar.scss' as *;

.avatar {
    width: 34px;
    height: 34px;
    position: relative;
    left: 8px;
    top: 7px;
    border-radius: 50%;
    object-fit: cover;
}

.logo {
    width: 34px;
    height: 34px;
    position: relative;
    left: -8px;
    top: -27px;
    object-fit: cover;
}
.thumb {
    &-up {
        color: $malou-color-state-success;
    }
    &-down {
        color: $malou-color-chart-pink--accent;
    }
}

.grid-wrapper-menu-review {
    display: grid;
    grid-template-columns: repeat(2, auto);
    grid-template-rows: 1fr;
}

@container (min-width: 500px) {
    .grid-wrapper-menu-review {
        grid-template-columns: repeat(3, auto) !important;
    }
}

@container (min-width: 700px) {
    .grid-wrapper-menu-review {
        grid-template-columns: repeat(4, auto) !important;
    }
}
