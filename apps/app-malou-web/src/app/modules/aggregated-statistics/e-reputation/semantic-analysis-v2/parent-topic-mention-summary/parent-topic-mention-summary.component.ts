import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    input,
    OnInit,
    Signal,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort, SortDirection } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { isEqual, isEqualWith, partition } from 'lodash';
import { combineLatest, distinctUntilChanged, filter, forkJoin, map, Observable, switchMap } from 'rxjs';

import { ParentTopicMentionsSummaryDto } from '@malou-io/package-dto';
import {
    HeapEventName,
    isSameDay,
    MalouComparisonPeriod,
    PlatformFilterPage,
    PlatformKey,
    ReviewAnalysisSentiment,
    TooltipPosition,
} from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { SegmentAnalysisParentTopicsService } from ':core/services/segment-analysis-parent-topics.service';
import { ParentTopicDetailsModalComponent } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-details-modal/parent-topic-details-modal.component';
import { ParentTopicMentionSummaryContext } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.context';
import {
    ParentTopicDetailsModalData,
    ParentTopicScore,
} from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.model';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { SearchComponent } from ':shared/components/search/search.component';
import { FilterOption, SortByFiltersComponent } from ':shared/components/sort-by-filters/sort-by-filters.component';
import { CustomTooltipDirective } from ':shared/directives/custom-tooltip-directive';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { LightRestaurant, Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

enum TableColumns {
    EVOLUTION = 'evolution',
    TOPIC_NAME = 'topicName',
    MENTIONS = 'mentions',
    RESTAURANTS_COUNT = 'restaurantsCount',
    SCORE = 'score',
    ANALYZE = 'analyze',
}

interface ParentTopicMentionSummaryTableItem {
    topicName: string;
    positiveMentions: number;
    negativeMentions: number;
    negativeMentionsEvolution: number | null;
    positiveMentionsEvolution: number | null;
    positiveEvolutionIcon: SvgIcon | null;
    negativeEvolutionIcon: SvgIcon | null;
    restaurantsCount: number;
    score: ParentTopicScore;
    restaurantIds: string[];
}

@Component({
    selector: 'app-parent-topic-mention-summary',
    imports: [
        MatTableModule,
        NgTemplateOutlet,
        MatIconModule,
        MatSortModule,
        MatTooltipModule,
        TranslateModule,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        MatButtonModule,
        NgClass,
        ApplyPurePipe,
        SortByFiltersComponent,
        SearchComponent,
        CustomTooltipDirective,
    ],
    templateUrl: './parent-topic-mention-summary.component.html',
    styleUrl: './parent-topic-mention-summary.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentTopicMentionSummaryComponent implements OnInit {
    readonly isPdfDownload = input<boolean>(false);

    private readonly _store = inject(Store);
    private readonly _translate = inject(TranslateService);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);
    private readonly _segmentAnalysisParentTopicsService = inject(SegmentAnalysisParentTopicsService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _parentTopicMentionSummaryContext = inject(ParentTopicMentionSummaryContext);
    private readonly _heapService = inject(HeapService);

    readonly TableColumns = TableColumns;
    readonly ReviewAnalysisSentiment = ReviewAnalysisSentiment;
    readonly ParentTopicScore = ParentTopicScore;
    readonly SvgIcon = SvgIcon;
    readonly TooltipPosition = TooltipPosition;
    readonly SORT_OPTIONS: FilterOption[] = [
        {
            key: TableColumns.EVOLUTION,
            label: this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.evolution'),
        },
        {
            key: TableColumns.TOPIC_NAME,
            label: this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.topics'),
        },
        {
            key: TableColumns.MENTIONS,
            label: this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.mentions'),
        },
        {
            key: TableColumns.RESTAURANTS_COUNT,
            label: this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.locations'),
        },
        {
            key: TableColumns.SCORE,
            label: this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.table_headers.score'),
        },
    ];

    readonly dates$: Observable<{ startDate: Date | null; endDate: Date | null }> = this._store
        .select(AggregatedStatisticsSelectors.selectDatesFilter)
        .pipe(
            distinctUntilChanged((prev, curr) => {
                const isSameStartDate = !!prev.startDate && !!curr.startDate && isSameDay(prev.startDate, curr.startDate);
                const isSameEndDate = !!prev.endDate && !!curr.endDate && isSameDay(prev.endDate, curr.endDate);
                return isSameStartDate && isSameEndDate;
            })
        );
    readonly platformKeys$: Observable<PlatformKey[]> = this._store
        .select(AggregatedStatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.E_REPUTATION }))
        .pipe(
            distinctUntilChanged((prev, curr) => prev.length === curr.length && isEqual(prev, curr)),
            map((platforms) => {
                if (platforms?.length) {
                    return platforms;
                }
                return Object.values(PlatformKey);
            })
        );

    readonly selectedRestaurants$: Observable<Restaurant[]> = this._aggregatedStatisticsFiltersContext.selectedRestaurants$.pipe(
        distinctUntilChanged((prev, curr) => prev.length === curr.length && isEqualWith(prev, curr, (c, d) => c.id === d.id))
    );

    readonly restaurants: Signal<LightRestaurant[]> = computed(() =>
        this._restaurantsService.restaurants().map((r) => LightRestaurant.fromRestaurant(r))
    );
    readonly period: WritableSignal<{ startDate: Date | null; endDate: Date | null }> = signal({ startDate: null, endDate: null });
    readonly platformKeys: WritableSignal<PlatformKey[]> = signal([]);

    readonly parentTopicScoreLabelMap: Record<ParentTopicScore, string> = {
        [ParentTopicScore.URGENT]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.urgent'
        ),
        [ParentTopicScore.WORRYING]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.worrying'
        ),
        [ParentTopicScore.STABLE]: this._translate.instant(
            'aggregated_statistics.e_reputation.parent_topic_mention_summary.score_labels.stable'
        ),
    };

    readonly sort: WritableSignal<Sort> = signal({ active: TableColumns.MENTIONS, direction: ChartSortBy.DESC });
    readonly sortOrder: WritableSignal<SortDirection> = signal(ChartSortBy.DESC);
    readonly sortKey: WritableSignal<string> = signal('');
    readonly displayedColumns: WritableSignal<TableColumns[]> = signal(Object.values(TableColumns));
    readonly dataSource: WritableSignal<MatTableDataSource<ParentTopicMentionSummaryTableItem>> = signal(
        new MatTableDataSource<ParentTopicMentionSummaryTableItem>([])
    );

    // Store max total mentions for bar width calculations
    private readonly _maxTotalMentions: WritableSignal<number> = signal(0);
    // Store MatSort instance to prevent reinitialization on change detection
    private _matSortInstance: MatSort | null = null;
    // Flag to apply initial sort only once
    private _isInitialSort = true;

    @ViewChild(MatSort, { static: false }) set matSort(sort: MatSort) {
        // Only configure sort if it's a new instance to avoid retriggering on tooltip hover
        if (this.dataSource() && sort && sort !== this._matSortInstance) {
            this._matSortInstance = sort;

            this.dataSource().sortingDataAccessor = (item, property): string | number => {
                switch (property) {
                    case TableColumns.TOPIC_NAME:
                        return item.topicName ?? '';
                    case TableColumns.MENTIONS:
                        return item.negativeMentions;
                    case TableColumns.EVOLUTION:
                        return item.negativeMentionsEvolution ?? 0;
                    default:
                        return item[property] ?? 0;
                }
            };
            this.dataSource().sort = sort;

            // Apply initial sort only once to prevent retriggering
            if (this._isInitialSort) {
                this._isInitialSort = false;
                this.dataSource().sort?.sort({
                    id: this.dataSource().sort?.active || TableColumns.MENTIONS,
                    start: this.sortOrder(),
                    disableClear: false,
                });
            }
        }
    }

    // TrackBy function to prevent unnecessary row re-renders
    trackByTopicName(_index: number, item: ParentTopicMentionSummaryTableItem): string {
        return item.topicName;
    }

    ngOnInit(): void {
        combineLatest([this.dates$, this.platformKeys$, this.selectedRestaurants$])
            .pipe(
                filter(
                    ([dates, platforms, restaurants]) =>
                        !!dates.startDate && !!dates.endDate && platforms.length > 0 && !!restaurants.length
                ),
                switchMap(([dates, platformKeys, restaurants]) => {
                    const [businessRestaurant, _nonBusinessRestaurants] = partition(restaurants, (r) => !r.isBrandBusiness());
                    const restaurantIds = businessRestaurant.map((r) => r.id);
                    const query = {
                        restaurantIds,
                        startDate: dates.startDate!,
                        endDate: dates.endDate!,
                        platformKeys,
                    };
                    this.period.set({ startDate: dates.startDate, endDate: dates.endDate });
                    this.platformKeys.set(platformKeys);
                    return forkJoin([
                        this._segmentAnalysisParentTopicsService.getAggregatedMentionsSummary(query),
                        this._segmentAnalysisParentTopicsService.getAggregatedMentionsSummary({
                            ...query,
                            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                        }),
                    ]);
                })
            )
            .subscribe({
                next: ([currentData, previousData]) => {
                    // Calculate max total mentions once for all bar width calculations
                    this._maxTotalMentions.set(
                        Math.max(
                            ...currentData.map((dto) => dto.positiveMentions + dto.negativeMentions),
                            1 // Minimum 1 to avoid division by zero
                        )
                    );

                    this.dataSource.set(this._buildTableDataSource(currentData, previousData));
                    this._parentTopicMentionSummaryContext.clearParentTopicReviewsByTopic();
                },
            });
    }

    openParentTopicDetailsModal(topic: ParentTopicMentionSummaryTableItem): void {
        const concernedRestaurants = this.restaurants().filter((restaurant) => topic.restaurantIds.includes(restaurant.id));

        const data: ParentTopicDetailsModalData = {
            topicName: topic.topicName,
            restaurants: concernedRestaurants,
            negativeMentionsCount: topic.negativeMentions,
            positiveMentionsCount: topic.positiveMentions,
            score: topic.score,
            period: {
                startDate: this.period().startDate!,
                endDate: this.period().endDate!,
            },
            platformKeys: this.platformKeys(),
        };

        this._heapService.track(HeapEventName.TRACKING_SEMANTIC_ANALYSIS_OPEN_PARENT_TOPIC_DETAILS, {
            topicName: topic.topicName,
            restaurantsCount: topic.restaurantsCount,
        });

        this._customDialogService.open(ParentTopicDetailsModalComponent, {
            width: '80vw',
            height: '80vh',
            disableClose: true,
            data,
        });
    }

    onSearchValueChange(searchValue: string): void {
        this.dataSource().filter = searchValue.trim().toLowerCase();
    }

    onSortOrderChange(): void {
        this.sortOrder.set(this.dataSource().sort?.direction === ChartSortBy.ASC ? ChartSortBy.DESC : ChartSortBy.ASC);
        this.dataSource().sort?.sort({
            id: this.dataSource().sort?.active || this.sortKey(),
            start: this.sortOrder(),
            disableClear: false,
        });
    }

    onSortByChange(sortBy: string): void {
        this.sortKey.set(sortBy);
        this.dataSource().sort?.sort({ id: sortBy, start: this.sortOrder(), disableClear: false });
    }

    getTopicNegativeMentionsEvolutionTooltip = (topic: ParentTopicMentionSummaryTableItem): string => {
        const negativeMentionsEvolution = topic.negativeMentionsEvolution;
        if (negativeMentionsEvolution === null || negativeMentionsEvolution === 0) {
            return '';
        }
        const sign = negativeMentionsEvolution > 0 ? '+' : '';
        const stringifiedEvolution = `${sign}${negativeMentionsEvolution}`;
        return this._translate.instant('aggregated_statistics.e_reputation.parent_topic_mention_summary.tooltips.evolution', {
            value: stringifiedEvolution,
        });
    };

    private _buildTableDataSource(
        currentData: ParentTopicMentionsSummaryDto[],
        previousData: ParentTopicMentionsSummaryDto[]
    ): MatTableDataSource<ParentTopicMentionSummaryTableItem> {
        const tableItems = currentData
            .map((topicMentionsSummary) => {
                const topicPreviousMentionsSummary = previousData.find((item) => item.topicName === topicMentionsSummary.topicName);
                return this._mapDtoToTableItem(topicMentionsSummary, topicPreviousMentionsSummary, currentData);
            })
            .filter((item) => item.positiveMentions + item.negativeMentions > 0);

        const newDataSource = new MatTableDataSource(tableItems);

        // Preserve existing sort configuration to maintain sort state across data updates
        const existingDataSource = this.dataSource();
        if (existingDataSource?.sort) {
            newDataSource.sort = existingDataSource.sort;
            newDataSource.sortingDataAccessor = existingDataSource.sortingDataAccessor;
        }

        return newDataSource;
    }

    private _mapDtoToTableItem(
        topicMentionsSummary: ParentTopicMentionsSummaryDto,
        topicPreviousMentionsSummary: ParentTopicMentionsSummaryDto | undefined,
        allData: ParentTopicMentionsSummaryDto[]
    ): ParentTopicMentionSummaryTableItem {
        const score = this._calculateScore(topicMentionsSummary, allData);
        const { positiveMentionsEvolution, negativeMentionsEvolution } = this._computeMentionsEvolution(
            topicMentionsSummary,
            topicPreviousMentionsSummary
        );

        return {
            topicName: topicMentionsSummary.topicName,
            positiveMentions: topicMentionsSummary.positiveMentions,
            negativeMentions: topicMentionsSummary.negativeMentions,
            positiveMentionsEvolution,
            negativeMentionsEvolution,
            positiveEvolutionIcon: this._getEvolutionIcon(positiveMentionsEvolution),
            negativeEvolutionIcon: this._getEvolutionIcon(negativeMentionsEvolution),
            restaurantsCount: topicMentionsSummary.restaurantIds.length,
            restaurantIds: topicMentionsSummary.restaurantIds,
            score,
        };
    }

    private _getEvolutionIcon = (evolution: number | null): SvgIcon | null => {
        if (evolution === null || evolution === 0) {
            return null;
        }
        return evolution > 0 ? SvgIcon.STAT_ARROW_UP : SvgIcon.STAT_ARROW_DOWN;
    };

    private _computeMentionsEvolution(
        topicMentionsSummary: ParentTopicMentionsSummaryDto,
        topicPreviousMentionsSummary: ParentTopicMentionsSummaryDto | undefined
    ): { positiveMentionsEvolution: number | null; negativeMentionsEvolution: number | null } {
        if (!topicPreviousMentionsSummary) {
            return { positiveMentionsEvolution: null, negativeMentionsEvolution: null };
        }

        return {
            positiveMentionsEvolution: topicMentionsSummary.positiveMentions - topicPreviousMentionsSummary.positiveMentions,
            negativeMentionsEvolution: topicMentionsSummary.negativeMentions - topicPreviousMentionsSummary.negativeMentions,
        };
    }

    calculateBarWidth = (negativeMentions: number, positiveMentions: number, type: ReviewAnalysisSentiment): number => {
        const minWidth = this._maxTotalMentions() > 0 ? 10 : 1; // Minimum width to ensure visibility

        // If no mentions, return 0
        if ((negativeMentions === 0 && positiveMentions === 0) || this._maxTotalMentions() === 0) {
            return 0;
        }

        const value = type === ReviewAnalysisSentiment.NEGATIVE ? negativeMentions : positiveMentions;

        if (value === 0) {
            return 0;
        }

        // Negative mentions count triple to draw attention
        const NEGATIVE_WEIGHT = 3;
        const POSITIVE_WEIGHT = 1;

        // Calculate weighted value
        const weightedValue = type === ReviewAnalysisSentiment.NEGATIVE ? value * NEGATIVE_WEIGHT : value * POSITIVE_WEIGHT;

        // Calculate weighted max (applying negative weight to max)
        const weightedMax = this._maxTotalMentions() * NEGATIVE_WEIGHT;

        // Calculate percentage relative to weighted max
        const percentageOfMax = (weightedValue / weightedMax) * 100;

        // Limit to 100% to not exceed half of the column
        return Math.min(percentageOfMax, 100) + minWidth;
    };

    // Calculate score based on negative mentions and restaurant count
    private _calculateScore(item: ParentTopicMentionsSummaryDto, allData: ParentTopicMentionsSummaryDto[]): ParentTopicScore {
        if (allData.length === 0) {
            return ParentTopicScore.STABLE;
        }

        // Find max values for normalization
        const maxRestaurantsCount = Math.max(...allData.map((d) => d.restaurantIds.length));
        const maxNegativeMentions = Math.max(...allData.map((d) => d.negativeMentions));

        // Avoid division by zero
        if (maxRestaurantsCount === 0 && maxNegativeMentions === 0) {
            return ParentTopicScore.STABLE;
        }

        // Normalize each factor to a 0-1 scale
        const restaurantsScore = maxRestaurantsCount > 0 ? item.restaurantIds.length / maxRestaurantsCount : 0;
        const negativeMentionsScore = maxNegativeMentions > 0 ? item.negativeMentions / maxNegativeMentions : 0;

        // Combine with 50/50 weighting and convert to 0-100 scale
        const combinedScore = (restaurantsScore * 0.5 + negativeMentionsScore * 0.5) * 100;

        // Categorize score into enum values
        if (combinedScore >= 70) {
            return ParentTopicScore.URGENT;
        } else if (combinedScore >= 40) {
            return ParentTopicScore.WORRYING;
        } else {
            return ParentTopicScore.STABLE;
        }
    }
}
