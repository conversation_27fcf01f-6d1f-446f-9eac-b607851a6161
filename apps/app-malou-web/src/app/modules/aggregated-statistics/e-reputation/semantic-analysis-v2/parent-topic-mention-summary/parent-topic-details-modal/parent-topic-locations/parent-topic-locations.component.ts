import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { ReviewAnalysisSentiment } from '@malou-io/package-utils';

import { RestaurantWithMentions } from ':modules/aggregated-statistics/e-reputation/semantic-analysis-v2/parent-topic-mention-summary/parent-topic-mention-summary.model';
import { ApplyPurePipe, ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-parent-topic-locations',
    imports: [NgTemplateOutlet, ApplySelfPurePipe, ImagePathResolverPipe, ApplyPurePipe, TranslateModule],
    templateUrl: './parent-topic-locations.component.html',
    styleUrl: './parent-topic-locations.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentTopicLocationsComponent {
    readonly locationsData = input.required<RestaurantWithMentions[]>();
    readonly selectedLocationId = input.required<string>();
    readonly allRestaurantsNegativeMentionsCount = input.required<number>();
    readonly allRestaurantsPositiveMentionsCount = input.required<number>();
    readonly selectLocationChange = output<string>();

    readonly ReviewAnalysisSentiment = ReviewAnalysisSentiment;

    readonly isLocationSelected = computed(
        () =>
            (location: RestaurantWithMentions): boolean =>
                location.id === this.selectedLocationId()
    );

    selectLocation(location: RestaurantWithMentions): void {
        this.selectLocationChange.emit(location.id);
    }

    getLocationLogoUrl = (location: RestaurantWithMentions): string => location.getLogoUrl();

    calculateBarWidth = (mentionsCount: number, type: ReviewAnalysisSentiment): number => {
        const minWidth = 1;

        // If no mentions, return 0
        if (mentionsCount === 0) {
            return 0;
        }

        // Negative mentions count triple to draw attention
        const NEGATIVE_WEIGHT = 3;
        const POSITIVE_WEIGHT = 1;

        // Calculate weighted value
        const weightedValue = type === ReviewAnalysisSentiment.NEGATIVE ? mentionsCount * NEGATIVE_WEIGHT : mentionsCount * POSITIVE_WEIGHT;

        // Calculate weighted max (applying negative weight to max)
        const weightedMax = NEGATIVE_WEIGHT;

        // Calculate percentage relative to weighted max
        const percentageOfMax = (weightedValue / weightedMax) * 100;

        // Limit to 100% to not exceed half of the column
        return Math.min(percentageOfMax, 100) + minWidth;
    };
}
