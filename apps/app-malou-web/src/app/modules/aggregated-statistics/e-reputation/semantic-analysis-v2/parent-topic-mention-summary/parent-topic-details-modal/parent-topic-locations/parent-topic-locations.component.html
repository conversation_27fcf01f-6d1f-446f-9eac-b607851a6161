<div class="flex h-full flex-col">
    <ng-container [ngTemplateOutlet]="allLocationsTemplate"></ng-container>

    <div class="overflow-y-auto">
        @for (locationData of locationsData(); track locationData.id) {
            @let logoUrl = (getLocationLogoUrl | applyPure: locationData) || ('default_logo' | imagePathResolver);

            <div
                class="border-b-malou-color-border-1 flex cursor-pointer gap-2 border-b-2 p-4"
                [class.bg-malou-color-background-dark]="isLocationSelected()(locationData)"
                (click)="selectLocation(locationData)">
                <div class="grid-rows-auto grid grid-cols-5 gap-4">
                    <div class="flex">
                        <img class="h-12 w-12 rounded-md" [src]="logoUrl" />
                    </div>
                    <div class="col-span-4">
                        <div class="flex flex-col gap-1">
                            <span class="malou-text-14--semibold malou-color-text-1">
                                {{ locationData | applySelfPure: 'getDisplayName' }}
                            </span>
                            <span class="malou-text-10--medium malou-color-text-2">
                                {{ locationData | applySelfPure: 'getFullFormattedAddress' }}
                            </span>
                        </div>
                    </div>
                    <div class="row-start-2"></div>
                    <div class="col-span-4 row-start-2">
                        <ng-container [ngTemplateOutlet]="customGaugeTemplate" [ngTemplateOutletContext]="{ locationData }"> </ng-container>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<ng-template #allLocationsTemplate>
    <div class="border-b-malou-color-border-1 flex w-full border-b-2 p-4">
        <div class="grid grid-cols-5 grid-rows-1 gap-1">
            <div class="flex h-12 w-12 rounded-md" style="background-color: aqua"></div>
            <div class="col-span-4">
                <div class="flex flex-col gap-2">
                    <span class="malou-text-14--semibold malou-color-text-1">
                        {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.locations.all_locations' | translate }}
                    </span>
                    <span class="malou-text-10--medium malou-color-text-2">
                        {{
                            'aggregated_statistics.e_reputation.parent_topic_mention_summary.locations.locations_mentions'
                                | translate
                                    : {
                                          negativeCount: allRestaurantsNegativeMentionsCount(),
                                          positiveCount: allRestaurantsPositiveMentionsCount(),
                                      }
                        }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template let-locationData="locationData" #customGaugeTemplate>
    <div class="mentions-gauge-container">
        <div class="mentions-gauge">
            <div class="negative-section">
                <div
                    class="negative-bar bg-malou-color-chart-pink--accent"
                    [style.width.%]="calculateBarWidth | applyPure: locationData.negativeMentionsCount : ReviewAnalysisSentiment.NEGATIVE">
                    @if (locationData.negativeMentionsCount > 0) {
                        <span class="malou-text-10--medium whitespace-nowrap text-white">{{ locationData.negativeMentionsCount }}</span>
                    }
                </div>
            </div>

            <div class="separator"></div>

            <div class="positive-section">
                <div
                    class="positive-bar bg-malou-color-background-success-dark"
                    [style.width.%]="calculateBarWidth | applyPure: locationData.positiveMentionsCount : ReviewAnalysisSentiment.POSITIVE">
                    @if (locationData.positiveMentionsCount > 0) {
                        <span class="malou-text-10--medium whitespace-nowrap text-white">{{ locationData.positiveMentionsCount }}</span>
                    }
                </div>
            </div>
        </div>
    </div>
</ng-template>
