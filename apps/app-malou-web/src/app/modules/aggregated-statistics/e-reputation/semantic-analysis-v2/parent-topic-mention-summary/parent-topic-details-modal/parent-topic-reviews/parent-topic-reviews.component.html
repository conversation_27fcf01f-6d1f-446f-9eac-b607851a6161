<div class="malou-dialog h-full bg-malou-color-background-light">
    <div class="malou-dialog__header">
        <div class="malou-text-16--semibold">
            {{ 'aggregated_statistics.e_reputation.parent_topic_mention_summary.reviews.concerned_reviews' | translate }}
        </div>

        <app-select
            [values]="reviewSentimentFilterByOptions"
            [displayWith]="reviewSentimentFilterByDisplayFn"
            [formControl]="reviewSentimentFilterByControl"
            (selectChange)="onReviewSentimentFilterByChange($event)">
        </app-select>
    </div>

    <div class="malou-dialog__body mb-4">
        <div class="mr-2 mt-2 flex flex-col gap-y-4">
            @for (review of filteredReviewsWithAnalyses(); track $index) {
                <div>
                    <ng-container [ngTemplateOutlet]="reviewTemplate" [ngTemplateOutletContext]="{ review }"></ng-container>
                </div>
            }
        </div>
    </div>
</div>

<ng-template let-review="review" #reviewTemplate>
    <div class="flex flex-col rounded-[10px] border !bg-white px-6 py-4">
        @let reviewerDisplayName = review.reviewer?.displayName ?? ('reviews.anonymous' | translate);
        <div class="malou-card__header flex flex-col !pb-0 pl-0">
            <div class="flex w-full">
                <div class="malou-card__image-container">
                    <img class="avatar" alt="avatar" [lazyLoad]="reviewerDisplayName | avatar" />
                    <app-platform-logo
                        imgClasses="w-8.5 h-8.5 relative left-[-8px] top-[-27px] object-cover !rounded-full"
                        [logo]="review.key"
                        [withLazyLoading]="true"></app-platform-logo>
                </div>
                <div class="flex w-[30%] grow cursor-pointer flex-col justify-center">
                    <div class="malou-text-12--bold truncate">{{ reviewerDisplayName }}</div>
                    <div class="malou-text__card-subtitle">{{ (review.socialCreatedAt | DateToStringPipe) || '-' }}</div>
                </div>

                <div class="malou-card__action-button">
                    <app-star-gauge class="px-4" [stars]="review.rating"></app-star-gauge>
                </div>
            </div>
        </div>
        <div class="malou-card__body-text mt-2">
            <div class="font-normal" [innerHTML]="review | applySelfPure: 'getHighlightedText'"></div>

            <ng-container [ngTemplateOutlet]="reviewMenuItemsTemplate" [ngTemplateOutletContext]="{ review }"></ng-container>
        </div>
    </div>
</ng-template>

<ng-template let-review="review" #reviewMenuItemsTemplate>
    @if (review | getMenuItemReviews; as menuItems) {
        <div class="mb-2">
            <div>
                <div class="grid-wrapper-menu-review gap-1.5">
                    @for (
                        itemReview of menuItems | displayMenuItems: { isFold: false, maxMenuItemShown: MAX_MENU_ITEM_SHOWN };
                        track itemReview
                    ) {
                        <div class="mt-1">
                            <div class="flex items-end">
                                <mat-icon
                                    class="smaller-icon mr-2 !h-[16px] !w-[16px]"
                                    svgIcon="{{ itemReview.rating ? 'thumb-up' : 'thumb-down' }}"
                                    [ngClass]="itemReview.rating ? 'thumb-up' : 'thumb-down'">
                                </mat-icon>
                                <span class="malou-text-10--regular flex">
                                    {{ itemReview.name }}
                                </span>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</ng-template>
