import { container, singleton } from 'tsyringe';

import { UpsertCommentsForPlatformUseCase } from ':modules/comments/use-cases/upsert-comments-for-platform/upsert-comments-for-platform.use-case';

@singleton()
export class SyncPlatformCommentsTask {
    constructor(private readonly _upsertCommentsForPlatformUseCase: UpsertCommentsForPlatformUseCase) {}

    async execute() {
        console.log('Starting SyncPlatformCommentsTask');

        const platformId = '65718a41114dc93beb059481'; // Replace with the actual platform ID
        const restaurantId = '65718a41114dc93beb059458'; // Replace with the actual restaurant ID

        await this._upsertCommentsForPlatformUseCase.execute(platformId, restaurantId);

        console.log('SyncPlatformCommentsTask ended');
    }
}

const task = container.resolve(SyncPlatformCommentsTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
