import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable, container } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';

import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import ':plugins/db';

// One time only task to fetch keyword analysis for all reviews impacted by this bug
// https://airtable.com/appIqBldyX7wZlWnp/tblbOxMTpexQyxSTV/viwVSdtBlz857nQiA/reci8BH9Metu5QwY1

type PartialReview = Pick<
    IReview,
    | '_id'
    | 'comments'
    | 'text'
    | 'restaurantId'
    | 'aiRelatedBricksCount'
    | 'rating'
    | 'lang'
    | 'socialCreatedAt'
    | 'reviewer'
    | 'reviewerNameValidation'
>;

@autoInjectable()
class RefetchKeywordAnalysis {
    private readonly _REVIEW_CHUNK_SIZE = 100;

    constructor(
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService,
        private readonly _reviewsRepository: ReviewsRepository
    ) {}

    async execute() {
        const reviews = await this._getImpactedReviews();
        console.log(`Will update ${reviews.length} reviews`);
        const reviewChunks = chunk(reviews, this._REVIEW_CHUNK_SIZE);
        for (const reviewChunk of reviewChunks) {
            try {
                await Promise.all(reviewChunk.map((review) => this._getKeywordAnalysisForComment(review)));
                console.log(`Finished ${reviewChunk.length} reviews...`);
            } catch (err) {
                console.log(`Error for reviews ${reviewChunk.map((r) => r._id).join(', ')}`);
            }
        }
        const reviewsAfter = await this._getImpactedReviews();
        console.log(`Remaining reviews to update: ${reviewsAfter.length} - ${reviewsAfter.map((r) => r._id).join(', ')}`);
        return;
    }

    private async _getKeywordAnalysisForComment(review: PartialReview): Promise<IReview | void> {
        if (!review?.comments?.length) {
            return;
        }
        if (review.comments.every((comment) => !!comment.keywordAnalysis?.score)) {
            return;
        }
        const updatedComments = await Promise.all(
            review?.comments.map(async (comment) => {
                if (!comment.text) {
                    return comment;
                }
                const keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                    reviewId: review._id.toString(),
                    restaurantId: toDbId(review.restaurantId),
                    rating: review.rating ?? 0,
                    text: comment.text,
                    reviewLang: review.lang ?? 'fr',
                    reviewSocialCreatedAt: review.socialCreatedAt,
                    reviewerName: review.reviewer?.displayName ?? '',
                    commentSocialUpdatedAt: comment.socialUpdatedAt,
                    reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
                });
                return {
                    ...comment,
                    keywordAnalysis,
                };
            })
        );
        return this._reviewsRepository.findOneAndUpdate({
            filter: { _id: review._id },
            update: {
                comments: updatedComments.filter((c) => !!c.text),
            },
        }) as Promise<IReview>;
    }

    private async _getImpactedReviews(): Promise<PartialReview[]> {
        const releaseDate = DateTime.fromJSDate(new Date('2025-06-01')).startOf('day').toJSDate();
        const impactedReviews = await this._reviewsRepository.find({
            filter: {
                comments: {
                    $elemMatch: {
                        socialUpdatedAt: { $gte: releaseDate },
                        text: { $exists: true, $ne: '' },
                        'keywordAnalysis.score': 0,
                    },
                },
            },
            projection: {
                _id: 1,
                comments: 1,
                text: 1,
                restaurantId: 1,
                aiRelatedBricksCount: 1,
                rating: 1,
                lang: 1,
                socialCreatedAt: 1,
                reviewer: 1,
                reviewerNameValidation: 1,
            },
            options: {
                lean: true,
            },
        });
        return impactedReviews;
    }
}
const task = container.resolve(RefetchKeywordAnalysis);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
