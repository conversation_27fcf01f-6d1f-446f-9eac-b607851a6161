import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { autoInjectable, container } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';

import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import ':plugins/db';

@autoInjectable()
class FetchKeywordAnalysis {
    private readonly _MAX_REVIEWS_LIMIT_PER_RESTAURANT = 200;
    private readonly _REVIEW_CHUNK_SIZE = 100;

    constructor(
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    // Task can make up to 2000 restaurants x 200 reviews = 400k requests to the lambda, we don't mind if it is slow (8000 chunks of 50 reviews)
    async execute() {
        const restaurants = await this._restaurantsRepository.find({ filter: { active: true }, projection: { _id: 1, name: 1 } });
        console.log(`Will update reviews for ${restaurants.length} restaurants`);
        let index = 0;
        for (const restaurant of restaurants) {
            console.log('Starting restaurant', index, ' - ', restaurant.name);
            const reviews = await this._reviewsRepository.find({
                filter: {
                    restaurantId: restaurant._id,
                    comments: {
                        $elemMatch: {
                            keywordAnalysis: { $exists: false },
                            text: { $exists: true, $ne: '' },
                        },
                    },
                    $expr: { $gte: [{ $size: '$comments' }, 1] },
                },
                options: {
                    lean: true,
                    sort: { socialCreatedAt: -1 },
                    limit: this._MAX_REVIEWS_LIMIT_PER_RESTAURANT,
                },
            });
            const reviewChunks = chunk(reviews, this._REVIEW_CHUNK_SIZE);
            for (const reviewChunk of reviewChunks) {
                try {
                    await Promise.all(reviewChunk.map((r) => this._getKeywordAnalysisForComment(r, restaurant._id.toString())));
                    console.log(`Finished ${reviewChunk.length} reviews...`);
                } catch (err) {
                    console.log(`Error for restaurant ${restaurant._id}`);
                }
            }
            index++;
        }
    }
    private async _getKeywordAnalysisForComment(review: IReview, restaurantId: string): Promise<IReview | void> {
        if (!review?.comments?.length) {
            return;
        }
        if (review.comments.every((comment) => comment.keywordAnalysis)) {
            return;
        }
        const updatedComments = await Promise.all(
            review?.comments.map(async (comment) => {
                if (comment.keywordAnalysis || !comment.text) {
                    return comment;
                }
                const keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                    reviewId: review._id.toString(),
                    restaurantId: toDbId(restaurantId),
                    rating: review.rating ?? 0,
                    text: comment.text,
                    reviewLang: review.lang ?? 'fr',
                    reviewSocialCreatedAt: review.socialCreatedAt,
                    reviewerName: review.reviewer?.displayName ?? '',
                    commentSocialUpdatedAt: comment.socialUpdatedAt,
                    reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
                });
                return {
                    ...comment,
                    keywordAnalysis,
                };
            })
        );
        return this._reviewsRepository.findOneAndUpdate({
            filter: { _id: review._id },
            update: {
                comments: updatedComments.filter((c) => !!c.text),
            },
        }) as Promise<IReview>;
    }
}
const task = container.resolve(FetchKeywordAnalysis);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
