import 'reflect-metadata';

import ':env';

import assert from 'node:assert/strict';
import { container, singleton } from 'tsyringe';

import { IYextLocationPopulated, toDbId } from '@malou-io/package-models';
import { YextAddRequestStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { getMalouCountryCode } from ':helpers/utils';
import { YextLocationRepository } from ':modules/publishers/yext/repositories/yext-location.repository';
import ':plugins/db';
import { YextProvider } from ':providers/yext/yext.provider';

@singleton()
class AddYextServicesTask {
    constructor(
        private readonly _yextProvider: YextProvider,
        private readonly _yextLocationRepository: YextLocationRepository
    ) {}

    async execute() {
        const yextAccountId = '667d84015e5f2ba21d03ee41'; // Malou Yext Account ID

        const yextLocations = await this._yextLocationRepository.find({
            filter: {
                yextAccountId: toDbId(yextAccountId),
                addRequestStatus: YextAddRequestStatus.COMPLETE,
            },
            options: { lean: true, populate: [{ path: 'yextAccount' }, { path: 'restaurant' }] },
        });
        const activeYextLocations = yextLocations.filter(({ restaurant }) => restaurant && restaurant.active && restaurant.isYextActivated);

        for (const yextLocation of activeYextLocations) {
            await this._addMissingServicesForLocation(yextLocation);
        }
    }

    private async _addMissingServicesForLocation(yextLocation: IYextLocationPopulated): Promise<void> {
        try {
            assert(yextLocation.restaurant, 'Restaurant not found for Yext Location');
            assert(yextLocation.yextAccount, 'Yext Account not found for Yext Location');

            const {
                response: { count },
            } = await this._yextProvider.getLocationServices(yextLocation);
            if (count > 0) {
                logger.info('Yext services already existing for restaurant, skipping', {
                    restaurantId: yextLocation.restaurantId,
                });
                return;
            }

            const countryCode = yextLocation.restaurant.address?.regionCode?.toLocaleUpperCase();
            assert(countryCode);
            const skus = this._yextProvider.getSkusByCountryCode(getMalouCountryCode(countryCode));
            assert(skus.length > 0, `No Yext SKUs found for country code: ${countryCode}`);

            const data = await this._yextProvider.addServicesForLocation({
                accountId: yextLocation.yextAccount.partnerAccountId,
                locationId: yextLocation.partnerLocationId,
                skus,
            });
            logger.info('Yext services added for restaurant successfully.', { restaurantId: yextLocation.restaurantId, data });
        } catch (error) {
            logger.error('Error while adding Yext services for restaurant', { restaurantId: yextLocation.restaurantId, error });
        }
    }
}

const task = container.resolve(AddYextServicesTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
