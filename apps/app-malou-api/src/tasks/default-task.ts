/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import assert from 'node:assert';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { StoreLocatorJobStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import ReviewReplyAutomationsRepository from ':modules/automations/features/review-replies/review-replies.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class DefaultTask {
    constructor(
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _automationsRepository: ReviewReplyAutomationsRepository
    ) {}

    async execute() {
        const basilicOrganizationId = toDbId('69031c5e7e9a5b253f58c1e8');
        const basilicRestaurants = await this._restaurantsRepository.find({
            filter: { organizationId: basilicOrganizationId },
            projection: { _id: 1 },
            options: { lean: true },
        });
        const montpellierBasilic = basilicRestaurants.find((r) => r._id.toString() === '69086331b91bfc516f3db1d1');
        assert(montpellierBasilic);
        const montpellierReviewAutomations = await this._automationsRepository.find({
            filter: { restaurantId: montpellierBasilic._id, feature: 'REPLY_TO_REVIEW' },
            options: { lean: true },
        });
        for (const otherRestaurant of basilicRestaurants) {
            if (otherRestaurant._id.toString() === montpellierBasilic._id.toString()) {
                continue;
            }
            for (const automation of montpellierReviewAutomations) {
                await this._automationsRepository.upsert({
                    filter: {
                        restaurantId: otherRestaurant._id,
                        feature: automation.feature,
                        ratingCategory: automation.ratingCategory,
                        withComment: automation.withComment,
                        platformKey: automation.platformKey,
                    },
                    update: {
                        restaurantId: otherRestaurant._id,
                        feature: automation.feature,
                        ratingCategory: automation.ratingCategory,
                        withComment: automation.withComment,
                        platformKey: automation.platformKey,
                        active: automation.active,
                        replyMethod: automation.replyMethod,
                        shouldValidateAiBeforeSend: automation.shouldValidateAiBeforeSend,
                        templateIds: automation.templateIds,
                    },
                });
            }
        }
        await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
            configurationId: '',
            status: StoreLocatorJobStatus.PENDING,
        });
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
