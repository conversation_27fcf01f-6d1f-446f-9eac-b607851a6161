import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorPageStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
class UpdateStoreLocatorS3StructureTask {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute(): Promise<void> {
        const storeLocatorPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {
                status: StoreLocatorPageStatus.PUBLISHED,
            },
            options: { lean: true },
        });

        const total = storeLocatorPages.length;
        let count = 0;
        for (const page of storeLocatorPages) {
            const configurationId = page.configurationId.toString();
            const restaurantId = page.restaurantId.toString();
            const lang = page.lang;

            logger.info(`Migrating S3 structure for Store Locator Page ${restaurantId} in lang ${lang} (${++count}/${total})`);

            const pageOldPrefix = `store-locator/configuration/${configurationId}/restaurants/${restaurantId}`;
            const socialNetworksOldPrefix = `store-locator/configuration/${configurationId}/social-networks`;
            const mapOldPrefix = `store-locator/configuration/${configurationId}/map`;

            // Create new folders
            try {
                await Promise.all([
                    this._cloudStorageService.createFolderIfNotExists(
                        `store-locator/configuration/${configurationId}/pages/local/${restaurantId}/${lang}/`
                    ),
                    this._cloudStorageService.createFolderIfNotExists(`store-locator/configuration/${configurationId}/pages/local/shared`),
                    this._cloudStorageService.createFolderIfNotExists(
                        `store-locator/configuration/${configurationId}/pages/centralization`
                    ),
                ]);
            } catch (err) {
                logger.warn('Failed to create empty folders', { err });
                continue;
            }

            // Copy images

            try {
                await Promise.all([
                    this._cloudStorageService.duplicateFolderContents(
                        pageOldPrefix,
                        `store-locator/configuration/${configurationId}/pages/local/${restaurantId}/${lang}/`
                    ),
                    this._cloudStorageService.duplicateFolderContents(
                        socialNetworksOldPrefix,
                        `store-locator/configuration/${configurationId}/pages/local/shared/social-networks/`
                    ),
                    this._cloudStorageService.duplicateFolderContents(
                        mapOldPrefix,
                        `store-locator/configuration/${configurationId}/pages/centralization/`
                    ),
                ]);
            } catch (err) {
                logger.warn('Failed to copy content', { err });
                continue;
            }

            logger.info(`Successfully migrated S3 structure for Store Locator Page ${restaurantId} in lang ${lang}`);
        }
    }
}

const task = container.resolve(UpdateStoreLocatorS3StructureTask);

task.execute()
    .then(() => {
        logger.info('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
