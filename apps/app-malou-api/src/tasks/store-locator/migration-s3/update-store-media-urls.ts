import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { IStoreLocatorMapPage, IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { isNotNil, StoreLocatorLanguage } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorCentralizationPageRepository } from ':modules/store-locator/store-locator-centralization-page.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
class UpdateStoreMediaUrlsTask {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorCentralizationPageRepository: StoreLocatorCentralizationPageRepository
    ) {}

    async execute(): Promise<void> {
        await this._updateRestaurantPages();
        await this._updateCentralizationPages();
    }

    private async _updateRestaurantPages(): Promise<void> {
        const restaurantPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {},
            options: { lean: true },
        });
        for (const page of restaurantPages) {
            const storeLocatorPageId = page._id.toString();
            const restaurantId = page.restaurantId.toString();

            if (!page.configurationId) {
                throw new Error(
                    `Restaurant page ${page._id} has no configurationId, create-store-locator-architecture migration must be run first or failed`
                );
            }

            const { information, gallery, socialNetworks, descriptions, head } = page.blocks;

            const newHeadSection: IStoreLocatorRestaurantPage['blocks']['head'] = {
                ...head,
                facebookImageUrl: this._updateMediaUrl({ url: head.facebookImageUrl, lang: page.lang, restaurantId }),
                twitterImageUrl: this._updateMediaUrl({ url: head.twitterImageUrl, lang: page.lang, restaurantId }),
                snippetImageUrl: this._updateMediaUrl({ url: head.snippetImageUrl, lang: page.lang, restaurantId }),
            };

            const newInformationSection: IStoreLocatorRestaurantPage['blocks']['information'] = {
                ...information,
                image: {
                    ...information.image,
                    url: this._updateMediaUrl({ url: information.image.url, lang: page.lang, restaurantId }),
                },
            };
            logger.info('Updating information image URL from', information.image.url, 'to', newInformationSection.image.url);

            const newGallerySection: IStoreLocatorRestaurantPage['blocks']['gallery'] = {
                ...gallery,
                images: gallery.images.map((img) => {
                    const newUrl = this._updateMediaUrl({ url: img.url, lang: page.lang, restaurantId });
                    logger.info('Updating gallery image URL from', img.url, 'to', newUrl);
                    return {
                        ...img,
                        url: newUrl,
                    };
                }),
            };

            const newDescriptionsSection: IStoreLocatorRestaurantPage['blocks']['descriptions'] = {
                ...descriptions,
                items: descriptions.items.map((desc) => {
                    const newUrl = this._updateMediaUrl({ url: desc.image.url, lang: page.lang, restaurantId });
                    logger.info('Updating description image URL from', desc.image.url, 'to', newUrl);
                    return {
                        ...desc,
                        image: {
                            ...desc.image,
                            url: newUrl,
                        },
                    };
                }),
            };

            const backupSocialNetworks = socialNetworks?.backup?.socialNetworks as unknown as any;

            const instagramPublications = (backupSocialNetworks.instagram?.publications ?? []).map((pub) => {
                const newUrl = this._updateSocialNetworksMediaUrl({ url: pub.imageUrl });
                logger.info('Updating instagram publication image URL from', pub.imageUrl, 'to', newUrl);
                return {
                    ...pub,
                    imageUrl: newUrl,
                };
            });

            const newSocialNetworksBackup = {
                ...socialNetworks.backup,
                socialNetworks: {
                    instagram: {
                        ...backupSocialNetworks.instagram,
                        publications: instagramPublications,
                    },
                },
            };

            const newDoc = {
                ...page,
                blocks: {
                    ...page.blocks,
                    head: newHeadSection,
                    information: newInformationSection,
                    gallery: newGallerySection,
                    descriptions: newDescriptionsSection,
                    socialNetworks: {
                        ...socialNetworks,
                        backup: newSocialNetworksBackup,
                    },
                },
            };

            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: page._id },
                update: newDoc,
            });

            logger.info(`Updated media URLs for restaurant page ${page._id}`);
        }
    }

    private async _updateCentralizationPages(): Promise<void> {
        const centralizationPages = await this._storeLocatorCentralizationPageRepository.find({
            filter: {},
            options: { lean: true },
        });
        for (const page of centralizationPages) {
            if (!page.configurationId) {
                throw new Error(
                    `centralization page ${page._id} has no configurationId, create-store-locator-architecture migration must be run first or failed`
                );
            }
            const { head, map } = page.blocks;

            const newHeadSection: IStoreLocatorMapPage['blocks']['head'] = {
                ...head,
                facebookImageUrl: this._updateMediaUrl({
                    url: head.facebookImageUrl,
                    lang: page.lang,
                    restaurantId: null,
                }),
                twitterImageUrl: this._updateMediaUrl({
                    url: head.twitterImageUrl,
                    lang: page.lang,
                    restaurantId: null,
                }),
                snippetImageUrl: this._updateMediaUrl({
                    url: head.snippetImageUrl,
                    lang: page.lang,
                    restaurantId: null,
                }),
            };

            const newMapSection: IStoreLocatorMapPage['blocks']['map'] = {
                pins: {
                    activePin: {
                        ...map.pins.activePin,
                        url: this._updateMapMediaUrl(map.pins.activePin.url),
                    },
                    inactivePin: {
                        ...map.pins.inactivePin,
                        url: this._updateMapMediaUrl(map.pins.inactivePin.url),
                    },
                },
                popup: {
                    noStoreImage: {
                        ...map.popup.noStoreImage,
                        url: this._updateMapMediaUrl(map.popup.noStoreImage.url),
                    },
                },
            };

            logger.info('Updating map active pin URL from', map.pins.activePin.url, 'to', newMapSection.pins.activePin.url);
            logger.info('Updating map inactive pin URL from', map.pins.inactivePin.url, 'to', newMapSection.pins.inactivePin.url);
            logger.info('Updating map no store image URL from', map.popup.noStoreImage.url, 'to', newMapSection.popup.noStoreImage.url);

            const newDoc = {
                ...page,
                blocks: {
                    ...page.blocks,
                    head: newHeadSection,
                    map: newMapSection,
                },
            };

            await this._storeLocatorCentralizationPageRepository.updateOne({
                filter: { _id: page._id },
                update: newDoc,
            });

            logger.info(`Updated media URLs for centralization page ${page._id}`);
        }
    }

    private _updateMediaUrl({ url, lang, restaurantId }: { url: string; lang: StoreLocatorLanguage; restaurantId: string | null }): string {
        const shouldSkip = url.includes('media');
        if (shouldSkip) {
            return url;
        }
        const isUrlCorrect = url.includes(`/pages/local/${restaurantId}/${lang}/`);
        if (isUrlCorrect) {
            return url;
        }
        return url.replace(
            isNotNil(restaurantId) ? `/restaurants/${restaurantId}/` : /\/restaurants\/[a-fA-F0-9]{24}\//,
            `/pages/local/${restaurantId}/${lang}/`
        );
    }

    private _updateSocialNetworksMediaUrl({ url }: { url: string }): string {
        const isUrlCorrect = url.includes(`/pages/local/shared/`);
        if (isUrlCorrect) {
            return url;
        }
        return url.replace('/social-networks/', `/pages/local/shared/social-networks/`);
    }

    private _updateMapMediaUrl(url: string): string {
        const shouldSkip = url.includes('shared') || url.includes('media');
        if (shouldSkip) {
            return url;
        }
        const isUrlCorrect = url.includes('/pages/centralization/map/');
        if (isUrlCorrect) {
            return url;
        }
        return url.replace('/map/', '/pages/centralization/map/');
    }
}

const task = container.resolve(UpdateStoreMediaUrlsTask);

task.execute()
    .then(() => {
        logger.info('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
