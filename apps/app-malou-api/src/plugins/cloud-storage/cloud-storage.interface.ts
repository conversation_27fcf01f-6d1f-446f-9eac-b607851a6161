import { AppEntity, PictureSizeRecord } from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';

export type UploadMediaParams = {
    localImagePath: string;
    remotePath: string;
    mediaName: string;
    extension: string;
    mimetype?: string;
};

export type GetSignedUrlParams = {
    key: string;
};

export interface CloudStorage {
    downloadAndSaveMedia(remoteObjectKey: string, localfilePath: string, syncWrite?: boolean): Promise<string>;
    uploadMedia(params: UploadMediaParams): Promise<string>;
    getPutObjectSignedUrl(params: GetSignedUrlParams): Promise<string>;
    deleteObject(remoteObjectKey: string): Promise<void>;
    emptyDirectory(dir: string): Promise<void>;
    duplicateObject(format: string, urls: Media['urls'], entityRelated: AppEntity, entityId: string): Promise<PictureSizeRecord<string>>;
    duplicateFolderContents(sourceKey: string, targetKey: string): Promise<void>;
    createFolderIfNotExists(folderPath: string): Promise<void>;
    copyObject(sourceKey: string, targetKey: string): Promise<string>;
}
