import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { Malou<PERSON>rrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import ClientsRepository from ':modules/clients/clients.repository';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ProviderClientsMapper } from ':modules/clients/provider-clients/provider-clients.mapper';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { CreateProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/create-provider-client/create-provider-client.use-case';
import { FetchProviderClientByEmailUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-by-email/fetch-provider-client-by-email.use-case';
import { Fetch<PERSON>roviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/fetch-provider-client.use-case';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';

@singleton()
export class SendGiftWonComoEventUseCase {
    constructor(
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _clientsRepository: ClientsRepository,
        private readonly _providerClientsRepository: ProviderClientsRepository,
        private readonly _fetchProviderClientUseCase: FetchProviderClientUseCase,
        private readonly _fetchProviderClientByEmailUseCase: FetchProviderClientByEmailUseCase,
        private readonly _createProviderClientUseCase: CreateProviderClientUseCase,
        private readonly _providerClientsMapper: ProviderClientsMapper,
        private readonly _comoProviderWrapper: ComoProviderWrapper
    ) {}

    async execute(giftDrawId: string): Promise<void> {
        const giftDraw = await this._giftDrawsRepository.findOneOrFail({
            filter: { _id: giftDrawId },
            options: { lean: true },
        });
        assert(giftDraw.clientId, 'Client id is required');

        const client = await this._clientsRepository.findOneOrFail({
            filter: { _id: giftDraw.clientId },
            options: { lean: true },
        });
        assert(client.email, 'Client email is required');

        // Get Como client
        const source = this._providerClientsMapper.fromPlatformKeyToProviderClientSource(PlatformKey.COMO);
        assert(source, 'Source not found');

        const providerClient = await this._providerClientsRepository.getByEmailAndRestaurantIdAndSource({
            email: client.email,
            restaurantId: giftDraw.restaurantId.toString(),
            source,
        });

        let comoClient: ProviderClient | null = null;

        if (providerClient) {
            comoClient = await this._fetchProviderClientUseCase.execute(
                providerClient.providerClientId,
                giftDraw.restaurantId.toString(),
                PlatformKey.COMO
            );
        }

        // If it doesn't exist, fetch it by email
        if (!comoClient) {
            try {
                comoClient = await this._fetchProviderClientByEmailUseCase.execute(
                    client.email,
                    client.restaurantId.toString(),
                    PlatformKey.COMO
                );
            } catch (error) {
                if (MalouError.isMalouError(error) && error.malouErrorCode === MalouErrorCode.COMO_CLIENT_NOT_FOUND) {
                    comoClient = await this._createProviderClientUseCase.execute(
                        giftDraw.restaurantId.toString(),
                        {
                            email: client.email,
                            firstName: client.firstName,
                            lastName: client.lastName,
                            phone: client.phone,
                        },
                        PlatformKey.COMO
                    );
                } else {
                    throw error;
                }
            }

            if (!comoClient) {
                throw new MalouError(MalouErrorCode.COMO_CLIENT_NOT_FOUND, {
                    message: 'Como client not found',
                    metadata: { giftDrawId, email: client.email, restaurantId: client.restaurantId.toString() },
                });
            }
        }

        // Upsert provider client
        await this._providerClientsRepository.upsertProviderClient(comoClient);

        const doesConsent = comoClient.contactOptions && comoClient.contactOptions.length > 0; // rule is from ProviderClient entity mapping

        if (doesConsent) {
            // Send event
            const comoGiftName = this._mapGiftIdToComoGiftName(giftDraw.giftId.toString());
            assert(comoGiftName, 'Como gift name is required');

            await this._comoProviderWrapper.submitEvent({
                restaurantId: giftDraw.restaurantId.toString(),
                comoMemberId: comoClient.providerClientId,
                eventName: comoGiftName,
                date: giftDraw.createdAt,
            });
        } else {
            // Create coupon code
            const comoCouponCode = this._mapGiftIdToComoCouponCode(giftDraw.giftId.toString());
            assert(comoCouponCode, 'Como coupon code is required');

            await this._comoProviderWrapper.submitEvent({
                restaurantId: giftDraw.restaurantId.toString(),
                comoMemberId: comoClient.providerClientId,
                eventName: comoCouponCode,
                date: giftDraw.createdAt,
            });
        }
    }

    // TODO CRM - real mapping, or maybe the data will be attached to the gift
    private _mapGiftIdToComoGiftName(giftId: string): string | null {
        switch (giftId) {
            case '67fd08ec721bd35f1ae9d6ca':
                return 'wof-dev-boite6OG';
            case '67fd08ec721bd35f1ae9d6c6':
                return 'wof-dev-doughnutauchoix';
            case '67fd08ec721bd35f1ae9d6d6':
                return 'wof-dev-OG';
            case '67fd08ec721bd35f1ae9d6ce':
                return 'wof-dev-boissongourmande';
            case '67fd08ec721bd35f1ae9d6c2':
                return 'wof-dev-espresso';
        }
        return null;
    }

    // TODO CRM - real mapping, or maybe the data will be attached to the gift
    private _mapGiftIdToComoCouponCode(giftId: string): string | null {
        switch (giftId) {
            case '67fd08ec721bd35f1ae9d6ca':
                return 'wof-dev-code-boite6OG';
            case '67fd08ec721bd35f1ae9d6c6':
                return 'wof-dev-code-doughnutauchoix';
            case '67fd08ec721bd35f1ae9d6d6':
                return 'wof-dev-code-OG';
            case '67fd08ec721bd35f1ae9d6ce':
                return 'wof-dev-code-boissongourmande';
            case '67fd08ec721bd35f1ae9d6c2':
                return 'wof-dev-code-espresso';
        }
        return null;
    }
}
