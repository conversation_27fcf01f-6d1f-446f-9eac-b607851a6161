import { container } from 'tsyringe';

import { newDbId, toDbId } from '@malou-io/package-models';
import { ContactMode, MalouErrorCode, PlatformKey, ProviderClientSource } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ProviderClientsMapper } from ':modules/clients/provider-clients/provider-clients.mapper';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { CreateProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/create-provider-client/create-provider-client.use-case';
import { FetchProviderClientByEmailUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-by-email/fetch-provider-client-by-email.use-case';
import { FetchProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/fetch-provider-client.use-case';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { SendGiftWonComoEventUseCase } from ':modules/gift-draws/use-cases/send-gift-won-como-event/send-gift-won-como-event.use-case';
import { getDefaultGiftDraw } from ':modules/wheels-of-fortune/tests/wheels-of-fortune.builder';

describe('SendGiftWonComoEventUseCase', () => {
    let comoProviderWrapperMock: jest.Mocked<ComoProviderWrapper>;
    let fetchProviderClientUseCaseMock: jest.Mocked<FetchProviderClientUseCase>;
    let fetchProviderClientByEmailUseCaseMock: jest.Mocked<FetchProviderClientByEmailUseCase>;
    let createProviderClientUseCaseMock: jest.Mocked<CreateProviderClientUseCase>;
    let providerClientsMapperMock: jest.Mocked<ProviderClientsMapper>;

    const TEST_RESTAURANT_ID = newDbId().toString();

    beforeEach(() => {
        container.clearInstances();
        registerRepositories(['GiftDrawsRepository', 'ClientsRepository', 'ProviderClientsRepository']);

        // Mock ComoProviderWrapper
        comoProviderWrapperMock = {
            getComoClient: jest.fn(),
            submitEvent: jest.fn(),
        } as unknown as jest.Mocked<ComoProviderWrapper>;

        // Mock FetchProviderClientUseCase
        fetchProviderClientUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchProviderClientUseCase>;

        // Mock FetchProviderClientByEmailUseCase
        fetchProviderClientByEmailUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<FetchProviderClientByEmailUseCase>;

        // Mock CreateProviderClientUseCase
        createProviderClientUseCaseMock = {
            execute: jest.fn(),
        } as unknown as jest.Mocked<CreateProviderClientUseCase>;

        // Mock ProviderClientsMapper
        providerClientsMapperMock = {
            fromPlatformKeyToProviderClientSource: jest.fn(),
        } as unknown as jest.Mocked<ProviderClientsMapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ComoProviderWrapper, comoProviderWrapperMock);
        container.registerInstance(FetchProviderClientUseCase, fetchProviderClientUseCaseMock);
        container.registerInstance(FetchProviderClientByEmailUseCase, fetchProviderClientByEmailUseCaseMock);
        container.registerInstance(CreateProviderClientUseCase, createProviderClientUseCaseMock);
        container.registerInstance(ProviderClientsMapper, providerClientsMapperMock);
    });

    const createMockProviderClient = (overrides: Partial<ProviderClient> = {}): ProviderClient => {
        return new ProviderClient({
            id: newDbId().toString(),
            providerClientId: 'test-como-member-id',
            restaurantId: TEST_RESTAURANT_ID,
            source: ProviderClientSource.COMO,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            visits: [],
            contactOptions: [ContactMode.EMAIL], // Default to having consent
            ...overrides,
        });
    };

    describe('execute', () => {
        it('should successfully send gift won event when provider client exists', async () => {
            const TEST_CLIENT_ID = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    providerClients: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    providerClientId: 'existing-como-member-id',
                                    restaurantId: TEST_RESTAURANT_ID,
                                    source: ProviderClientSource.COMO,
                                    email: '<EMAIL>',
                                    firstName: 'John',
                                    lastName: 'Doe',
                                },
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'existing-como-member-id',
                email: '<EMAIL>',
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);
            comoProviderWrapperMock.submitEvent.mockResolvedValue();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);
            await useCase.execute(giftDrawId);

            expect(providerClientsMapperMock.fromPlatformKeyToProviderClientSource).toHaveBeenCalledWith(PlatformKey.COMO);
            expect(fetchProviderClientUseCaseMock.execute).toHaveBeenCalledWith(
                'existing-como-member-id',
                TEST_RESTAURANT_ID,
                PlatformKey.COMO
            );
            expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                restaurantId: TEST_RESTAURANT_ID,
                comoMemberId: 'existing-como-member-id',
                eventName: 'wof-dev-boite6OG',
                date: expect.any(Date),
            });
        });

        it('should fetch Como client by email when provider client does not exist', async () => {
            const TEST_CLIENT_ID_2 = newDbId().toString();
            const TEST_RESTAURANT_ID_2 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6c6'))
                                    .clientId(toDbId(TEST_CLIENT_ID_2))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_2))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_2))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_2))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'new-como-member-id',
                email: '<EMAIL>',
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientByEmailUseCaseMock.execute.mockResolvedValue(mockProviderClient);
            comoProviderWrapperMock.submitEvent.mockResolvedValue();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);
            await useCase.execute(giftDrawId);

            expect(fetchProviderClientByEmailUseCaseMock.execute).toHaveBeenCalledWith(
                '<EMAIL>',
                TEST_RESTAURANT_ID_2,
                PlatformKey.COMO
            );
            expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                restaurantId: TEST_RESTAURANT_ID_2,
                comoMemberId: 'new-como-member-id',
                eventName: 'wof-dev-doughnutauchoix',
                date: expect.any(Date),
            });
        });

        it('should throw error when gift draw is not found', async () => {
            const nonExistentGiftDrawId = newDbId().toString();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(nonExistentGiftDrawId)).rejects.toThrow();
        });

        it('should throw error when client is not found', async () => {
            const TEST_RESTAURANT_ID_3 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId('67fd08ec721bd35f1ae9d999'))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_3))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should throw error when client email is missing', async () => {
            const TEST_CLIENT_ID_3 = newDbId().toString();
            const TEST_RESTAURANT_ID_3 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_3))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_3))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_3))
                                    .email(undefined)
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_3))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should throw COMO_CLIENT_NOT_FOUND error when Como client is not found by email', async () => {
            const TEST_CLIENT_ID_4 = newDbId().toString();
            const TEST_RESTAURANT_ID_4 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_4))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_4))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_4))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientByEmailUseCaseMock.execute.mockResolvedValue(null as any);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: expect.objectContaining({
                        email: '<EMAIL>',
                        restaurantId: TEST_RESTAURANT_ID,
                    }),
                })
            );
        });

        it('should handle different gift IDs and map to correct Como gift names', async () => {
            const testCases = [
                { giftId: '67fd08ec721bd35f1ae9d6ca', expectedEventName: 'wof-dev-boite6OG' },
                { giftId: '67fd08ec721bd35f1ae9d6c6', expectedEventName: 'wof-dev-doughnutauchoix' },
                { giftId: '67fd08ec721bd35f1ae9d6d6', expectedEventName: 'wof-dev-OG' },
                { giftId: '67fd08ec721bd35f1ae9d6ce', expectedEventName: 'wof-dev-boissongourmande' },
                { giftId: '67fd08ec721bd35f1ae9d6c2', expectedEventName: 'wof-dev-espresso' },
            ];

            for (const testCaseData of testCases) {
                const TEST_CLIENT_ID = newDbId().toString();
                const TEST_RESTAURANT_ID_7 = newDbId().toString();

                const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                    seeds: {
                        giftDraws: {
                            data() {
                                return [
                                    getDefaultGiftDraw()
                                        .giftId(toDbId(testCaseData.giftId))
                                        .clientId(toDbId(TEST_CLIENT_ID))
                                        .restaurantId(toDbId(TEST_RESTAURANT_ID_7))
                                        .build(),
                                ];
                            },
                        },
                        clients: {
                            data() {
                                return [
                                    getDefaultClient()
                                        ._id(toDbId(TEST_CLIENT_ID))
                                        .email('<EMAIL>')
                                        .restaurantId(toDbId(TEST_RESTAURANT_ID_7))
                                        .build(),
                                ];
                            },
                        },
                        providerClients: {
                            data() {
                                return [
                                    {
                                        _id: newDbId(),
                                        providerClientId: 'como-member-id',
                                        restaurantId: TEST_RESTAURANT_ID_7,
                                        source: ProviderClientSource.COMO,
                                        email: '<EMAIL>',
                                        firstName: 'John',
                                        lastName: 'Doe',
                                    },
                                ];
                            },
                        },
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const giftDrawId = seededObjects.giftDraws[0]._id.toString();

                const mockProviderClient = createMockProviderClient({
                    providerClientId: 'como-member-id',
                    email: '<EMAIL>',
                });

                providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
                fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);
                comoProviderWrapperMock.submitEvent.mockResolvedValue();

                const useCase = container.resolve(SendGiftWonComoEventUseCase);
                await useCase.execute(giftDrawId);

                expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                    restaurantId: TEST_RESTAURANT_ID_7,
                    comoMemberId: 'como-member-id',
                    eventName: testCaseData.expectedEventName,
                    date: expect.any(Date),
                });

                // Clear mocks for next iteration
                jest.clearAllMocks();
            }
        });

        it('should throw error when gift ID is not mapped to Como gift name', async () => {
            const TEST_CLIENT_ID = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d999'))
                                    .clientId(toDbId(TEST_CLIENT_ID))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    providerClients: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    providerClientId: 'como-member-id',
                                    restaurantId: TEST_RESTAURANT_ID,
                                    source: ProviderClientSource.COMO,
                                    email: '<EMAIL>',
                                    firstName: 'John',
                                    lastName: 'Doe',
                                },
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'como-member-id',
                email: '<EMAIL>',
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should propagate errors from Como service', async () => {
            const TEST_CLIENT_ID = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    providerClients: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    providerClientId: 'como-member-id',
                                    restaurantId: TEST_RESTAURANT_ID,
                                    source: ProviderClientSource.COMO,
                                    email: '<EMAIL>',
                                    firstName: 'John',
                                    lastName: 'Doe',
                                },
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'como-member-id',
                email: '<EMAIL>',
            });

            const mockError = new Error('Como service error');

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);
            comoProviderWrapperMock.submitEvent.mockRejectedValue(mockError);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow(mockError);
        });

        it('should handle gift draw without client ID', async () => {
            const testCase = new TestCaseBuilderV2<'giftDraws'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(undefined)
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should handle provider client source mapping failure', async () => {
            const TEST_CLIENT_ID_5 = newDbId().toString();
            const TEST_RESTAURANT_ID_5 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_5))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_5))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_5))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_5))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(null as any);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should upsert provider client after fetching by email', async () => {
            const TEST_CLIENT_ID_6 = newDbId().toString();
            const TEST_RESTAURANT_ID_6 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_6))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_6))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_6))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_6))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'new-como-member-id',
                email: '<EMAIL>',
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientByEmailUseCaseMock.execute.mockResolvedValue(mockProviderClient);
            comoProviderWrapperMock.submitEvent.mockResolvedValue();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);
            await useCase.execute(giftDrawId);

            // Verify that the provider client was upserted
            const providerClientsRepository = container.resolve(ProviderClientsRepository);
            const upsertedClient = await providerClientsRepository.getByEmailAndRestaurantIdAndSource({
                email: '<EMAIL>',
                restaurantId: TEST_RESTAURANT_ID,
                source: ProviderClientSource.COMO,
            });

            expect(upsertedClient).toBeDefined();
            expect(upsertedClient?.providerClientId).toBe('new-como-member-id');
        });

        it('should send coupon code event when client has no consent', async () => {
            const TEST_CLIENT_ID_7 = newDbId().toString();
            const TEST_RESTAURANT_ID_7 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_7))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_7))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_7))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_7))
                                    .build(),
                            ];
                        },
                    },
                    providerClients: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    providerClientId: 'como-member-no-consent',
                                    restaurantId: TEST_RESTAURANT_ID_7,
                                    source: ProviderClientSource.COMO,
                                    email: '<EMAIL>',
                                    firstName: 'Jane',
                                    lastName: 'Doe',
                                },
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'como-member-no-consent',
                email: '<EMAIL>',
                contactOptions: [], // No consent
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);
            comoProviderWrapperMock.submitEvent.mockResolvedValue();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);
            await useCase.execute(giftDrawId);

            expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                restaurantId: TEST_RESTAURANT_ID_7,
                comoMemberId: 'como-member-no-consent',
                eventName: 'wof-dev-code-boite6OG', // Coupon code instead of gift event
                date: expect.any(Date),
            });
        });

        it('should create provider client when not found by email and COMO_CLIENT_NOT_FOUND error is thrown', async () => {
            const TEST_CLIENT_ID_8 = newDbId().toString();
            const TEST_RESTAURANT_ID_8 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6c6'))
                                    .clientId(toDbId(TEST_CLIENT_ID_8))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_8))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_8))
                                    .email('<EMAIL>')
                                    .firstName('New')
                                    .lastName('User')
                                    .phone({ prefix: 33, digits: 123456789 })
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_8))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockCreatedProviderClient = createMockProviderClient({
                providerClientId: 'newly-created-como-member',
                email: '<EMAIL>',
                firstName: 'New',
                lastName: 'User',
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);

            // Mock the COMO_CLIENT_NOT_FOUND error from fetchProviderClientByEmailUseCase
            const comoClientNotFoundError = new MalouError(MalouErrorCode.COMO_CLIENT_NOT_FOUND, {
                message: 'Como client not found',
            });
            fetchProviderClientByEmailUseCaseMock.execute.mockRejectedValue(comoClientNotFoundError);

            // Mock successful client creation
            createProviderClientUseCaseMock.execute.mockResolvedValue(mockCreatedProviderClient);
            comoProviderWrapperMock.submitEvent.mockResolvedValue();

            const useCase = container.resolve(SendGiftWonComoEventUseCase);
            await useCase.execute(giftDrawId);

            expect(createProviderClientUseCaseMock.execute).toHaveBeenCalledWith(
                TEST_RESTAURANT_ID_8,
                {
                    email: '<EMAIL>',
                    firstName: 'New',
                    lastName: 'User',
                    phone: { prefix: 33, digits: 123456789 },
                },
                PlatformKey.COMO
            );

            expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                restaurantId: TEST_RESTAURANT_ID_8,
                comoMemberId: 'newly-created-como-member',
                eventName: 'wof-dev-doughnutauchoix',
                date: expect.any(Date),
            });
        });

        it('should handle different gift IDs and map to correct Como coupon codes when no consent', async () => {
            const testCases = [
                { giftId: '67fd08ec721bd35f1ae9d6ca', expectedEventName: 'wof-dev-code-boite6OG' },
                { giftId: '67fd08ec721bd35f1ae9d6c6', expectedEventName: 'wof-dev-code-doughnutauchoix' },
                { giftId: '67fd08ec721bd35f1ae9d6d6', expectedEventName: 'wof-dev-code-OG' },
                { giftId: '67fd08ec721bd35f1ae9d6ce', expectedEventName: 'wof-dev-code-boissongourmande' },
                { giftId: '67fd08ec721bd35f1ae9d6c2', expectedEventName: 'wof-dev-code-espresso' },
            ];

            for (const testCaseData of testCases) {
                const TEST_CLIENT_ID = newDbId().toString();
                const TEST_RESTAURANT_ID_9 = newDbId().toString();

                const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                    seeds: {
                        giftDraws: {
                            data() {
                                return [
                                    getDefaultGiftDraw()
                                        .giftId(toDbId(testCaseData.giftId))
                                        .clientId(toDbId(TEST_CLIENT_ID))
                                        .restaurantId(toDbId(TEST_RESTAURANT_ID_9))
                                        .build(),
                                ];
                            },
                        },
                        clients: {
                            data() {
                                return [
                                    getDefaultClient()
                                        ._id(toDbId(TEST_CLIENT_ID))
                                        .email('<EMAIL>')
                                        .restaurantId(toDbId(TEST_RESTAURANT_ID_9))
                                        .build(),
                                ];
                            },
                        },
                        providerClients: {
                            data() {
                                return [
                                    {
                                        _id: newDbId(),
                                        providerClientId: 'como-member-id',
                                        restaurantId: TEST_RESTAURANT_ID_9,
                                        source: ProviderClientSource.COMO,
                                        email: '<EMAIL>',
                                        firstName: 'John',
                                        lastName: 'Doe',
                                    },
                                ];
                            },
                        },
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const giftDrawId = seededObjects.giftDraws[0]._id.toString();

                const mockProviderClient = createMockProviderClient({
                    providerClientId: 'como-member-id',
                    email: '<EMAIL>',
                    contactOptions: [], // No consent
                });

                providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
                fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);
                comoProviderWrapperMock.submitEvent.mockResolvedValue();

                const useCase = container.resolve(SendGiftWonComoEventUseCase);
                await useCase.execute(giftDrawId);

                expect(comoProviderWrapperMock.submitEvent).toHaveBeenCalledWith({
                    restaurantId: TEST_RESTAURANT_ID_9,
                    comoMemberId: 'como-member-id',
                    eventName: testCaseData.expectedEventName,
                    date: expect.any(Date),
                });

                // Clear mocks for next iteration
                jest.clearAllMocks();
            }
        });

        it('should throw error when gift ID is not mapped to Como coupon code', async () => {
            const TEST_CLIENT_ID = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients' | 'providerClients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d999'))
                                    .clientId(toDbId(TEST_CLIENT_ID))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID))
                                    .build(),
                            ];
                        },
                    },
                    providerClients: {
                        data() {
                            return [
                                {
                                    _id: newDbId(),
                                    providerClientId: 'como-member-id',
                                    restaurantId: TEST_RESTAURANT_ID,
                                    source: ProviderClientSource.COMO,
                                    email: '<EMAIL>',
                                    firstName: 'John',
                                    lastName: 'Doe',
                                },
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            const mockProviderClient = createMockProviderClient({
                providerClientId: 'como-member-id',
                email: '<EMAIL>',
                contactOptions: [], // No consent
            });

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);
            fetchProviderClientUseCaseMock.execute.mockResolvedValue(mockProviderClient);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow();
        });

        it('should throw error when created provider client is null', async () => {
            const TEST_CLIENT_ID_9 = newDbId().toString();
            const TEST_RESTAURANT_ID_9 = newDbId().toString();

            const testCase = new TestCaseBuilderV2<'giftDraws' | 'clients'>({
                seeds: {
                    giftDraws: {
                        data() {
                            return [
                                getDefaultGiftDraw()
                                    .giftId(toDbId('67fd08ec721bd35f1ae9d6ca'))
                                    .clientId(toDbId(TEST_CLIENT_ID_9))
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_9))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [
                                getDefaultClient()
                                    ._id(toDbId(TEST_CLIENT_ID_9))
                                    .email('<EMAIL>')
                                    .restaurantId(toDbId(TEST_RESTAURANT_ID_9))
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const giftDrawId = seededObjects.giftDraws[0]._id.toString();

            providerClientsMapperMock.fromPlatformKeyToProviderClientSource.mockReturnValue(ProviderClientSource.COMO);

            // Mock the COMO_CLIENT_NOT_FOUND error from fetchProviderClientByEmailUseCase
            const comoClientNotFoundError = new MalouError(MalouErrorCode.COMO_CLIENT_NOT_FOUND, {
                message: 'Como client not found',
            });
            fetchProviderClientByEmailUseCaseMock.execute.mockRejectedValue(comoClientNotFoundError);

            // Mock failed client creation (returns null)
            createProviderClientUseCaseMock.execute.mockResolvedValue(null as any);

            const useCase = container.resolve(SendGiftWonComoEventUseCase);

            await expect(useCase.execute(giftDrawId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: expect.objectContaining({
                        email: '<EMAIL>',
                    }),
                })
            );
        });
    });
});
