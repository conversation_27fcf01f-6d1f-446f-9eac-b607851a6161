import { autoInjectable } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightsRepository } from ':modules/post-insights/post-insights.repository';

@autoInjectable()
export class PostInsightsUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postInsightsRepository: PostInsightsRepository
    ) {}

    getStoriesInsights = async (restaurantId: string, platformKey: PlatformKey, socialIds: string[]) => {
        const platform: Pick<Platform, 'socialId'> | null = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            restaurantId,
            platformKey,
            {
                socialId: true,
            }
        );
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey,
                },
            });
        }
        const { socialId } = platform;

        return this._postInsightsRepository.find({
            filter: { platformSocialId: socialId, socialId: { $in: socialIds } },
            options: { lean: true },
        });
    };
}
