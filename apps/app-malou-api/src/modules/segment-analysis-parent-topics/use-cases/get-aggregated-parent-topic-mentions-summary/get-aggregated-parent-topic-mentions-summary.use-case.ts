import { injectable } from 'tsyringe';

import { GetAggregatedParentTopicMentionsSummaryBodyDto, ParentTopicMentionsSummaryDto } from '@malou-io/package-dto';
import { getDateRangeFromMalouComparisonPeriod, MalouComparisonPeriod, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

@injectable()
export class GetAggregatedParentTopicMentionsSummaryUseCase {
    constructor(private segmentAnalysisParentTopicsRepository: SegmentAnalysisParentTopicsRepository) {}

    async execute(filters: GetAggregatedParentTopicMentionsSummaryBodyDto): Promise<ParentTopicMentionsSummaryDto[]> {
        const { startDate, endDate, comparisonPeriod, restaurantIds, platformKeys } = filters;

        const period = this._getPeriod({ startDate, endDate, comparisonPeriod });

        const results = await this.segmentAnalysisParentTopicsRepository.getAggregatedParentTopicMentionsSummary({
            restaurantIds,
            startDate: period.startDate,
            endDate: period.endDate,
            platformKeys,
        });

        return results.map((result) => ({
            topicName: result.topicName,
            negativeMentions: result.negativeMentions,
            positiveMentions: result.positiveMentions,
            restaurantIds: result.restaurantIds,
        }));
    }

    private _getPeriod({
        startDate,
        endDate,
        comparisonPeriod,
    }: {
        startDate: Date;
        endDate: Date;
        comparisonPeriod?: MalouComparisonPeriod;
    }): { startDate: Date; endDate: Date } {
        if (!comparisonPeriod) {
            return { startDate, endDate };
        }

        const period = getDateRangeFromMalouComparisonPeriod({
            dateFilters: { startDate, endDate },
            comparisonPeriod,
        });

        if (!period.startDate || !period.endDate) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: '[GetAggregatedParentTopicMentionsSummaryUseCase] Invalid date range for previous period comparison',
                metadata: { startDate, comparisonPeriod },
            });
        }

        return { startDate: period.startDate, endDate: period.endDate };
    }
}
