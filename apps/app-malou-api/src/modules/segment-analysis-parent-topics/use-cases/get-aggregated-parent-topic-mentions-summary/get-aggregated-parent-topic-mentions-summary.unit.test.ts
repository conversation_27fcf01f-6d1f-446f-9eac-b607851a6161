import { container } from 'tsyringe';

import { ParentTopicMentionsSummaryDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';
import { GetAggregatedParentTopicMentionsSummaryUseCase } from ':modules/segment-analysis-parent-topics/use-cases/get-aggregated-parent-topic-mentions-summary/get-aggregated-parent-topic-mentions-summary.use-case';

describe('GetAggregatedParentTopicMentionsSummaryUseCase', () => {
    beforeAll(() => {
        registerRepositories(['SegmentAnalysisParentTopicsRepository', 'SegmentAnalysesRepository']);
    });

    describe('execute', () => {
        it('should return aggregated mentions summary for single restaurant and single topic', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 1,
                            positiveMentions: 1,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should count same review only once per sentiment even with multiple segments', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                // Same review with 2 positive segments about pizza
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great pizza crust')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Amazing pizza toppings')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 0,
                            positiveMentions: 1, // Only counted once
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should count review in both positive and negative if it has segments with different sentiments and different topics', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),

                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Service')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const foodParentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const serviceParentTopicId = dependencies.segmentAnalysisParentTopics()[1]._id;
                            return [
                                // Same review with positive segment
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great pizza crust')
                                    .segmentAnalysisParentTopicIds([foodParentTopicId])
                                    .build(),
                                // Same review with negative segment
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('Pizza was too salty')
                                    .segmentAnalysisParentTopicIds([foodParentTopicId])
                                    .build(),
                                // Same review with another negative segment with different parent topic
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('Slow service')
                                    .segmentAnalysisParentTopicIds([serviceParentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 1,
                            positiveMentions: 1,
                            restaurantIds: [restaurantId.toString()],
                        },
                        {
                            topicName: 'Service',
                            negativeMentions: 1,
                            positiveMentions: 0,
                            restaurantIds: [restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should aggregate mentions across multiple restaurants for same topic', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurant1Id = newDbId();
            const restaurant2Id = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurant1Id)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurant2Id)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopic1Id = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const parentTopic2Id = dependencies.segmentAnalysisParentTopics()[1]._id;
                            return [
                                // Restaurant 1 - 1 positive
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopic1Id])
                                    .build(),
                                // Restaurant 2 - 1 negative
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([parentTopic2Id])
                                    .build(),
                                // Restaurant 2 - 1 positive
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review3')
                                    .reviewSocialCreatedAt(new Date('2025-01-25'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .category(ReviewAnalysisTag.OVERALL_EXPERIENCE)
                                    .segmentAnalysisParentTopicIds([parentTopic2Id])
                                    .build(),
                                // Restaurant 2 - another positive for same review (should be counted only once)
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review3')
                                    .reviewSocialCreatedAt(new Date('2025-01-25'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopic2Id])
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    const restaurant1 = dependencies.segmentAnalysisParentTopics[0].restaurantId.toString();
                    const restaurant2 = dependencies.segmentAnalysisParentTopics[1].restaurantId.toString();
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 1,
                            positiveMentions: 2,
                            restaurantIds: [restaurant1, restaurant2],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurant1Id.toString(), restaurant2Id.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should handle shared review across multiple restaurants (same reviewSocialId, different parent topics)', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurant1Id = newDbId();
            const restaurant2Id = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurant1Id)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurant2Id)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopic1Id = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const parentTopic2Id = dependencies.segmentAnalysisParentTopics()[1]._id;
                            return [
                                // Shared review linked to both restaurants' parent topics
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('shared-review-1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopic1Id, parentTopic2Id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    const restaurant1 = dependencies.segmentAnalysisParentTopics[0].restaurantId.toString();
                    const restaurant2 = dependencies.segmentAnalysisParentTopics[1].restaurantId.toString();
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 0,
                            positiveMentions: 1, // Counted once even though it's linked to 2 parent topics
                            restaurantIds: [restaurant1, restaurant2],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurant1Id.toString(), restaurant2Id.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
            expect(result[0].restaurantIds).toHaveLength(2);
        });

        it('should filter by date range correctly', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-15');
            const endDate = new Date('2025-01-20');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                // Before date range
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-10'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                // Within date range
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-17'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                // After date range
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review3')
                                    .reviewSocialCreatedAt(new Date('2025-01-25'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 0,
                            positiveMentions: 1,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should filter by platform keys correctly', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                // GMB review
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                // Facebook review (should be excluded)
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 0,
                            positiveMentions: 1,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB], // Only GMB
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return empty array when no segments match filters', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                // Outside date range
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2024-12-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should handle multiple topics and return sorted by negativeMentions desc then name', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Zucchini')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Apple Pie')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const zucchiniTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const applePieTopicId = dependencies.segmentAnalysisParentTopics()[1]._id;
                            return [
                                // Zucchini - 1 positive
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([zucchiniTopicId])
                                    .build(),
                                // Apple Pie - 1 negative
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([applePieTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Apple Pie',
                            negativeMentions: 1,
                            positiveMentions: 0,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[1].restaurantId.toString()],
                        },
                        {
                            topicName: 'Zucchini',
                            negativeMentions: 0,
                            positiveMentions: 1,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should not count neutral sentiment reviews', async () => {
            const useCase = container.resolve(GetAggregatedParentTopicMentionsSummaryUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEUTRAL)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-20'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicMentionsSummaryDto[] {
                    return [
                        {
                            topicName: 'Pizza',
                            negativeMentions: 0,
                            positiveMentions: 1,
                            restaurantIds: [dependencies.segmentAnalysisParentTopics[0].restaurantId.toString()],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                restaurantIds: [restaurantId.toString()],
                startDate,
                endDate,
                platformKeys: [PlatformKey.GMB],
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
