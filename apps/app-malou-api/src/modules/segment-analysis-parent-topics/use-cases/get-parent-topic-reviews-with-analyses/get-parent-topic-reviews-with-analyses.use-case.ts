import { injectable } from 'tsyringe';

import { GetParentTopicReviewsWithAnalysesBodyDto, ParentTopicReviewsWithAnalysesDto } from '@malou-io/package-dto';
import { getDateRangeFromMalouComparisonPeriod, MalouComparisonPeriod, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

@injectable()
export class GetParentTopicReviewsWithAnalysesUseCase {
    constructor(private segmentAnalysisParentTopicsRepository: SegmentAnalysisParentTopicsRepository) {}

    async execute(filters: GetParentTopicReviewsWithAnalysesBodyDto): Promise<ParentTopicReviewsWithAnalysesDto[]> {
        const { topicName, restaurantIds, startDate, endDate, comparisonPeriod, platformKeys } = filters;

        const period = this._getPeriod({ startDate, endDate, comparisonPeriod });

        return Promise.all(
            restaurantIds.map(async (restaurantId) => {
                const reviewsWithAnalyses = await this.segmentAnalysisParentTopicsRepository.getParentTopicReviewsWithAnalyses({
                    topicName,
                    restaurantId,
                    startDate: period.startDate,
                    endDate: period.endDate,
                    platformKeys,
                });

                return {
                    topicName,
                    restaurantId,
                    reviewsWithAnalyses: reviewsWithAnalyses.map((review) => ({
                        ...review,
                        id: review.id.toString(),
                        text: review.text ?? undefined,
                        rating: review.rating ?? undefined,
                        lang: review.lang ?? undefined,
                        ratingTags: review.ratingTags?.map((rt) => ({ ...rt, translationsId: rt.translationsId?.toString() })) ?? [],
                        menuItemReviews: review.menuItemReviews ?? [],
                        socialCreatedAt: review.socialCreatedAt.toISOString(),
                        reviewer: {
                            ...review.reviewer,
                            profilePhotoUrl: review.reviewer?.profilePhotoUrl ?? undefined,
                            displayName: review.reviewer?.displayName ?? '',
                            socialUrl: review.reviewer?.socialUrl ?? undefined,
                            socialId: review.reviewer?.socialId ?? undefined,
                        },
                        socialAttachments: review.socialAttachments ?? [],
                    })),
                };
            })
        );
    }

    private _getPeriod({
        startDate,
        endDate,
        comparisonPeriod,
    }: {
        startDate: Date;
        endDate: Date;
        comparisonPeriod?: MalouComparisonPeriod;
    }): { startDate: Date; endDate: Date } {
        if (!comparisonPeriod) {
            return { startDate, endDate };
        }

        const period = getDateRangeFromMalouComparisonPeriod({
            dateFilters: { startDate, endDate },
            comparisonPeriod,
        });

        if (!period.startDate || !period.endDate) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: '[GetParentTopicReviewsWithAnalysesUseCase] Invalid date range for previous period comparison',
                metadata: { startDate, comparisonPeriod },
            });
        }

        return { startDate: period.startDate, endDate: period.endDate };
    }
}
