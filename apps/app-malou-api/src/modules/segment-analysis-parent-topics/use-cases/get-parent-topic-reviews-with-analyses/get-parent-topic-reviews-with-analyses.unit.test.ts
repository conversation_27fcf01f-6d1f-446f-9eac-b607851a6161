import { container } from 'tsyringe';

import { ParentTopicReviewsWithAnalysesDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, ReviewAnalysisSentiment, ReviewAnalysisSubCategory, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';
import { GetParentTopicReviewsWithAnalysesUseCase } from ':modules/segment-analysis-parent-topics/use-cases/get-parent-topic-reviews-with-analyses/get-parent-topic-reviews-with-analyses.use-case';

describe('GetParentTopicReviewsWithAnalysesUseCase', () => {
    beforeAll(() => {
        registerRepositories(['SegmentAnalysisParentTopicsRepository', 'SegmentAnalysesRepository', 'ReviewsRepository']);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('execute', () => {
        it('should return empty array when no parent topic found', async () => {
            const useCase = container.resolve(GetParentTopicReviewsWithAnalysesUseCase);

            const restaurantId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const result = await useCase.execute({
                topicName: 'NonExistentTopic',
                restaurantIds: [restaurantId.toString()],
                platformKeys: [PlatformKey.GMB],
                startDate,
                endDate,
            });

            expect(result).toEqual([
                {
                    topicName: 'NonExistentTopic',
                    restaurantId: restaurantId.toString(),
                    reviewsWithAnalyses: [],
                },
            ]);
        });

        it('should return parent topic with reviews and segment analyses', async () => {
            const useCase = container.resolve(GetParentTopicReviewsWithAnalysesUseCase);

            const restaurantId = newDbId();
            const platformId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses' | 'reviews'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .socialId('review1')
                                    .key(PlatformKey.GMB)
                                    .text('Great pizza with amazing crust')
                                    .rating(5)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-15'))
                                    .reviewer({
                                        displayName: 'John Doe',
                                        profilePhotoUrl: 'https://example.com/photo.jpg',
                                    })
                                    .socialAttachments([])
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const review = dependencies.reviews()[0];
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review.socialId)
                                    .reviewSocialCreatedAt(review.socialCreatedAt)
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great pizza')
                                    .aiFoundSegment('pizza quality')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicReviewsWithAnalysesDto[] {
                    const segmentAnalysis = dependencies.segmentAnalyses[0];
                    const review = dependencies.reviews[0];
                    return [
                        {
                            topicName: 'Pizza',
                            restaurantId: dependencies.segmentAnalysisParentTopics[0].restaurantId.toString(),
                            reviewsWithAnalyses: [
                                {
                                    id: review._id.toString(),
                                    text: 'Great pizza with amazing crust',
                                    socialId: 'review1',
                                    key: PlatformKey.GMB,
                                    rating: 5,
                                    lang: review.lang || undefined,
                                    ratingTags:
                                        review.ratingTags?.map((rt) => ({ ...rt, translationsId: rt.translationsId?.toString() })) || [],
                                    menuItemReviews: review.menuItemReviews || [],
                                    reviewer: {
                                        displayName: 'John Doe',
                                        profilePhotoUrl: 'https://example.com/photo.jpg',
                                        socialId: undefined,
                                        socialUrl: undefined,
                                    },
                                    socialCreatedAt: new Date('2025-01-15').toISOString(),
                                    socialAttachments: [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'Great pizza',
                                            aiFoundSegment: 'pizza quality',
                                            category: segmentAnalysis.category,
                                        },
                                    ],
                                },
                            ],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                topicName: 'Pizza',
                restaurantIds: [restaurantId.toString()],
                platformKeys: [PlatformKey.GMB],
                startDate,
                endDate,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should group multiple segment analyses for the same review', async () => {
            const useCase = container.resolve(GetParentTopicReviewsWithAnalysesUseCase);

            const restaurantId = newDbId();
            const platformId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses' | 'reviews'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .socialId('review1')
                                    .key(PlatformKey.GMB)
                                    .text('Great pizza with amazing crust but slow service')
                                    .rating(4)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-15'))
                                    .reviewer({
                                        displayName: 'John Doe',
                                    })
                                    .socialAttachments([])
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const review = dependencies.reviews()[0];
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review.socialId)
                                    .reviewSocialCreatedAt(review.socialCreatedAt)
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great pizza')
                                    .aiFoundSegment('pizza quality')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review.socialId)
                                    .reviewSocialCreatedAt(review.socialCreatedAt)
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('amazing crust')
                                    .aiFoundSegment('pizza crust')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicReviewsWithAnalysesDto[] {
                    const review = dependencies.reviews[0];
                    const segmentAnalyses = dependencies.segmentAnalyses;
                    return [
                        {
                            topicName: 'Pizza',
                            restaurantId: dependencies.segmentAnalysisParentTopics[0].restaurantId.toString(),
                            reviewsWithAnalyses: [
                                {
                                    id: review._id.toString(),
                                    text: 'Great pizza with amazing crust but slow service',
                                    socialId: 'review1',
                                    key: PlatformKey.GMB,
                                    rating: 4,
                                    lang: review.lang || undefined,
                                    ratingTags:
                                        review.ratingTags?.map((rt) => ({ ...rt, translationsId: rt.translationsId?.toString() })) || [],
                                    menuItemReviews: review.menuItemReviews || [],
                                    reviewer: {
                                        displayName: 'John Doe',
                                    },
                                    socialCreatedAt: new Date('2025-01-15').toISOString(),
                                    socialAttachments: [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'Great pizza',
                                            aiFoundSegment: 'pizza quality',
                                            category: segmentAnalyses[0].category,
                                        },
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'amazing crust',
                                            aiFoundSegment: 'pizza crust',
                                            category: segmentAnalyses[1].category,
                                        },
                                    ],
                                },
                            ],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                topicName: 'Pizza',
                restaurantIds: [restaurantId.toString()],
                platformKeys: [PlatformKey.GMB],
                startDate,
                endDate,
            });

            expect(result).toEqual(expectedResult);
        });

        it('should handle multiple reviews with mixed sentiments', async () => {
            const useCase = container.resolve(GetParentTopicReviewsWithAnalysesUseCase);

            const restaurantId = newDbId();
            const platformId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses' | 'reviews'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Service')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .socialId('review1')
                                    .key(PlatformKey.GMB)
                                    .text('Fast service')
                                    .rating(5)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-15'))
                                    .reviewer({ displayName: 'Alice' })
                                    .socialAttachments([])
                                    .build(),
                                getDefaultReview()
                                    .socialId('review2')
                                    .key(PlatformKey.GMB)
                                    .text('Slow service')
                                    .rating(2)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-20'))
                                    .reviewer({ displayName: 'Bob' })
                                    .socialAttachments([])
                                    .build(),
                                getDefaultReview()
                                    .socialId('review3')
                                    .key(PlatformKey.TRIPADVISOR)
                                    .text('Great service TA')
                                    .rating(5)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2024-01-17'))
                                    .reviewer({ displayName: 'Bob' })
                                    .socialAttachments([])
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const review1 = dependencies.reviews()[0];
                            const review2 = dependencies.reviews()[1];
                            const review3 = dependencies.reviews()[2];
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review1.socialId)
                                    .reviewSocialCreatedAt(review1.socialCreatedAt)
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Fast service')
                                    .aiFoundSegment('service speed')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review2.socialId)
                                    .reviewSocialCreatedAt(review2.socialCreatedAt)
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('Slow service')
                                    .aiFoundSegment('service speed')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(review3.socialId)
                                    .reviewSocialCreatedAt(review3.socialCreatedAt)
                                    .platformKey(PlatformKey.TRIPADVISOR)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great service TA')
                                    .aiFoundSegment('service quality')
                                    .segmentAnalysisParentTopicIds([parentTopicId])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicReviewsWithAnalysesDto[] {
                    const review1 = dependencies.reviews[0];
                    const review2 = dependencies.reviews[1];
                    const segmentAnalyses = dependencies.segmentAnalyses;
                    return [
                        {
                            topicName: 'Service',
                            restaurantId: dependencies.segmentAnalysisParentTopics[0].restaurantId.toString(),
                            reviewsWithAnalyses: expect.arrayContaining([
                                expect.objectContaining({
                                    id: review1._id.toString(),
                                    text: 'Fast service',
                                    socialId: 'review1',
                                    key: PlatformKey.GMB,
                                    rating: 5,
                                    reviewer: expect.objectContaining({ displayName: 'Alice' }),
                                    lang: review1.lang || undefined,
                                    ratingTags:
                                        review1.ratingTags?.map((rt) => ({
                                            ...rt,
                                            translationsId: rt.translationsId?.toString(),
                                        })) || [],
                                    menuItemReviews: review1.menuItemReviews || [],
                                    socialAttachments: [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'Fast service',
                                            aiFoundSegment: 'service speed',
                                            category: segmentAnalyses[0].category,
                                        },
                                    ],
                                }),
                                expect.objectContaining({
                                    id: review2._id.toString(),
                                    text: 'Slow service',
                                    socialId: 'review2',
                                    key: PlatformKey.GMB,
                                    rating: 2,
                                    reviewer: expect.objectContaining({ displayName: 'Bob' }),
                                    lang: review2.lang || undefined,
                                    ratingTags:
                                        review2.ratingTags?.map((rt) => ({
                                            ...rt,
                                            translationsId: rt.translationsId?.toString(),
                                        })) || [],
                                    socialAttachments: [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                            segment: 'Slow service',
                                            aiFoundSegment: 'service speed',
                                            category: segmentAnalyses[1].category,
                                        },
                                    ],
                                }),
                            ]),
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                topicName: 'Service',
                restaurantIds: [restaurantId.toString()],
                platformKeys: [PlatformKey.GMB],
                startDate,
                endDate,
            });

            expect(result).toMatchObject(expectedResult);
        });

        it('should merge multiple parent topics with same name', async () => {
            const useCase = container.resolve(GetParentTopicReviewsWithAnalysesUseCase);

            const restaurantId = newDbId();
            const platformId = newDbId();
            const startDate = new Date('2025-01-01');
            const endDate = new Date('2025-01-31');

            const testCase = new TestCaseBuilderV2<'segmentAnalysisParentTopics' | 'segmentAnalyses' | 'reviews'>({
                seeds: {
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .subcategory(ReviewAnalysisSubCategory.MENU_ITEMS)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name('Pizza')
                                    .restaurantId(restaurantId)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .subcategory(ReviewAnalysisSubCategory.STAFF_MEMBERS)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data() {
                            return [
                                getDefaultReview()
                                    .socialId('review1')
                                    .key(PlatformKey.GMB)
                                    .text('Good pizza')
                                    .rating(5)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-15'))
                                    .reviewer({ displayName: 'Alice' })
                                    .socialAttachments([])
                                    .build(),
                                getDefaultReview()
                                    .socialId('review2')
                                    .key(PlatformKey.GMB)
                                    .text('Great pizza')
                                    .rating(5)
                                    .restaurantId(restaurantId)
                                    .platformId(platformId)
                                    .socialCreatedAt(new Date('2025-01-17'))
                                    .reviewer({ displayName: 'Bob' })
                                    .socialAttachments([])
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const parentTopicId1 = dependencies.segmentAnalysisParentTopics()[0]._id;
                            const parentTopicId2 = dependencies.segmentAnalysisParentTopics()[1]._id;
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review1')
                                    .reviewSocialCreatedAt(new Date('2025-01-15'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Good pizza')
                                    .aiFoundSegment('pizza')
                                    .segmentAnalysisParentTopicIds([parentTopicId1])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId('review2')
                                    .reviewSocialCreatedAt(new Date('2025-01-17'))
                                    .platformKey(PlatformKey.GMB)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('Great pizza')
                                    .aiFoundSegment('pizza')
                                    .segmentAnalysisParentTopicIds([parentTopicId2])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): ParentTopicReviewsWithAnalysesDto[] {
                    const reviews = dependencies.reviews;
                    const segmentAnalyses = dependencies.segmentAnalyses;
                    return [
                        {
                            topicName: 'Pizza',
                            restaurantId: dependencies.segmentAnalysisParentTopics[0].restaurantId.toString(),
                            reviewsWithAnalyses: [
                                {
                                    id: reviews[0]._id.toString(),
                                    text: 'Good pizza',
                                    socialId: 'review1',
                                    key: PlatformKey.GMB,
                                    rating: 5,
                                    reviewer: expect.objectContaining({ displayName: 'Alice' }),
                                    socialCreatedAt: new Date('2025-01-15').toISOString(),
                                    lang: reviews[0].lang || undefined,
                                    socialAttachments: [],
                                    ratingTags:
                                        reviews[0].ratingTags?.map((rt) => ({ ...rt, translationsId: rt.translationsId?.toString() })) ||
                                        [],
                                    menuItemReviews: reviews[0].menuItemReviews || [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'Good pizza',
                                            aiFoundSegment: 'pizza',
                                            category: segmentAnalyses[0].category,
                                        },
                                    ],
                                },
                                {
                                    id: reviews[1]._id.toString(),
                                    text: 'Great pizza',
                                    socialId: 'review2',
                                    key: PlatformKey.GMB,
                                    rating: 5,
                                    reviewer: expect.objectContaining({ displayName: 'Bob' }),
                                    socialCreatedAt: new Date('2025-01-17').toISOString(),
                                    socialAttachments: [],
                                    lang: reviews[1].lang || undefined,
                                    ratingTags:
                                        reviews[1].ratingTags?.map((rt) => ({ ...rt, translationsId: rt.translationsId?.toString() })) ||
                                        [],
                                    menuItemReviews: reviews[1].menuItemReviews || [],
                                    segmentAnalyses: [
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            segment: 'Great pizza',
                                            aiFoundSegment: 'pizza',
                                            category: segmentAnalyses[1].category,
                                        },
                                    ],
                                },
                            ],
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResults: ParentTopicReviewsWithAnalysesDto[] = testCase.getExpectedResult();

            const results = await useCase.execute({
                topicName: 'Pizza',
                restaurantIds: [restaurantId.toString()],
                platformKeys: [PlatformKey.GMB],
                startDate,
                endDate,
            });

            const sortedResults = results.map((result) => ({
                ...result,
                reviewsWithAnalyses: result?.reviewsWithAnalyses.sort((a, b) => (a.socialId! < b.socialId! ? -1 : 1)) ?? [],
            }));

            const sortedExpectedResults = expectedResults.map((expectedResult) => ({
                ...expectedResult,
                reviewsWithAnalyses: expectedResult.reviewsWithAnalyses.sort((a, b) => (a.socialId! < b.socialId! ? -1 : 1)),
            }));
            expect(sortedResults).toMatchObject(sortedExpectedResults);
        });
    });
});
