import { Router } from 'express';
import { singleton } from 'tsyringe';

import SegmentAnalysisParentTopicsController from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class SegmentAnalysisParentTopicsRouter {
    constructor(private _segmentAnalysesParentTopicsController: SegmentAnalysisParentTopicsController) {}

    init(router: Router): void {
        router.post('/segment-analysis-parent-topics', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleCreateSegmentAnalysisParentTopic(req, res, next)
        );

        router.post('/segment-analysis-parent-topics/aggregated-mentions-summary', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleGetAggregatedParentTopicMentionsSummary(req, res, next)
        );

        router.post('/segment-analysis-parent-topics/parent-topic-reviews-with-analyses', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleGetParentTopicReviewsWithAnalyses(req, res, next)
        );

        router.put('/segment-analysis-parent-topics/merge', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleMergeSegmentAnalysisParentTopics(req, res, next)
        );

        router.put('/segment-analysis-parent-topics/:id', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleUpdateSegmentAnalysisParentTopic(req, res, next)
        );

        router.delete('/segment-analysis-parent-topics/:id', authorize(), (req, res, next) =>
            this._segmentAnalysesParentTopicsController.handleDeleteSegmentAnalysisParentTopic(req, res, next)
        );
    }
}
