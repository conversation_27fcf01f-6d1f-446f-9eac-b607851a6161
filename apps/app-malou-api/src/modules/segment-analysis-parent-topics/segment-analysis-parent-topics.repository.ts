import { singleton } from 'tsyringe';

import {
    DbId,
    EntityRepository,
    IReview,
    ISegmentAnalysis,
    ISegmentAnalysisParentTopics,
    ISegmentAnalysisParentTopicsWithTranslations,
    ReadPreferenceMode,
    SegmentAnalysisParentTopicModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import { PlatformKey, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { SemanticAnalysisTopicPruningPayloadTopic, SemanticAnalysisTopicPruningResponse } from ':modules/ai/interfaces/ai.interfaces';
import { SegmentAnalysisParentTopic } from ':modules/segment-analysis-parent-topics/entities/segment-analysis-parent-topic.entity';
import { Translations } from ':modules/translations/entities/translations.entity';

export type SegmentAnalysisPayload = Omit<ISegmentAnalysisParentTopics, 'createdAt' | 'updatedAt' | '_id'>;

export interface AggregatedParentTopicMentions {
    topicName: string;
    restaurantIds: string[];
    negativeMentions: number;
    positiveMentions: number;
}

export type ReviewWithSegmentAnalyses = Pick<
    IReview,
    | 'socialId'
    | 'key'
    | 'text'
    | 'rating'
    | 'lang'
    | 'ratingTags'
    | 'menuItemReviews'
    | 'reviewer'
    | 'socialAttachments'
    | 'socialCreatedAt'
> & {
    id: DbId;
    segmentAnalyses: Pick<ISegmentAnalysis, 'segment' | 'sentiment' | 'category' | 'aiFoundSegment'>[];
};

@singleton()
export class SegmentAnalysisParentTopicsRepository extends EntityRepository<ISegmentAnalysisParentTopics> {
    constructor() {
        super(SegmentAnalysisParentTopicModel);
    }

    async createSegmentAnalysisParentTopic(payload: {
        name: string;
        restaurantId: string;
        category: ReviewAnalysisTag;
        lastLinkedAt: Date;
        translationsId?: string;
    }): Promise<SegmentAnalysisParentTopic> {
        const newSegmentAnalysisParentTopic = await this.upsert({
            filter: {
                name: payload.name,
                restaurantId: toDbId(payload.restaurantId),
                category: payload.category,
            },
            update: {
                name: payload.name,
                restaurantId: toDbId(payload.restaurantId),
                category: payload.category,
                lastLinkedAt: payload.lastLinkedAt,
                isUserInput: true,
                ...(payload.translationsId ? { translationsId: toDbId(payload.translationsId) } : {}),
            },
            options: { new: true, populate: [{ path: 'translations' }] },
        });
        return this.toEntity(newSegmentAnalysisParentTopic);
    }

    async getByRestaurantIds(restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getByIds(ids: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                _id: { $in: toDbIds(ids) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getAiVisibleParentTopicIdsByRestaurantIds(restaurantIds: string[]): Promise<string[]> {
        const results = await this.find({
            filter: {
                restaurantId: { $in: toDbIds(restaurantIds) },
                isAiVisible: true,
            },
            projection: { _id: 1 },
            options: {
                lean: true,
            },
        });
        return results.map((doc) => doc._id.toString());
    }

    async getByRestaurantIdsAndName(topicName: string, restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                name: topicName,
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getByRestaurantIdsAndCategory(topicCategory: ReviewAnalysisTag, restaurantIds: string[]): Promise<SegmentAnalysisParentTopic[]> {
        const results = await this.find({
            filter: {
                category: topicCategory,
                restaurantId: { $in: toDbIds(restaurantIds) },
            },
            options: {
                populate: [{ path: 'translations' }],
                lean: true,
            },
        });
        return results.map(this.toEntity);
    }

    async getSegmentAnalysesParentTopicWithReviewCount(
        restaurantId: string
    ): Promise<(SemanticAnalysisTopicPruningPayloadTopic & { category: ReviewAnalysisTag })[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                    },
                },
                {
                    $lookup: {
                        from: 'segmentanalyses',
                        localField: '_id',
                        foreignField: 'segmentAnalysisParentTopicIds',
                        as: 'segments',
                        pipeline: [
                            {
                                $group: {
                                    _id: {
                                        segmentAnalysisParentTopicId: '$segmentAnalysisParentTopicIds',
                                    },
                                    numberOfLinkedReviews: {
                                        $sum: 1,
                                    },
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: '$segments',
                    },
                },
                {
                    $project: {
                        _id: 0,
                        topicId: '$_id',
                        title: '$name',
                        createdAt: 1,
                        sentiment: '$segments._id.sentiment',
                        lastLinkedAt: 1,
                        numberOfLinkedReviews: '$segments.numberOfLinkedReviews',
                        isAiVisible: 1,
                        isUserInput: 1,
                        isFavorite: 1,
                        category: 1,
                        subCategory: '$subcategory',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getSegmentAnalysesParentTopicWithReviewCount',
            }
        ).exec();
    }

    async updateManyAiVisible(topics: SemanticAnalysisTopicPruningResponse['topics']): Promise<void> {
        if (topics.length === 0) {
            return;
        }

        const bulkOperations = topics.map((topic) => ({
            updateOne: {
                filter: { _id: toDbId(topic.topicId) },
                update: {
                    isAiVisible: topic.isAiVisible,
                },
            },
        }));
        await SegmentAnalysisParentTopicModel.bulkWrite(bulkOperations);
    }

    async getAggregatedParentTopicMentionsSummary(filters: {
        restaurantIds: string[];
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Promise<AggregatedParentTopicMentions[]> {
        const results = await this.aggregate(
            [
                // Stage 1: Filter parent topics by restaurants
                {
                    $match: {
                        restaurantId: { $in: toDbIds(filters.restaurantIds) },
                    },
                },
                // Stage 2: Join with segment analyses and filter by date/platform
                {
                    $lookup: {
                        from: 'segmentanalyses',
                        localField: '_id',
                        foreignField: 'segmentAnalysisParentTopicIds',
                        as: 'segments',
                        pipeline: [
                            {
                                $match: {
                                    reviewSocialCreatedAt: {
                                        $gte: filters.startDate,
                                        $lte: filters.endDate,
                                    },
                                    platformKey: { $in: filters.platformKeys },
                                    sentiment: { $ne: ReviewAnalysisSentiment.NEUTRAL },
                                },
                            },
                            {
                                $project: {
                                    reviewSocialId: 1,
                                    sentiment: 1,
                                },
                            },
                        ],
                    },
                },
                // Stage 3: Flatten segments array to have one document per segment
                {
                    $unwind: '$segments',
                },
                // Stage 4: Deduplicate reviews per parent topic (same review counted once per parent topic per sentiment)
                {
                    $group: {
                        _id: {
                            parentTopicId: '$_id',
                            reviewSocialId: '$segments.reviewSocialId',
                            sentiment: '$segments.sentiment',
                        },
                        topicName: { $first: '$name' },
                        restaurantId: { $first: '$restaurantId' },
                    },
                },
                // Stage 5: Aggregate by topic name + review + sentiment to merge different parent topics with same name
                {
                    $group: {
                        _id: {
                            topicName: '$topicName',
                            reviewSocialId: '$_id.reviewSocialId',
                            sentiment: '$_id.sentiment',
                        },
                        restaurantIds: { $addToSet: '$restaurantId' },
                        parentTopicIds: { $addToSet: '$_id.parentTopicId' },
                    },
                },
                // Stage 6: Group by topic name to aggregate all reviews and collect restaurant/parent topic IDs arrays
                {
                    $group: {
                        _id: '$_id.topicName',
                        allRestaurantIds: { $push: '$restaurantIds' },
                        allParentTopicIds: { $push: '$parentTopicIds' },
                        reviews: {
                            $push: {
                                reviewSocialId: '$_id.reviewSocialId',
                                sentiment: '$_id.sentiment',
                            },
                        },
                    },
                },
                // Stage 7: Flatten nested arrays of restaurants into single unique arrays
                {
                    $project: {
                        _id: 1,
                        allRestaurantIds: 1,
                        allParentTopicIds: 1,
                        reviews: 1,
                        restaurantIds: {
                            $reduce: {
                                input: '$allRestaurantIds',
                                initialValue: [],
                                in: { $setUnion: ['$$value', '$$this'] },
                            },
                        },
                    },
                },
                // Stage 8: Format final output and count mentions by sentiment
                {
                    $project: {
                        _id: 0,
                        topicName: '$_id',
                        restaurantIds: 1,
                        negativeMentions: {
                            $size: {
                                $filter: {
                                    input: '$reviews',
                                    as: 'review',
                                    cond: { $eq: ['$$review.sentiment', 'negative'] },
                                },
                            },
                        },
                        positiveMentions: {
                            $size: {
                                $filter: {
                                    input: '$reviews',
                                    as: 'review',
                                    cond: { $eq: ['$$review.sentiment', 'positive'] },
                                },
                            },
                        },
                    },
                },
                // Stage 9: Sort by negative mentions (desc) then topic name (asc)
                {
                    $sort: {
                        negativeMentions: -1,
                        topicName: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getAggregatedParentTopicMentionsSummary',
            }
        );

        return results.map((result) => ({
            topicName: result.topicName,
            restaurantIds: result.restaurantIds.map((id: any) => id.toString()),
            negativeMentions: result.negativeMentions,
            positiveMentions: result.positiveMentions,
        }));
    }

    async getParentTopicReviewsWithAnalyses(filters: {
        topicName: string;
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Promise<ReviewWithSegmentAnalyses[]> {
        const results = await this.aggregate(
            [
                // Stage 1: Filter parent topics by restaurant and topic name
                {
                    $match: {
                        restaurantId: toDbId(filters.restaurantId),
                        name: filters.topicName,
                    },
                },
                // Stage 2: Join with segment analyses and filter by date/platform
                {
                    $lookup: {
                        from: 'segmentanalyses',
                        localField: '_id',
                        foreignField: 'segmentAnalysisParentTopicIds',
                        as: 'segments',
                        pipeline: [
                            {
                                $match: {
                                    reviewSocialCreatedAt: {
                                        $gte: filters.startDate,
                                        $lte: filters.endDate,
                                    },
                                    platformKey: { $in: filters.platformKeys },
                                    sentiment: { $ne: ReviewAnalysisSentiment.NEUTRAL },
                                },
                            },
                            {
                                $project: {
                                    reviewSocialId: 1,
                                    platformKey: 1,
                                    sentiment: 1,
                                    segment: 1,
                                    category: 1,
                                    aiFoundSegment: 1,
                                },
                            },
                        ],
                    },
                },
                // Stage 3: Flatten segments array
                {
                    $unwind: '$segments',
                },
                {
                    $lookup: {
                        from: 'reviews',
                        let: {
                            reviewSocialId: '$segments.reviewSocialId',
                            platformKey: '$segments.platformKey',
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            {
                                                $eq: ['$socialId', '$$reviewSocialId'],
                                            },
                                            { $eq: ['$key', '$$platformKey'] },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 1,
                                    key: 1,
                                    socialId: 1,
                                    text: 1,
                                    ratingTags: 1,
                                    menuItemReviews: 1,
                                    rating: 1,
                                    reviewer: 1,
                                    lang: 1,
                                    socialAttachments: 1,
                                    socialCreatedAt: 1,
                                },
                            },
                        ],
                        as: 'review',
                    },
                },
                {
                    $unwind: {
                        path: '$review',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $group: {
                        _id: {
                            reviewSocialId: '$review.socialId',
                            platformKey: '$review.key',
                        },
                        id: { $first: '$review._id' },
                        text: { $first: '$review.text' },
                        rating: { $first: '$review.rating' },
                        lang: { $first: '$review.lang' },
                        ratingTags: { $first: '$review.ratingTags' },
                        menuItemReviews: { $first: '$review.menuItemReviews' },
                        reviewer: { $first: '$review.reviewer' },
                        socialAttachments: { $first: '$review.socialAttachments' },
                        socialCreatedAt: { $first: '$review.socialCreatedAt' },
                        segmentAnalyses: {
                            $push: {
                                sentiment: '$segments.sentiment',
                                segment: '$segments.segment',
                                aiFoundSegment: '$segments.aiFoundSegment',
                                category: '$segments.category',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        id: 1,
                        socialId: '$_id.reviewSocialId',
                        key: '$_id.platformKey',
                        text: 1,
                        lang: 1,
                        socialCreatedAt: 1,
                        rating: 1,
                        reviewer: 1,
                        ratingTags: 1,
                        menuItemReviews: 1,
                        socialAttachments: 1,
                        segmentAnalyses: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY,
                comment: 'getParentTopicReviewsWithAnalyses',
            }
        );

        return results;
    }

    toEntity(document: ISegmentAnalysisParentTopicsWithTranslations): SegmentAnalysisParentTopic {
        return new SegmentAnalysisParentTopic({
            id: document._id.toString(),
            restaurantId: document.restaurantId.toString(),
            mergedParentTopicNames: document.mergedParentTopicNames,
            isUserInput: document.isUserInput,
            category: document.category,
            subcategory: document.subcategory,
            sentiment: document.sentiment || null,
            isFavorite: document.isFavorite,
            name: document.name,
            createdAt: document.createdAt,
            updatedAt: document.updatedAt,
            ...(document.translationsId ? { translationsId: document.translationsId.toString() } : {}),
            ...(document.translations
                ? {
                      translations: new Translations({
                          id: document.translations._id.toString(),
                          fr: document.translations.fr,
                          en: document.translations.en,
                          es: document.translations.es,
                          it: document.translations.it,
                          language: document.translations.language,
                          source: document.translations.source,
                      }),
                  }
                : {}),
        });
    }
}
