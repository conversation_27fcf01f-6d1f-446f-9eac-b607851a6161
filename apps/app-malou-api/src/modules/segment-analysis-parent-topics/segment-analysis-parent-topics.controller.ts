import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    CreateSegmentAnalysisParentTopicBodyDto,
    createSegmentAnalysisParentTopicBodyValidator,
    DeleteSegmentAnalysisParentTopicParamsDto,
    deleteSegmentAnalysisParentTopicParamsValidator,
    GetAggregatedParentTopicMentionsSummaryBodyDto,
    getAggregatedParentTopicMentionsSummaryBodyValidator,
    GetParentTopicReviewsWithAnalysesBodyDto,
    getParentTopicReviewsWithAnalysesBodyValidator,
    MergeSegmentAnalysisParentTopicsBodyDto,
    mergeSegmentAnalysisParentTopicsBodyValidator,
    ParentTopicMentionsSummaryDto,
    ParentTopicReviewsWithAnalysesDto,
    UpdateSegmentAnalysisParentTopicBodyDto,
    updateSegmentAnalysisParentTopicBodyValidator,
    UpdateSegmentAnalysisParentTopicParamsDto,
    updateSegmentAnalysisParentTopicParamsValidator,
} from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { CreateSegmentAnalysisParentTopicUseCase } from ':modules/segment-analysis-parent-topics/use-cases/create-segment-analysis-parent-topic/create-segment-analysis-parent-topic.use-case';
import { DeleteSegmentAnalysisParentTopicUseCase } from ':modules/segment-analysis-parent-topics/use-cases/delete-segment-analysis-parent-topic/delete-segment-analysis-parent-topic.use-case';
import { GetAggregatedParentTopicMentionsSummaryUseCase } from ':modules/segment-analysis-parent-topics/use-cases/get-aggregated-parent-topic-mentions-summary/get-aggregated-parent-topic-mentions-summary.use-case';
import { GetParentTopicReviewsWithAnalysesUseCase } from ':modules/segment-analysis-parent-topics/use-cases/get-parent-topic-reviews-with-analyses/get-parent-topic-reviews-with-analyses.use-case';
import { MergeSegmentAnalysisParentTopicsUseCase } from ':modules/segment-analysis-parent-topics/use-cases/merge-segment-analysis-parent-topics/merge-segment-analysis-parent-topics.use-case';
import { UpdateFavoriteSegmentAnalysisParentTopicUseCase } from ':modules/segment-analysis-parent-topics/use-cases/update-segment-analysis-parent-topic/update-segment-analysis-parent-topic.use-case';

@singleton()
export default class SegmentAnalysisParentTopicsController {
    constructor(
        private readonly _updateFavoriteSegmentAnalysisParentTopicUseCase: UpdateFavoriteSegmentAnalysisParentTopicUseCase,
        private readonly _mergeFavoriteSegmentAnalysisParentTopicsUseCase: MergeSegmentAnalysisParentTopicsUseCase,
        private readonly _createSegmentAnalysisParentTopicUseCase: CreateSegmentAnalysisParentTopicUseCase,
        private readonly _deleteSegmentAnalysisParentTopicUseCase: DeleteSegmentAnalysisParentTopicUseCase,
        private readonly _getAggregatedParentTopicMentionsSummaryUseCase: GetAggregatedParentTopicMentionsSummaryUseCase,
        private readonly _getParentTopicReviewsWithAnalysesUseCase: GetParentTopicReviewsWithAnalysesUseCase
    ) {}

    @Body(createSegmentAnalysisParentTopicBodyValidator)
    async handleCreateSegmentAnalysisParentTopic(
        req: Request<never, never, never, CreateSegmentAnalysisParentTopicBodyDto>,
        res: Response<ApiResultV2<any, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { category, name, restaurantId } = req.body;

            const result = await this._createSegmentAnalysisParentTopicUseCase.execute({
                category,
                name,
                restaurantId,
            });
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateSegmentAnalysisParentTopicParamsValidator)
    @Body(updateSegmentAnalysisParentTopicBodyValidator)
    async handleUpdateSegmentAnalysisParentTopic(
        req: Request<UpdateSegmentAnalysisParentTopicParamsDto, never, never, UpdateSegmentAnalysisParentTopicBodyDto>,
        res: Response<ApiResultV2<any, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;
            const { isFavorite } = req.body;

            const result = await this._updateFavoriteSegmentAnalysisParentTopicUseCase.execute(id, isFavorite);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(mergeSegmentAnalysisParentTopicsBodyValidator)
    async handleMergeSegmentAnalysisParentTopics(
        req: Request<never, never, never, MergeSegmentAnalysisParentTopicsBodyDto>,
        res: Response<ApiResultV2<any, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { topicIdToKeep, topicIdsToMerge, restaurantId } = req.body;

            await this._mergeFavoriteSegmentAnalysisParentTopicsUseCase.execute({
                topicIdToKeep,
                topicIdsToMerge,
                restaurantId,
            });
            return res.json();
        } catch (err) {
            next(err);
        }
    }

    @Params(deleteSegmentAnalysisParentTopicParamsValidator)
    async handleDeleteSegmentAnalysisParentTopic(
        req: Request<DeleteSegmentAnalysisParentTopicParamsDto, never, never>,
        res: Response<ApiResultV2<any, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { id } = req.params;

            const result = await this._deleteSegmentAnalysisParentTopicUseCase.execute(id);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getAggregatedParentTopicMentionsSummaryBodyValidator)
    async handleGetAggregatedParentTopicMentionsSummary(
        req: Request<never, never, never, GetAggregatedParentTopicMentionsSummaryBodyDto>,
        res: Response<ApiResultV2<ParentTopicMentionsSummaryDto[]>>,
        next: NextFunction
    ) {
        try {
            const result = await this._getAggregatedParentTopicMentionsSummaryUseCase.execute(req.body);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getParentTopicReviewsWithAnalysesBodyValidator)
    async handleGetParentTopicReviewsWithAnalyses(
        req: Request<never, never, never, GetParentTopicReviewsWithAnalysesBodyDto>,
        res: Response<ApiResultV2<ParentTopicReviewsWithAnalysesDto[]>>,
        next: NextFunction
    ) {
        try {
            const result = await this._getParentTopicReviewsWithAnalysesUseCase.execute(req.body);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }
}
