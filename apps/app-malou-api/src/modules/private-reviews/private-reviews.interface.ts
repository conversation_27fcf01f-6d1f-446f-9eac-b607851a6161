import {
    DbId,
    INfc,
    IPrivateReview,
    IReviewAnalysis,
    IScan,
    ISegmentAnalysisWithParentTopics,
    OverwriteOrAssign,
    PopulateBuilderHelper,
} from '@malou-io/package-models';
import { ReviewAnalysisSentiment } from '@malou-io/package-utils';

export interface PrivateReviewsSearchFilters {
    scanIds?: DbId[];
}

export type PrivateReviewWithTranslations = PopulateBuilderHelper<IPrivateReview, [{ path: 'client' }, { path: 'translations' }]>;

export type PrivateReviewWithScanWithNfc = OverwriteOrAssign<
    PrivateReviewWithTranslations,
    {
        scan: OverwriteOrAssign<IScan, { nfc: INfc }>;
        semanticAnalysis?: IReviewAnalysis;
        semanticAnalysisSegments: ISegmentAnalysisWithParentTopics[];
    }
>;

export type PrivateReviewWithSemanticAnalysis = OverwriteOrAssign<
    PrivateReviewWithTranslations,
    {
        semanticAnalysis?: IReviewAnalysis;
        semanticAnalysisSegments?: ISegmentAnalysisWithParentTopics[];
    }
>;

export type PrivateReviewWithSemanticAnalysisAndSentiment = OverwriteOrAssign<
    PrivateReviewWithSemanticAnalysis,
    {
        sentiment: ReviewAnalysisSentiment;
    }
>;
