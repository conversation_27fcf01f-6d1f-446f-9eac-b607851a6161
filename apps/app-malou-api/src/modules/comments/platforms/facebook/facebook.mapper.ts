import { singleton } from 'tsyringe';

import { IComment, toDbId } from '@malou-io/package-models';
import { platformsKeys } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';

import { FbComment } from './facebook.comment.interface';

@singleton()
export class FbCommentsMapper {
    mapToMalouComment(args: FbComment, platform: Platform): Omit<IComment, '_id' | 'createdAt' | 'updatedAt'> | null {
        const text = args.message;
        const socialCreatedAt = new Date(args.created_time);
        const socialId = args.id;
        const postSocialId = args.postId;
        const photoSocialId = args.photoSocialId;
        const reviewer = {
            socialId: args.from ? args.from.id : null,
            socialUrl: null,
            displayName: args.from ? args.from.name : null,
        };
        const likeCount = args.like_count ?? 0;
        const replies = (args.comments?.data ?? [])
            .filter((c) => !!c.from?.id && !!c.message)
            .map((c) => ({
                socialId: c.id,
                socialCreatedAt: new Date(c.created_time),
                text: c.message,
                reviewer: {
                    socialId: c.from.id,
                    displayName: c.from.name ?? null,
                    socialUrl: `${platformsKeys.FACEBOOK.baseUrl}/${c.from.id}`,
                },
                likeCount: c.like_count ?? 0,
            }));
        if (!socialId) {
            return null;
        }
        const mappedComment = {
            socialId,
            postSocialId,
            photoSocialId,
            text,
            likeCount,
            socialCreatedAt,
            reviewer,
            replies,
            hasBeenAnswered: replies.some((e) => e.reviewer?.socialId === platform.socialId),
        };
        return {
            restaurantId: toDbId(platform.restaurantId),
            platformId: platform._id,
            platformKey: platform.key,
            ...mappedComment,
        };
    }

    mapToPlatformReply(args: { text: string }) {
        return {
            message: args.text,
        };
    }
}
