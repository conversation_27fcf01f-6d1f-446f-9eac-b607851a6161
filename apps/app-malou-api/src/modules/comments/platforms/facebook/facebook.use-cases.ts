import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { IComment, ICommentMention } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isFulfilled, isRejected } from ':helpers/utils';
import { FbCommentsMapper } from ':modules/comments/platforms/facebook/facebook.mapper';
import * as facebookCredentialUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';

import { FbComment, FbPost } from './facebook.comment.interface';

const platformsRepository = container.resolve(PlatformsRepository);
const fbCommentsMapper = container.resolve(FbCommentsMapper);

export const getCommentsData = async (socialId: string, restaurantId: string): Promise<FbPostData[]> => {
    const platform = await platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FACEBOOK);
    const credentialId = platform?.credentials?.[0];
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    const result = await facebookCredentialUseCases.getPagePostsWithComments(credentialId, socialId);
    const posts = result?.posts?.data;
    if (!posts) {
        return [];
    }
    const promises = posts.map((post) => addCommentsFromCarouselPost(post, credentialId, socialId));
    return Promise.all(promises);
};

export const addCommentsFromCarouselPost = async (post: FbPostData, credentialId: string, pageId: string): Promise<FbPostData> => {
    const postAttachmentsData = post.attachments?.data?.[0];
    const isCarouselPost = postAttachmentsData?.subattachments?.data?.length;
    if (isCarouselPost) {
        const subattachments = postAttachmentsData.subattachments?.data;
        const photoResult = await Promise.allSettled(
            subattachments
                ?.filter((sub) => sub.target?.id)
                .map((sub) => facebookCredentialUseCases.getPhoto(credentialId, sub.target?.id as string, pageId)) ?? []
        );

        const failedPhotoResult = photoResult.filter(isRejected);
        if (failedPhotoResult.length) {
            logger.warn(
                '[ERROR_GETTING_FB_PHOTO]',
                subattachments?.map((sub) => sub.target?.id),
                failedPhotoResult
            );
        }

        const photosFromCarouselPost = photoResult.filter(isFulfilled).map((r) => r.value);
        if (photosFromCarouselPost?.length) {
            const commentsFromAdditionalPhotos = photosFromCarouselPost.map(
                (p) => p?.comments?.data?.map((c) => ({ ...c, photo_id: p.id })) || []
            );
            if (post.comments) {
                post.comments.data = post.comments.data?.concat(commentsFromAdditionalPhotos.flat());
            }
        }
    }
    return post;
};

export const getCommentData = async (comment: IComment) => {
    const platform = await platformsRepository.getPlatformById(comment.platformId.toString());
    const credentialId = platform?.credentials?.[0];
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    return facebookCredentialUseCases.getComment(credentialId, comment);
};

export const mapCommentsDataToMalou = (posts: FbPost[], platform: Platform) =>
    posts
        .map((post) => post.comments?.data?.map((c) => ({ ...c, postId: post.id, photoSocialId: post.photo_id })))
        .flat()
        .filter(isNotNil)
        .map((comment) => fbCommentsMapper.mapToMalouComment(comment, platform))
        .filter(isNotNil);

export const mapCommentDataToMalou = (commentData: FbComment, platform: Platform) =>
    fbCommentsMapper.mapToMalouComment(commentData, platform);

export const reply = async ({ comment, message }: { comment: Partial<ICommentMention>; message: string }) => {
    assert(comment.platformId, 'Missing platformId on comment');
    const platform = await platformsRepository.getPlatformById(comment.platformId.toString());
    const credentialId = platform?.credentials?.[0];
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    assert(platform.socialId, 'Missing socialId on platform');
    return facebookCredentialUseCases.replyToComment(credentialId, comment, message, platform.socialId);
};
