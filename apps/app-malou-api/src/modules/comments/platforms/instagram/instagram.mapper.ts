import { singleton } from 'tsyringe';

import { IComment, toDbId } from '@malou-io/package-models';
import { platformsKeys } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';

@singleton()
export class IgCommentsMapper {
    mapToMalouComment(comment, platform: Platform): Omit<IComment, '_id' | 'createdAt' | 'updatedAt'> | null {
        const { text } = comment;
        const socialId = comment.id;
        const likeCount = comment.like_count ?? 0;
        const socialCreatedAt = comment.timestamp;
        const postSocialId = comment.postId;
        const reviewer = {
            displayName: comment.username,
            socialUrl: `${platformsKeys.INSTAGRAM.baseUrl}/${comment.username}`,
        };

        const replies = (comment.replies?.data ?? [])
            .map((r) => ({
                socialId: r.id,
                socialCreatedAt: r.timestamp,
                likeCount: r.like_count,
                text: r.text,
                reviewer: {
                    socialId: r.user?.id ?? null,
                    displayName: r.username,
                    socialUrl: `${platformsKeys.INSTAGRAM.baseUrl}/${r.username}`,
                },
            }))
            .filter((v) => !!v);

        if (!socialId) {
            return null;
        }
        const mappedComment = {
            socialId,
            postSocialId,
            text,
            likeCount,
            socialCreatedAt,
            reviewer,
            replies,
            hasBeenAnswered: replies.some((e) => e.reviewer?.socialId === platform.socialId),
        };
        return {
            restaurantId: toDbId(platform.restaurantId),
            platformId: platform._id,
            platformKey: platform.key,
            ...mappedComment,
        };
    }

    mapToPlatformReply(args) {
        return {
            message: args.text,
        };
    }

    mapToMalouReply(args) {
        return {
            socialId: args.id,
            text: args.message,
            socialCreatedAt: args.created_time,
            reviewer: {
                socialId: args.from?.id ?? null,
                socialUrl: `${platformsKeys.INSTAGRAM.baseUrl}/${args.from?.username}`,
                displayName: args.from?.username,
            },
        };
    }
}
