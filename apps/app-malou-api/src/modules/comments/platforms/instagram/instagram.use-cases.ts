import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { IComment, ICommentMention } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { IgCommentsMapper } from ':modules/comments/platforms/instagram/instagram.mapper';
import * as facebookCredentialUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';

import { IgComment, IgPost } from './instagram.comments.interface';

const platformsRepository = container.resolve(PlatformsRepository);
const instagramCommentsMapper = container.resolve(IgCommentsMapper);

export const getCommentsData = async (socialId: string, restaurantId: string) => {
    const platform = await platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.INSTAGRAM);
    const credentialId = platform?.credentials?.[0];

    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }

    const result = await facebookCredentialUseCases.igGetPagePostsWithComments(credentialId, socialId);
    return result?.media?.data ?? [];
};

export const getCommentData = async (comment: IComment) => {
    const platform = await platformsRepository.getPlatformById(comment.platformId.toString());
    const credentialId = platform?.credentials?.[0];
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    return facebookCredentialUseCases.igGetComment(credentialId, comment);
};

export const mapCommentsDataToMalou = (medias: IgPost, platform: Platform) =>
    medias
        .map((media) => media.comments?.data?.map((c) => ({ ...c, postId: media.id })))
        .flat()
        .filter((v) => v)
        .map((comment) => instagramCommentsMapper.mapToMalouComment(comment, platform));

export const mapCommentDataToMalou = (commentData: IgComment, platform: Platform) =>
    instagramCommentsMapper.mapToMalouComment(commentData, platform);

export const mapReplyDataToMalou = (commentData: IgComment) => instagramCommentsMapper.mapToMalouReply(commentData);

export const reply = async ({ comment, message }: { comment: Partial<ICommentMention>; message: string }) => {
    assert(comment.platformId, 'Missing platformId on comment');
    const platform = await platformsRepository.getPlatformById(comment.platformId.toString());
    const credentialId = platform?.credentials?.[0];
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    assert(platform.socialId, 'Missing socialId on platform');
    return facebookCredentialUseCases.igReplyToComment(credentialId, comment, message, platform.socialId);
};
