import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { UpsertCommentsForPlatformUseCase } from ':modules/comments/use-cases/upsert-comments-for-platform/upsert-comments-for-platform.use-case';

const updatePlatformCommentsJobValidator = z.object({
    platformId: objectIdValidator,
    restaurantId: objectIdValidator,
});

@singleton()
export class UpdatePlatformCommentsJob extends GenericJobDefinition {
    constructor(private readonly _upsertCommentsForPlatformUseCase: UpsertCommentsForPlatformUseCase) {
        super({
            agendaJobName: AgendaJobName.UPDATE_PLATFORM_COMMENTS,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job): Promise<void> {
        const data = updatePlatformCommentsJobValidator.parse(job.attrs.data);
        const { platformId, restaurantId } = data;

        await this._upsertCommentsForPlatformUseCase.execute(platformId, restaurantId);
    }
}
