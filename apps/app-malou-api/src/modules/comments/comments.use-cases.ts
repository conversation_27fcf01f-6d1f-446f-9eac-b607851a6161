import { isNil, omitBy } from 'lodash';
import { autoInjectable, delay, inject } from 'tsyringe';

import { DbId, IComment, ID, IPlatform, toDbId } from '@malou-io/package-models';
import { getPlatformKeysWithComment, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { CommentsPipeline } from ':helpers/filters/comments-pipeline';
import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import * as fbCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import MentionsRepository from ':modules/mentions/mentions.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import { PlatformPostUseCases } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

import { CommentsRepository } from './comments.repository';
import { CommentMappingService } from './comments.service';
import * as fbCommentsUseCases from './platforms/facebook/facebook.use-cases';
import * as igCommentsUseCases from './platforms/instagram/instagram.use-cases';

@autoInjectable()
export class CommentsUseCases {
    constructor(
        @inject(delay(() => CommentsRepository))
        private readonly _commentsRepository: CommentsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        @inject(FacebookPostsUseCases)
        private readonly _facebookPostsUseCases: PlatformPostUseCases,
        @inject(InstagramPostsUseCases)
        private readonly _instagramPostsUseCases: PlatformPostUseCases,
        private readonly _commentsService: CommentMappingService,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository,
        private readonly _mentionsRepository: MentionsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    getPlatformCommentsUseCases = (key: PlatformKey) => {
        const platformUseCases = {
            [PlatformKey.INSTAGRAM]: igCommentsUseCases,
            [PlatformKey.FACEBOOK]: fbCommentsUseCases,
        };
        if (!Object.keys(platformUseCases).includes(key)) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                message: 'commentsUseCases not implemented for this platform',
                metadata: { platform: key },
            });
        }
        return platformUseCases[key];
    };

    /**
     * delete all comments in db that are not fetched by synchronization
     */
    private _clearPlatformComments = async (mappedComments: IComment[]): Promise<void> => {
        if (!mappedComments?.length) {
            return;
        }
        const oldestMappedComment = mappedComments.sort((a, b) => a.socialCreatedAt.getTime() - b.socialCreatedAt.getTime())[0];
        const filter = {
            restaurantId: oldestMappedComment.restaurantId,
            platformKey: oldestMappedComment.platformKey,
            socialCreatedAt: {
                $gte: oldestMappedComment.socialCreatedAt,
            },
            socialId: {
                $nin: mappedComments.map((c) => c.socialId),
            },
        };
        await this._commentsRepository.deleteMany({ filter });
    };

    getPlatformPostsUseCases = (key: string) => {
        const platformPostsUseCases = {
            instagram: this._instagramPostsUseCases,
            facebook: this._facebookPostsUseCases,
        };
        if (!Object.keys(platformPostsUseCases).includes(key)) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                message: 'postsUseCases not implemented for this platform',
                metadata: { platform: key },
            });
        }
        return platformPostsUseCases[key];
    };

    /**
     * Upserts all comments for specific platform. Will also upsert associated platform posts.
     */
    upsertCommentsForPlatform = async (postsData, platform: IPlatform) => {
        let posts;
        switch (platform.key) {
            case PlatformKey.FACEBOOK:
            case PlatformKey.INSTAGRAM:
                posts = postsData;
                break;
            default:
                throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                    message: 'upsertCommentsForPlatform not implemented for this platform.',
                    metadata: { platform: platform.key },
                });
        }
        const mappedComments = this.getPlatformCommentsUseCases(platform.key).mapCommentsDataToMalou(posts, platform);
        await this._clearPlatformComments(mappedComments);
        const mappedPosts = this.getPlatformPostsUseCases(platform.key).mapPostsDataToMalou(posts, platform);

        const promises = mappedComments.map(async (mappedComment) => {
            const commentToUpdate = await this._commentsService.getMappedCommentWithMalouInformations(
                mappedComment,
                platform._id.toString()
            );
            await this._commentsRepository.upsert({
                filter: {
                    socialId: mappedComment.socialId,
                    platformId: mappedComment.platformId,
                },
                update: commentToUpdate,
            });
        });
        return Promise.allSettled([...promises, ...mappedPosts.map((mp) => this._postsRepository.upsertPostByRestaurantIdAndSocialId(mp))]);
    };

    authorizeSubscribedApps = async (platforms: IPlatform[]) => {
        for (const platform of platforms) {
            if (!platform) {
                logger.error('[authorizeSubscribedApps] no_platform');
                return;
            }

            const credentialId = platform.credentials?.[0]?.toString();
            if (!credentialId) {
                logger.error('[authorizeSubscribedApps] No credentialId', { platform });
                return;
            }

            let fbPageId;
            if (platform.key === PlatformKey.FACEBOOK) {
                fbPageId = platform.socialId;
            }
            if (platform.key === PlatformKey.INSTAGRAM) {
                const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);
                fbPageId = credential.pageAccess?.find((at) => at.igPageId === platform.socialId)?.fbPageId;
            }
            if (!fbPageId) {
                logger.error('[authorizeSubscribedApps] No fbPageId', { platform });
                return;
            }
            const response = await fbCredentialsUseCases
                .subscribeToWebhooks(credentialId, fbPageId, 'feed,messages,message_echoes,message_reactions,mention')
                .catch((error) => {
                    logger.error('[authorizeSubscribedApps] Failed', { platform, error });
                    return null;
                });
            if (response) {
                logger.info('[authorizeSubscribedApps] Success', { platform });
                return;
            }
        }
    };

    getMapKeySocialLink = async (restaurantId: DbId): Promise<Record<string, string>> => {
        const platforms = await this._platformsRepository.find({ filter: { restaurantId }, options: { lean: true } });
        const socialPlatforms = platforms.filter((platform) => getPlatformKeysWithComment().includes(platform.key));
        return omitBy(Object.fromEntries(new Map(socialPlatforms.map((sp) => [sp.key, sp.socialLink]))), isNil) as Record<string, string>;
    };

    getAvailableCommentPlatforms = async (restaurantId: ID) => {
        const platforms = await this._platformsRepository.find({ filter: { restaurantId: toDbId(restaurantId) }, options: { lean: true } });
        const commentsPlatforms = getPlatformKeysWithComment();
        return platforms.filter((platform) => commentsPlatforms.includes(platform.key));
    };

    reply = async ({ user, commentId, message }: { user: { _id: DbId }; commentId: string; message: string }) => {
        const comment = await this._commentsRepository.findOne({
            filter: { _id: commentId },
            options: { lean: true, populate: [{ path: 'post' }] },
        });
        if (!comment) {
            throw new MalouError(MalouErrorCode.COMMENT_NOT_FOUND, { message: 'comment not found' });
        }
        const res = this.getPlatformCommentsUseCases(comment.platformKey).reply({
            userId: user._id,
            comment,
            message,
        });

        await this._commentsRepository.findOneAndUpdate({
            filter: { _id: commentId },
            update: {
                hasBeenAnswered: true,
            },
        });

        return res;
    };

    refreshComment = async (commentId: string, replySocialId: string, malouAuthorId: DbId) => {
        const comment = await this._commentsRepository.findOneOrFail({ filter: { _id: toDbId(commentId) }, options: { lean: true } });
        const commentData = await this.getPlatformCommentsUseCases(comment.platformKey).getCommentData(comment);
        const platform = await this._platformsRepository.findOne({ filter: { _id: comment.platformId }, options: { lean: true } });
        const mappedData = this.getPlatformCommentsUseCases(comment.platformKey).mapCommentDataToMalou(commentData, platform);
        delete mappedData.platformId;
        delete mappedData.restaurantId;
        const mappedRepliesWithMalouFields = mappedData.replies?.map((reply) => {
            const replyFromDb = comment.replies.find((r) => r.socialId === reply.socialId) ?? {};
            const replyWithMalouFields = { ...replyFromDb, ...reply };
            return reply.socialId === replySocialId && malouAuthorId
                ? {
                      ...replyWithMalouFields,
                      malouAuthorId,
                  }
                : replyWithMalouFields;
        });
        await this._commentsRepository.updateMany({
            filter: {
                socialId: mappedData.socialId,
                platformKey: comment.platformKey,
            },
            update: {
                ...mappedData,
                replies: mappedRepliesWithMalouFields,
            },
        });

        return this._commentsRepository.findOne({ filter: { _id: commentId } });
    };

    startUpdateCommentsForRestaurant = async (restaurantId: ID): Promise<void> => {
        const availablePlatforms = await this.getAvailableCommentPlatforms(restaurantId);
        try {
            await this.authorizeSubscribedApps(availablePlatforms);
        } catch (e: any) {
            if (e.message.match(/messages field/)) {
                logger.warn('[AUTHORIZE_SUBSCRIBED_APPS] not subscribed to messages');
            } else {
                throw e;
            }
        }
        const [restaurant] = await this._restaurantsRepository.find({
            filter: {
                _id: toDbId(restaurantId),
            },
        });
        this._restaurantsRepository
            .startUpdateComments(
                restaurantId,
                availablePlatforms.map((p) => p.key)
            )
            .catch((e) => {
                logger.error('[START_UPDATE_COMMENTS_ERROR] - ', e);
            });
        for (const platform of availablePlatforms) {
            const agenda = await this._agendaSingleton.getInstance();
            await agenda.now(AgendaJobName.UPDATE_PLATFORM_COMMENTS, {
                platformId: platform._id.toString(),
                restaurantId: restaurant._id.toString(),
            });
        }
    };

    updateCommentsForPlatform = async (platformId: string, restaurantId: string): Promise<void> => {
        const [platform, restaurant] = await Promise.all([
            this._platformsRepository.findOneOrFail({ filter: { _id: toDbId(platformId) }, options: { lean: true } }),
            this._restaurantsRepository.findOneOrFail({ filter: { _id: toDbId(restaurantId) } }),
        ]);

        // Started to refactor this method for Facebook, next to be done is Instagram
        // See UpsertCommentsForPlatformUseCase for more details
        if (platform.key !== PlatformKey.INSTAGRAM) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                message: 'updateCommentsForPlatform not implemented for this platform.',
                metadata: { platform: platform.key },
            });
        }

        try {
            const commentsUseCases = this.getPlatformCommentsUseCases(platform.key);
            if (!commentsUseCases) {
                return;
            }
            const result = await commentsUseCases.getCommentsData(platform.socialId, restaurant._id.toString());
            try {
                await this.upsertCommentsForPlatform(result, platform);
                await this._restaurantsRepository.findOneAndUpdate({
                    filter: { _id: restaurantId },
                    update: {
                        [`currentState.comments.fetched.${platform.key}`]: {
                            status: 'success',
                            lastTried: new Date(),
                            error: null,
                        },
                    },
                });
            } catch (error) {
                logger.warn('error upsert comments', platform.key, error);
                await this._restaurantsRepository.findOneAndUpdate({
                    filter: { _id: restaurantId },
                    update: {
                        [`currentState.comments.fetched.${platform.key}`]: {
                            status: 'error',
                            lastTried: new Date(),
                            error: 'upsert_failed',
                        },
                    },
                });
            }
        } catch (e) {
            logger.warn('[COMMENTS_UPDATE] - Error trying to fetch comments', platform.key, e);
            await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantId },
                update: {
                    [`currentState.comments.fetched.${platform.key}`]: {
                        status: 'error',
                        lastTried: new Date(),
                        error: e instanceof Error ? e.message : 'fetch_failed',
                    },
                },
            });
        }
    };

    getMapKeySocialId = async (restaurantId: DbId): Promise<Record<string, string>> => {
        const platforms = await this._platformsRepository.find({ filter: { restaurantId }, options: { lean: true } });
        const socialPlatforms = platforms.filter((platform) => getPlatformKeysWithComment().includes(platform.key));
        return omitBy(Object.fromEntries(new Map(socialPlatforms.map((sp) => [sp.key, sp.socialId]))), isNil) as Record<string, string>;
    };

    /**
     *
     * @param {string} restaurantId
     * @param {CommentFilters} filters
     * @param {Pagination} pagination
     * @param {Object} query {answered, notAnswered, ...}
     */
    buildPipeline = async (restaurantId, filters, pagination, query) => {
        const mapPlatformKeySocialId = await this.getMapKeySocialId(restaurantId);
        const mapPlatformKeySocialLink = await this.getMapKeySocialLink(restaurantId);
        const filtersQuery = filters.buildQuery();
        const pipelineEntity = new CommentsPipeline({
            filters: filtersQuery,
            pagination,
            ...query,
            mapPlatformKeySocialId,
            mapPlatformKeySocialLink,
        });
        return pipelineEntity.buildCommentListPipeline();
    };

    toggleArchivedCommentsForPost = async (postSocialId: string, archived: boolean, restaurantId: DbId): Promise<void> => {
        await this._commentsRepository.updateMany({
            filter: { postSocialId, restaurantId },
            update: {
                archived,
            },
        });

        await this._mentionsRepository.updateMany({ filter: { 'post.socialId': postSocialId, restaurantId }, update: { archived } });
    };

    /**
     *
     * @param {Comment[]} comments
     * @param {{pageNumber,pageSize,total}} pagination
     */
    paginateCommentsPostMode = (comments, pagination) => {
        const maxPosts = pagination.pageSize;
        const cuttingIndex = [0];
        let postSocialIds = {};
        for (let i = 0; i < comments.length; i++) {
            const comment = comments[i];
            if (!postSocialIds[comment.postSocialId]) {
                postSocialIds[comment.postSocialId] = 1;
            }
            if (Object.keys(postSocialIds).length > maxPosts) {
                cuttingIndex.push(i);
                postSocialIds = {};
            }
        }
        return comments.slice(cuttingIndex[pagination.pageNumber], cuttingIndex[pagination.pageNumber + 1] || comments.length);
    };

    /**
     *
     * @param {Comment[]} comments
     */
    getTotalPostsCount = (comments) => {
        const postSocialIds = {};
        for (const comment of comments) {
            if (!postSocialIds[comment.postSocialId]) {
                postSocialIds[comment.postSocialId] = 1;
            }
        }
        return Object.keys(postSocialIds).length;
    };

    getCommentById = async (commentId: DbId) =>
        this._commentsRepository.findOne({
            filter: { _id: commentId },
            options: { populate: [{ path: 'post' }], lean: true },
        });

    async getRestaurantCommentsPaginated(pipeline: any) {
        return this._commentsRepository.aggregate(pipeline, { comment: 'getRestaurantCommentsPaginated' });
    }

    async getPostForComments(socialId: string, platformId: DbId) {
        return this._postsRepository.findOne({ filter: { socialId, platformId }, options: { lean: true } });
    }

    async updateCommentById(commentId: string, update: any) {
        return this._commentsRepository.findOneAndUpdate({ filter: { _id: commentId }, update, options: { lean: true } });
    }

    async updateMentionById(mentionId: string, update: any) {
        return this._mentionsRepository.findOneAndUpdate({ filter: { _id: toDbId(mentionId) }, update, options: { lean: true } });
    }

    async deleteCommentById(commentId: string) {
        return this._commentsRepository.deleteOne({ filter: { _id: commentId } });
    }
}
