import { singleton } from 'tsyringe';

import { IComment, toDbId } from '@malou-io/package-models';

import { CommentsRepository } from './comments.repository';

@singleton()
export class CommentMappingService {
    constructor(private readonly _commentsRepository: CommentsRepository) {}

    async getMappedCommentWithMalouInformations(
        mappedComment: Omit<IComment, '_id' | 'createdAt' | 'updatedAt'>,
        platformId: string
    ): Promise<Omit<IComment, '_id' | 'createdAt' | 'updatedAt'>> {
        const existingCommentInDb = await this._commentsRepository.findOne({
            filter: { socialId: mappedComment.socialId, platformId: toDbId(platformId) },
            options: { lean: true },
        });
        if (!existingCommentInDb) {
            return mappedComment;
        }
        const updatedReplies = (mappedComment.replies || []).map((reply) => {
            const associatedReplyInDb = existingCommentInDb.replies.find((r) => r.socialId === reply.socialId) ?? {};
            return {
                ...associatedReplyInDb,
                ...reply,
            };
        });

        return { ...mappedComment, replies: updatedReplies };
    }
}
