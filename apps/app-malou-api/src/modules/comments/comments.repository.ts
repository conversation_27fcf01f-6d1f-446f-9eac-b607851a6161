import { omit } from 'lodash';
import { singleton } from 'tsyringe';

import { CommentModel, DbId, EntityRepository, IComment, ID, IPost, toDbId } from '@malou-io/package-models';
import { DateFilter } from '@malou-io/package-utils';

import { CommentsFilters, ICommentsFilters } from ':helpers/filters/comments-filters';
import { CommentsPipeline } from ':helpers/filters/comments-pipeline';
import { Pagination } from ':helpers/pagination';

/**
 * Mention or Comment
 */
export interface IModerationTarget extends IComment {
    isMention: boolean;
    mentionType: 'post' | 'comment';
}
export interface IPostWithComments extends IPost {
    areAllCommentsArchived: boolean;
    unansweredCommentCount: number;
    unansweredMentionCount: number;
}

@singleton()
export class CommentsRepository extends EntityRepository<IComment> {
    constructor() {
        super(CommentModel);
    }

    async getRestaurantCommentsByPost({
        restaurantId,
        mapPlatformKeySocialId,
        mapPlatformKeySocialLink,
        filters,
        pagination,
    }: {
        restaurantId: DbId;
        mapPlatformKeySocialId: Record<string, string>;
        mapPlatformKeySocialLink: Record<string, string>;
        filters: ICommentsFilters;
        pagination: Pagination;
    }): Promise<{ posts: IPostWithComments[]; total: number }> {
        const filtersExceptText = omit(filters, 'text'); // We need to handle the text filter differently than usual because the search needs to be the first step of the aggregation
        const filtersQuery = new CommentsFilters({ ...filtersExceptText, restaurantId }).buildQuery();
        const pipeline = new CommentsPipeline({
            ...filters,
            filters: filtersQuery,
            pagination,
            mapPlatformKeySocialId,
            mapPlatformKeySocialLink,
            restaurantId,
        }).buildCommentsByPostPipeline();
        const [{ posts, totalCount }] = await this.aggregate(pipeline, { comment: 'getRestaurantCommentsByPost' });
        const total = totalCount[0]?.count ?? 0;
        return { posts, total };
    }

    async getCommentAndMentionUnansweredCount({
        restaurantId,
        mapPlatformKeySocialId,
        mapPlatformKeySocialLink,
        startDate,
    }: {
        restaurantId: DbId;
        mapPlatformKeySocialId: Record<string, string>;
        mapPlatformKeySocialLink: Record<string, string>;
        startDate?: Date;
    }): Promise<{
        unansweredCommentCount: number;
        unansweredMentionCount: number;
    }> {
        const filtersQuery = new CommentsFilters({
            archived: false,
            restaurantId,
            startDate,
        }).buildQuery();
        const pipeline = new CommentsPipeline({
            filters: filtersQuery,
            answered: 'false',
            notAnswered: 'true',
            withoutOwnComment: 'true',
            mapPlatformKeySocialId,
            mapPlatformKeySocialLink,
            restaurantId,
        }).buildCommentsAndMentionsCountPipeline();
        const result = await this.aggregate(pipeline, { comment: 'getCommentAndMentionUnansweredCount' });
        const unansweredCommentCount = result?.[0]?.unansweredComments?.[0]?.unansweredCount ?? 0;
        const unansweredMentionCount = result?.[0]?.unansweredMentions?.[0]?.unansweredCount ?? 0;
        return { unansweredCommentCount, unansweredMentionCount };
    }

    async getRestaurantCommentsAndMentionsList({
        restaurantId,
        mapPlatformKeySocialId,
        mapPlatformKeySocialLink,
        filters,
        pagination,
    }: {
        restaurantId: DbId;
        mapPlatformKeySocialId: Record<string, string>;
        mapPlatformKeySocialLink: Record<string, string>;
        filters: ICommentsFilters;
        pagination: Pagination;
    }): Promise<{ comments: IModerationTarget[]; totalCount: number; commentCount: number; mentionCount: number }> {
        const filtersQuery = new CommentsFilters({ ...filters, restaurantId }).buildQuery();
        const pipeline = new CommentsPipeline({
            ...filters,
            filters: filtersQuery,
            answered: filters.answered ? 'true' : 'false',
            notAnswered: filters.notAnswered ? 'true' : 'false',
            withoutOwnComment: filters.withoutOwnComment ? 'true' : 'false',
            pagination,
            mapPlatformKeySocialId,
            mapPlatformKeySocialLink,
            restaurantId,
        }).buildCommentAndMentionListPipeline();

        const [{ comments, totalCount, commentCount, mentionCount }] = await this.aggregate(pipeline, {
            comment: 'getRestaurantCommentsAndMentionsList',
        });
        const totalCountNumber = totalCount[0]?.count ?? 0;
        const commentCountNumber = commentCount[0]?.count ?? 0;
        const mentionCountNumber = mentionCount[0]?.count ?? 0;
        return { comments, totalCount: totalCountNumber, commentCount: commentCountNumber, mentionCount: mentionCountNumber };
    }

    async getCommentsAndMentionsByPostSocialId({
        restaurantId,
        postSocialId,
        mapPlatformKeySocialId,
        mapPlatformKeySocialLink,
        filters,
    }: {
        restaurantId: DbId;
        postSocialId: string;
        mapPlatformKeySocialId: Record<string, string>;
        mapPlatformKeySocialLink: Record<string, string>;
        filters: ICommentsFilters;
    }): Promise<IModerationTarget[]> {
        const filtersExceptText = { ...omit(filters, 'text'), postSocialId, restaurantId }; // We need to handle the text filter differently than usual because the search needs to be the first step of the aggregation
        const filtersQuery = new CommentsFilters(filtersExceptText).buildQuery();
        const pipeline = new CommentsPipeline({
            ...filters,
            filters: filtersQuery,
            answered: filters.answered ? 'true' : 'false',
            notAnswered: filters.notAnswered ? 'true' : 'false',
            withoutOwnComment: filters.withoutOwnComment ? 'true' : 'false',
            mapPlatformKeySocialId,
            mapPlatformKeySocialLink,
            restaurantId,
        }).buildCommentAndMentionListPipeline();
        const [{ comments }] = await this.aggregate(pipeline, { comment: 'getCommentsAndMentionsByPostSocialId' });
        return comments;
    }

    async getCommentsByRestaurantIdAndPeriod(restaurantId: ID, dateFilter: DateFilter): Promise<IComment[]> {
        return this.find({
            filter: {
                restaurantId,
                socialCreatedAt: dateFilter,
            },
        });
    }

    async getCommentById(commentId: string): Promise<IComment | null> {
        return this.findOne({ filter: { _id: toDbId(commentId) }, options: { lean: true } });
    }

    async clearPlatformComments({
        restaurantId,
        platformKey,
        socialIds,
        fromDate,
    }: {
        restaurantId: string;
        platformKey: string;
        socialIds: string[];
        fromDate: Date;
    }): Promise<void> {
        const filter = {
            restaurantId: toDbId(restaurantId),
            platformKey,
            socialCreatedAt: {
                $gte: fromDate,
            },
            socialId: {
                $nin: socialIds,
            },
        };
        await this.deleteMany({ filter });
    }
}

// Keep because used in files that are not yet updated yet to the new DI system
export default new CommentsRepository();
