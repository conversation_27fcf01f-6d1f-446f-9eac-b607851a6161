import { singleton } from 'tsyringe';

import { IUpsertCommentsUseCase } from ':modules/comments/use-cases/upsert-comments-for-platform/upsert-comments-for-platform.use-case';
import { Platform } from ':modules/platforms/platforms.entity';

@singleton()
export class UpsertCommentsForInstagramUseCase implements IUpsertCommentsUseCase {
    async execute(_platform: Platform): Promise<void> {
        throw new Error('Method not implemented.');
    }
}
