import { singleton } from 'tsyringe';

import { IComment } from '@malou-io/package-models';
import { isFulfilled, isNotNil, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isRejected } from ':helpers/utils';
import { CommentsRepository } from ':modules/comments/comments.repository';
import { CommentMappingService } from ':modules/comments/comments.service';
import { FbCommentsMapper } from ':modules/comments/platforms/facebook/facebook.mapper';
import { IUpsertCommentsUseCase } from ':modules/comments/use-cases/upsert-comments-for-platform/upsert-comments-for-platform.use-case';
import { Platform } from ':modules/platforms/platforms.entity';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { FacebookPostService } from ':modules/posts/platforms/facebook/facebook-post.service';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import { FbComment } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class UpsertCommentsForFacebookUseCase implements IUpsertCommentsUseCase {
    constructor(
        private readonly _facebookCommentsMapper: FbCommentsMapper,
        private readonly _commentsRepository: CommentsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _commentsService: CommentMappingService,
        private readonly _facebookPostsUseCases: FacebookPostsUseCases,
        private readonly _facebookPostService: FacebookPostService
    ) {}

    async execute(platform: Platform): Promise<void> {
        let fbPosts: FbPostData[];
        let mappedComments: Omit<IComment, '_id' | 'createdAt' | 'updatedAt'>[];
        try {
            // Comments are fetched from the posts
            fbPosts = await this._getCommentsData(platform);

            // Extract the comments from the posts
            mappedComments = this._getMalouCommentsFromPosts(fbPosts, platform);

            if (mappedComments.length === 0) {
                return;
            }
        } catch (error: unknown) {
            logger.error('Error in UpsertCommentsForFacebookUseCase', { error });
            throw new MalouError(MalouErrorCode.FETCH_COMMENTS_FAILED, {
                message: 'Error fetching comments for Facebook',
                metadata: { platformId: platform.id, error },
            });
        }

        try {
            // Delete comments that are not in the fetched comments anymore
            const oldestComment = mappedComments.sort((a, b) => a.socialCreatedAt.getTime() - b.socialCreatedAt.getTime())[0];
            await this._commentsRepository.clearPlatformComments({
                restaurantId: platform.restaurantId.toString(),
                platformKey: platform.key,
                socialIds: mappedComments.map((c) => c.socialId),
                fromDate: oldestComment.socialCreatedAt,
            });

            // Upsert the comments
            const upsertCommentPromises = mappedComments.map(async (mappedComment) => {
                const commentToUpdate = await this._commentsService.getMappedCommentWithMalouInformations(
                    mappedComment,
                    platform._id.toString()
                );
                await this._commentsRepository.upsert({
                    filter: {
                        socialId: mappedComment.socialId,
                        platformId: mappedComment.platformId,
                    },
                    update: commentToUpdate,
                });
            });

            // Upsert the posts
            const mappedPosts = this._facebookPostsUseCases.mapPostsDataToMalou(fbPosts, platform);
            const upsertPostPromises = mappedPosts.map(async (mp) => await this._postsRepository.upsertPostByRestaurantIdAndSocialId(mp));

            await Promise.allSettled([...upsertCommentPromises, ...upsertPostPromises]);
        } catch (error: unknown) {
            logger.error('Error in UpsertCommentsForFacebookUseCase', { error });
            throw new MalouError(MalouErrorCode.UPSERT_COMMENTS_FAILED, {
                message: 'Error upserting comments for Facebook',
                metadata: { platformId: platform.id, error },
            });
        }
    }

    private async _getCommentsData(platform: Platform): Promise<FbPostData[]> {
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_NOT_FOUND, { metadata: { platformId: platform.id } });
        }
        const socialId = platform.socialId;
        if (!socialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_MISSING_SOCIAL_ID, { metadata: { platformId: platform.id } });
        }
        const posts = await this._facebookPostService.fetchPagePostsWithComments({ credentialId, pageId: socialId });
        const promises = posts.map((post) => this._addCommentsFromCarouselPost(post, socialId, platform));
        return Promise.all(promises);
    }

    private _getMalouCommentsFromPosts(fbPosts: FbPostData[], platform: Platform): Omit<IComment, '_id' | 'createdAt' | 'updatedAt'>[] {
        const fbComments = fbPosts
            .map((post) => post.comments?.data?.map((c) => ({ ...c, postId: post.id, photoSocialId: post.photo_id })) ?? [])
            .flat()
            .filter(isNotNil);
        // Map them
        const mappedComments = fbComments
            .map((fbComment) => this._facebookCommentsMapper.mapToMalouComment(fbComment, platform))
            .filter(isNotNil);
        return mappedComments;
    }

    private async _addCommentsFromCarouselPost(post: FbPostData, pageId: string, platform: Platform): Promise<FbPostData> {
        const postAttachmentsData = post.attachments?.data?.[0];
        const subattachments = postAttachmentsData?.subattachments?.data;
        if (subattachments && subattachments.length > 0) {
            const photoCommentsResult = await Promise.allSettled(
                subattachments
                    ?.filter((sub) => sub.target?.id)
                    .map((sub) =>
                        this._facebookPostService.fetchCommentsFromPhoto({ photoId: sub.target?.id as string, platform, pageId })
                    ) ?? []
            );

            const failedPhotoWithCommentsResult = photoCommentsResult.filter(isRejected);
            if (failedPhotoWithCommentsResult.length) {
                logger.warn(
                    '[ERROR_GETTING_FB_PHOTO]',
                    subattachments?.map((sub) => sub.target?.id),
                    failedPhotoWithCommentsResult
                );
            }

            const photoWithCommentsFromCarouselPost = photoCommentsResult
                .map((r) => (isFulfilled(r) ? r.value : undefined))
                .filter(isNotNil);
            if (photoWithCommentsFromCarouselPost.length) {
                const commentsFromAdditionalPhotos: (FbComment & { photo_id: string })[][] = photoWithCommentsFromCarouselPost.map(
                    (photo) => photo.comments?.data?.map((c) => ({ ...c, photo_id: photo.id })) ?? []
                );
                if (post.comments) {
                    post.comments.data = (post.comments.data ?? []).concat(commentsFromAdditionalPhotos.flat());
                }
            }
        }
        return post;
    }
}
