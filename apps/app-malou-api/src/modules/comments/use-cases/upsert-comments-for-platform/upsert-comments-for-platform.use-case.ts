import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { CommentsUseCases } from ':modules/comments/comments.use-cases';
import { UpsertCommentsForFacebookUseCase } from ':modules/comments/use-cases/upsert-comments-for-platform/providers/facebook/upsert-comments-for-facebook.use-case';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

export interface IUpsertCommentsUseCase {
    execute(platform: Platform): Promise<void>;
}

@singleton()
export class UpsertCommentsForPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(UpsertCommentsForFacebookUseCase) private readonly _upsertCommentsForFacebookUseCase: IUpsertCommentsUseCase,
        // @inject(UpsertCommentsForInstagramUseCase) private readonly _upsertCommentsForInstagramUseCase: IUpsertCommentsUseCase // TODO uncomment when ready
        private readonly _commentsUseCase: CommentsUseCases
    ) {}

    async execute(platformId: string, restaurantId: string): Promise<void> {
        logger.info('Starting UpsertCommentsForPlatformUseCase', { platformId, restaurantId });

        const [platform, _restaurant] = await Promise.all([
            this._platformsRepository.getPlatformById(platformId),
            this._restaurantsRepository.findOneOrFail({ filter: { _id: toDbId(restaurantId) } }),
        ]);

        assert(platform, 'Platform not found');

        let upsertCommentsUseCase: IUpsertCommentsUseCase;
        switch (platform.key) {
            case PlatformKey.FACEBOOK:
                upsertCommentsUseCase = this._upsertCommentsForFacebookUseCase;
                break;
            case PlatformKey.INSTAGRAM:
                // upsertCommentsUseCase = this._upsertCommentsForInstagramUseCase; // TODO uncomment when ready
                return this._commentsUseCase.updateCommentsForPlatform(platformId, restaurantId);
                break;
            default:
                throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                    message: 'UpsertCommentsForPlatformUseCase not implemented for this platform.',
                    metadata: { platform: platform.key },
                });
        }

        try {
            await upsertCommentsUseCase.execute(platform);
            await this._handleSuccess(restaurantId, platform.key);
        } catch (error: unknown) {
            await this._handleError(error, restaurantId, platform.key);
        }

        logger.info('Finished UpsertCommentsForPlatformUseCase', { platformId, restaurantId });
    }

    private async _handleSuccess(restaurantId: string, platformKey: PlatformKey): Promise<void> {
        await this._restaurantsRepository.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: {
                [`currentState.comments.fetched.${platformKey}`]: {
                    status: 'success',
                    lastTried: new Date(),
                    error: null,
                },
            },
        });
    }

    private async _handleError(error: unknown, restaurantId: string, platformKey: PlatformKey): Promise<void> {
        logger.error('Error in UpsertCommentsForPlatformUseCase', { error });

        if (error instanceof MalouError) {
            if (error.malouErrorCode === MalouErrorCode.FETCH_COMMENTS_FAILED) {
                await this._restaurantsRepository.findOneAndUpdate({
                    filter: { _id: toDbId(restaurantId) },
                    update: {
                        [`currentState.comments.fetched.${platformKey}`]: {
                            status: 'error',
                            lastTried: new Date(),
                            error: 'fetch_failed',
                        },
                    },
                });
                return;
            }
            if (error.malouErrorCode === MalouErrorCode.UPSERT_COMMENTS_FAILED) {
                await this._restaurantsRepository.findOneAndUpdate({
                    filter: { _id: toDbId(restaurantId) },
                    update: {
                        [`currentState.comments.fetched.${platformKey}`]: {
                            status: 'error',
                            lastTried: new Date(),
                            error: 'upsert_failed',
                        },
                    },
                });
                return;
            }
        }
        await this._restaurantsRepository.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: {
                [`currentState.comments.fetched.${platformKey}`]: {
                    status: 'error',
                    lastTried: new Date(),
                    error: 'unknown_error',
                },
            },
        });
    }
}
