import { PipelineStage } from 'mongoose';
import { autoInjectable, delay, inject } from 'tsyringe';

import { UpdateConversationsBodyDto } from '@malou-io/package-dto';
import { IConversation, IMessage, IPlatform, IRestaurant, newDbId, toDbId } from '@malou-io/package-models';
import { ConversationStatus, errorReplacer, MalouErrorCode, MessageStatus, PlatformKey } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { BasicFilters } from ':helpers/filters/basic-filters';
import { logger } from ':helpers/logger';
import { Pagination } from ':helpers/pagination';
import { CommentsUseCases } from ':modules/comments/comments.use-cases';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

import { ConversationMapper } from './messages.conversation-mapper';
import { ConversationWithMessages } from './messages.interface';
import { ConversationsRepository, MessagesRepository } from './messages.repository';
import { FacebookConversationMapper } from './platforms/facebook/facebook-conversation-mapper';
import { InstagramConversationMapper } from './platforms/instagram/instagram-conversation-mapper';

@autoInjectable()
export default class MessagesUseCases {
    constructor(
        @inject(delay(() => MessagesRepository)) private readonly _messagesRepository: MessagesRepository,
        @inject(delay(() => ConversationsRepository)) private readonly _conversationsRepository: ConversationsRepository,
        @inject(delay(() => PlatformsRepository)) private readonly _platformsRepository: PlatformsRepository,
        @inject(delay(() => RestaurantsRepository)) private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(delay(() => CommentsUseCases)) private readonly _commentsUseCases: CommentsUseCases,
        private _agendaSingleton: AgendaSingleton
    ) {}

    getConversationsPaginated = async (
        filters: BasicFilters,
        pagination: Pagination
    ): Promise<{ conversationsWithMessages: ConversationWithMessages[]; totalCount: number }> => {
        const filterQuery = filters.buildQuery({ filterType: '' });
        const searchBy = Object.assign({}, ...filterQuery.$and);
        const { pageSize, pageNumber } = pagination;
        const sortBy = filters.sortBy === 'platform' ? 'key' : filters.sortBy;
        const pipeline = [
            { $match: searchBy },
            { $sort: { [sortBy]: filters.sortOrder } },
            { $skip: pageSize * pageNumber },
            ...this._buildMessagesArrayStage(),
            { $limit: pageSize },
        ];

        const conversationsWithMessages = await this._conversationsRepository.aggregate(pipeline as PipelineStage[]);
        const conversationCount = await this._conversationsRepository.countDocuments({ filter: searchBy });
        return { conversationsWithMessages, totalCount: conversationCount };
    };

    getUnreadConversationsCount = async (restaurantId: string, connectedPlatforms: string[]): Promise<number> => {
        const conversationsCount: number = await this._conversationsRepository.countDocuments({
            filter: {
                restaurantId,
                status: ConversationStatus.UNREAD,
                archived: false,
                key: { $in: connectedPlatforms },
            },
        });
        return conversationsCount;
    };

    getMessagesPaginated = async (
        filters: BasicFilters,
        { pageSize, pageNumber }: Pagination
    ): Promise<{ messages: IMessage[]; count: number }> => {
        const searchQuery = filters.buildQuery();
        const messages = await this._messagesRepository.aggregate([
            { $match: searchQuery },
            { $sort: { socialCreatedAt: -1 } },
            { $skip: pageSize * pageNumber },
            { $limit: pageSize },
            {
                $lookup: {
                    from: 'User',
                    localField: 'malouAuthorId',
                    foreignField: '_id',
                    as: 'malouAuthorId',
                },
            },
        ]);
        const count = await this._messagesRepository.countDocuments({
            filter: searchQuery,
        });
        return { messages: messages.reverse(), count };
    };

    updateConversation = async (conversationId: string, data: Partial<IConversation>): Promise<IConversation | null> =>
        this._conversationsRepository.findOneAndUpdate({
            filter: { _id: conversationId },
            update: { ...data },
            options: { lean: true },
        });

    updateConversationsByIds = (conversationIds: string[], data: UpdateConversationsBodyDto) =>
        this._conversationsRepository.updateMany({
            filter: { _id: { $in: conversationIds } },
            update: data,
            options: { lean: true },
        });

    saveMessageWithError = async (messageData: Partial<IMessage>): Promise<void> => {
        const idUsedOnInsert = newDbId();

        const message = await this._messagesRepository.upsert({
            filter: { _id: messageData?._id ?? idUsedOnInsert },
            update: { status: MessageStatus.ERROR },
        });
        await this._conversationsRepository.upsert({
            filter: { _id: message.conversationId },
            update: {
                latestMessageAt: message.socialCreatedAt,
                status: ConversationStatus.READ,
            },
        });
    };

    startSynchronizeRestaurantConversations = async (restaurantId: string, keys: PlatformKey[]) => {
        const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: toDbId(restaurantId) } });
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found' });
        }
        await this._restaurantsRepository.startUpdateMessages(toDbId(restaurantId), keys);
        await this._agendaSingleton.now(AgendaJobName.FETCH_MESSAGES, { restaurantId, keys });
    };

    synchronizeRestaurantConversations = async (restaurantId: string, keys: string[]) => {
        const restaurant = await this._restaurantsRepository.findOneOrFail({ filter: { _id: toDbId(restaurantId) } });
        try {
            const supportedPlatforms = keys.length ? keys : [];
            const platforms = await this._platformsRepository.find({
                filter: { restaurantId: toDbId(restaurantId) },
                options: { lean: true },
            });
            const filteredPlatforms = platforms.filter((p) => supportedPlatforms.includes(p.key));
            for (const platform of filteredPlatforms) {
                try {
                    await this.getConversationsForPlatforms(restaurant, platform);
                    await this._restaurantsRepository.findOneAndUpdate({
                        filter: { _id: restaurantId },
                        update: {
                            [`currentState.messages.fetched.${platform.key}`]: {
                                status: 'success',
                                lastTried: new Date(),
                                error: null,
                            },
                        },
                    });
                } catch (err) {
                    await this._restaurantsRepository.findOneAndUpdate({
                        filter: { _id: restaurantId },
                        update: {
                            [`currentState.messages.fetched.${platform.key}`]: {
                                status: 'error',
                                lastTried: new Date(),
                                error: JSON.stringify(err, errorReplacer),
                            },
                        },
                    });
                }
            }
        } catch (err) {
            logger.error('[ERROR_MESSAGES_SYNC]', err);
            await Promise.all(
                keys.map((key) =>
                    this._restaurantsRepository.findOneAndUpdate({
                        filter: { _id: restaurantId },
                        update: {
                            [`currentState.messages.fetched.${key}`]: {
                                status: 'error',
                                lastTried: new Date(),
                                error: err instanceof Error ? err.message : 'fetch_failed',
                            },
                        },
                    })
                )
            );
        }
    };

    subscribeToFacebookWebhook = async (platforms: any, restaurantId: string): Promise<void> => {
        if (!platforms?.includes(PlatformKey.FACEBOOK)) {
            return;
        }
        const platform = await this._platformsRepository.findOne({ filter: { key: PlatformKey.FACEBOOK, restaurantId } });
        const shouldSubscribeAgainToFacebookWebhook = true; // TODO: implement logic to check if should subscribe again
        if (shouldSubscribeAgainToFacebookWebhook && platform) {
            await this._commentsUseCases.authorizeSubscribedApps([platform]);
        }
    };

    sendMessage = async ({ message, platform }) => {
        try {
            const response = await this.getPlatformsUseCases(platform.key).sendMessage({ message, platform });
            return this.saveMessage(message, response.data);
        } catch (err) {
            logger.error('[SEND_MESSAGE_ERROR] - sending message failed', { err, platformKey: platform.key });
            try {
                await this.saveMessageWithError(message);
            } catch (error) {
                logger.warn('[SEND_MESSAGE_ERROR] - Could not save message with error', { error });
            }
            throw err;
        }
    };

    getPlatformsUseCases = (platformKey: string) => {
        return require(`./platforms/${platformKey}/use-cases`);
    };

    private getConversationsForPlatforms = async (restaurant: IRestaurant, platform: IPlatform) => {
        const mappedConversationsAndMessage: ConversationWithMessages[] = await this.getPlatformsUseCases(platform.key).synchronize({
            platform,
        });

        if (!mappedConversationsAndMessage) {
            return { conversations: [], errors: null };
        }

        const mappedConversationsSocialIds = mappedConversationsAndMessage.map((conv) => conv.conversation.socialConversationId);
        const existingConversations = await this._conversationsRepository.find({
            filter: { restaurantId: restaurant._id, key: platform.key },
            projection: { socialConversationId: 1 },
            options: { lean: true },
        });
        const conversationsToDelete = existingConversations
            .map((conv) => conv.socialConversationId)
            .filter((socialId) => !mappedConversationsSocialIds.includes(socialId));
        await this._conversationsRepository.deleteMany({
            filter: {
                restaurantId: restaurant._id,
                socialConversationId: { $in: conversationsToDelete },
            },
        });

        let messagesUpserts: Promise<IMessage>[] = [];
        for (const conversationWithMessages of mappedConversationsAndMessage) {
            const updatedConversation = await this._conversationsRepository.upsert({
                filter: {
                    socialConversationId: conversationWithMessages.conversation.socialConversationId,
                    key: conversationWithMessages.conversation.key,
                    restaurantId: restaurant._id,
                },
                update: {
                    ...conversationWithMessages.conversation,
                    latestMessageAt: conversationWithMessages.messages?.[0]?.socialCreatedAt,
                },
            });
            messagesUpserts = messagesUpserts.concat(
                conversationWithMessages?.messages?.map((message) =>
                    this._messagesRepository.upsert({
                        filter: {
                            socialMessageId: message.socialMessageId,
                            key: updatedConversation.key,
                            conversationId: updatedConversation._id,
                        },
                        update: { ...message, socialConversationId: updatedConversation.socialConversationId },
                    })
                ) ?? []
            );
        }
        const conversations = await Promise.all(messagesUpserts);
        return { conversations };
    };

    private getPlatformMapper = (platformKey: string): ConversationMapper => {
        switch (platformKey) {
            case PlatformKey.FACEBOOK:
                return new FacebookConversationMapper();
            case PlatformKey.INSTAGRAM:
                return new InstagramConversationMapper();
            default:
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, { message: 'Platform does not have conversation mapper' });
        }
    };

    private saveMessage = async (messageData: IMessage, platformMessageData: any): Promise<IMessage> => {
        const idUsedOnInsert = newDbId();
        const conversationMapper = this.getPlatformMapper(messageData.key);
        const message = await this._messagesRepository.upsert({
            filter: { _id: messageData?._id ?? idUsedOnInsert },
            update: {
                ...messageData,
                socialMessageId: conversationMapper.mapToMalouAfterSentSocialId({ message: platformMessageData }),
                status: MessageStatus.DELIVERED,
            },
            options: { populate: [{ path: 'malouAuthor', select: { name: 1, lastname: 1 } }], lean: true },
        });
        await this._conversationsRepository.upsert({
            filter: { _id: message.conversationId },
            update: {
                latestMessageAt: message.socialCreatedAt,
                status: ConversationStatus.READ,
            },
        });
        return message;
    };

    private _buildMessagesArrayStage = () => [
        {
            $lookup: {
                from: 'messages',
                let: { conversationId: '$_id' },
                pipeline: [
                    { $match: { $expr: { $eq: ['$conversationId', '$$conversationId'] } } },
                    { $sort: { socialCreatedAt: -1 } },
                    { $limit: 10 },
                    {
                        $lookup: {
                            from: 'User',
                            localField: 'malouAuthorId',
                            foreignField: '_id',
                            as: 'malouAuthorId',
                        },
                    },
                ],
                as: 'messages',
            },
        },
        {
            $addFields: {
                messages: {
                    $reverseArray: '$messages',
                },
            },
        },
        {
            $lookup: {
                from: 'User',
                localField: 'malouAuthorId',
                foreignField: '_id',
                as: 'malouAuthorId',
            },
        },
        {
            $project: {
                conversation: '$$ROOT',
                messages: 1,
            },
        },
    ];
}
