import { container } from 'tsyringe';

import { IMessage } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import {
    ConversationPlatform,
    IgMessage,
    IgUser,
    Participants,
    SocialConversationWithMessages,
} from ':modules/credentials/platforms/facebook/facebook.types';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { ConversationWithMessages } from ':modules/messages/messages.interface';
import { ConversationsRepository, MessagesRepository } from ':modules/messages/messages.repository';
import { InstagramConversationMapper } from ':modules/messages/platforms/instagram/instagram-conversation-mapper';

const conversationsRepository = container.resolve(ConversationsRepository);
const messagesRepository = container.resolve(MessagesRepository);

export const synchronize = async function ({ platform }): Promise<ConversationWithMessages[]> {
    const targetPlatform = ConversationPlatform.INSTAGRAM;
    const conversationsMapper = new InstagramConversationMapper();
    const {
        credentials: [credentialId],
    } = platform;
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    const result = await facebookCredentialsUseCases.getAllConversationsAndMessages(credentialId, platform.socialId, targetPlatform);
    const conversations: SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>[] = result?.data;
    if (!conversations) {
        return [];
    }

    const promises: Promise<SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>>[] = conversations.map(
        (conv) =>
            // eslint-disable-next-line no-async-promise-executor
            new Promise(async (resolve) => {
                try {
                    const { participants } = conv;
                    const updatedParticipants: Participants<IgUser> = {
                        data: [],
                    };
                    for (let i = 0; i < participants.data.length; i++) {
                        const participant = participants.data[i];
                        if (!participant) {
                            continue;
                        }
                        const profilePic: string = await facebookCredentialsUseCases.getUserProfilePicture(
                            participant.id,
                            credentialId,
                            platform.socialId,
                            targetPlatform
                        );

                        updatedParticipants.data.push({
                            ...participant,
                            profile_picture_url: profilePic,
                        });
                    }
                    conv.participants.data = updatedParticipants.data;
                    resolve(conv);
                } catch (error) {
                    logger.error('[INSTAGRAM_GET_PARTICIPANTS_PICS_ERROR]', { id: conv.participants.data[1]?.id, error });
                    resolve(conv);
                }
            })
    );

    const enrichedConversationsWithParticipantsPictures = await Promise.all(promises);
    return enrichedConversationsWithParticipantsPictures.map((conv) =>
        conversationsMapper.mapToMalouConversationWithMessages({ conversation: conv, platform })
    );
};

/**
 * @param {Object} data The message text to send the user.
 * @param {string} data.message The message text to send the user.
 * @param {Platform} data.platform
 */
export const sendMessage = async ({ message, platform }: { message: Partial<IMessage>; platform: any }): Promise<any> => {
    try {
        const conversationsMapper = new InstagramConversationMapper();
        const {
            credentials: [credentialId],
        } = platform;
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const mappedMessage = await conversationsMapper.mapToPlatformMessage({ message });
        if (!mappedMessage) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, { message: 'Error in message to send', metadata: { message } });
        }
        const result = await facebookCredentialsUseCases.sendMessage(credentialId, platform.socialId, mappedMessage);
        return result;
    } catch (err) {
        logger.error('[SEND_INSTAGRAM_MESSAGE_ERROR]', err);
        throw err;
    }
};

export const updateReaction = async ({
    message,
    platform,
    reactionType,
}: {
    message: IMessage;
    platform: any;
    reactionType: string;
}): Promise<IMessage> => {
    const conversationsMapper = new InstagramConversationMapper();
    const {
        credentials: [credentialId],
    } = platform;
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
    }
    const conversation = await conversationsRepository.findOneOrFail({ filter: { _id: message.conversationId } });
    const platformMessage = conversationsMapper.mapToPlatformReaction({
        message,
        recipientId: conversation.userInfo.userSocialId,
        type: reactionType,
    });
    if (!platformMessage) {
        throw new MalouError(MalouErrorCode.INVALID_DATA, { message: 'Error in message to send', metadata: { message } });
    }
    await facebookCredentialsUseCases.sendMessage(credentialId, platform.socialId, platformMessage);

    const updatedPlatformMessage = await facebookCredentialsUseCases.getMessage(
        platform.credentials?.[0]?._id,
        message.socialMessageId,
        platform.socialId,
        ConversationPlatform.INSTAGRAM
    );
    return messagesRepository.upsert({
        filter: { _id: message._id },
        update: {
            ...conversationsMapper.mapToMalouMessage({
                message: updatedPlatformMessage as IgMessage,
                platform,
                externalUserProfilePictureUrl: conversation.userInfo.profilePictureUrl,
                socialConversationId: conversation.socialConversationId,
            }),
        },
    });
};
