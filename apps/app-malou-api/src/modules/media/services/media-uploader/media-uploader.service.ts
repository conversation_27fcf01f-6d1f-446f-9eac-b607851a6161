import axios from 'axios';
import fs from 'fs';
import imageSize from 'image-size';
import assert from 'node:assert/strict';
import path from 'path';
import { inject, singleton } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';

import {
    coverDimensions,
    FileFormat,
    getTypeFromMimetype,
    IG_FIT_BIGGER_SIDE_MAX_SIZE,
    MalouErrorCode,
    MediaCategory,
    MediaDimension,
    MediaType,
    PictureSize,
    PictureSizeRecord,
    SMALL_MEDIA_BIGGER_SIDE_MAX_SIZE,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';
import SharpProvider from ':helpers/providers/sharp.provider';
import { Media, MediaProps } from ':modules/media/entities/media.entity';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';

import { ParsedFile } from '../file-parser/file-parser.interface';

export interface UploadedMedia {
    urls: PictureSizeRecord<string>;
    sizes: PictureSizeRecord<number>;
    dimensions?: PictureSizeRecord<MediaDimension>;
}

export type ResizeDimensions = {
    igFit: (number | undefined)[];
    small: (number | undefined)[];
    original: (number | undefined)[];
};

type UploadMediumParams = {
    type: MediaType;
    pathWhereFileIsStored: string;
    extension: string;
    category: string;
    entityRelated: string;
    entityId: string;
    mimetype?: string;
    keepOriginalAsIgFit?: boolean;
};

@singleton()
export class MediaUploaderService {
    constructor(
        private readonly _sharpProvider: SharpProvider,
        @inject(AwsS3) private readonly _cloudStorage: CloudStorage,
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}

    async uploadFromFile(
        file: ParsedFile,
        media: MediaProps,
        { keepOriginalAsIgFit } = { keepOriginalAsIgFit: false }
    ): Promise<UploadedMedia> {
        const { mimetype, type, pathWhereFileIsStored } = file;

        media.format = mimetype.split('/')[1] as FileFormat;

        const { entityRelated, entityId } = Media.getEntityRelatedOn({
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
        });

        assert(entityId, 'Missing entityId');

        const result = await this._uploadMedium({
            type,
            pathWhereFileIsStored,
            extension: media.format,
            category: media.category,
            entityRelated,
            entityId,
            mimetype,
            keepOriginalAsIgFit,
        });

        return result;
    }

    async uploadFromUrl(url: string, restaurantId: string): Promise<UploadedMedia> {
        const { type, pathName, format } = await this.downloadMediaFromUrl(url);
        const result = await this._uploadMedium({
            type,
            pathWhereFileIsStored: pathName,
            extension: format,
            category: MediaCategory.ADDITIONAL,
            entityRelated: 'restaurants',
            entityId: restaurantId.toString(),
        });
        return result;
    }

    async downloadMediaFromUrl(url: string): Promise<{ type: MediaType; format: FileFormat; pathName: string }> {
        const response = await axios({
            method: 'GET',
            url,
            responseType: 'stream',
        });

        const mimeType = response.headers['content-type'];
        const filename = uuidv4() + '.' + mimeType.split('/')[1];
        const savePath = path.join('.', 'downloadedMedias', filename);

        const outputStream = fs.createWriteStream(savePath);
        response.data.pipe(outputStream);

        await new Promise((resolve, reject) => {
            outputStream.on('finish', resolve);
            outputStream.on('error', reject);
        });

        return {
            type: getTypeFromMimetype(mimeType),
            format: mimeType.split('/')[1],
            pathName: savePath,
        };
    }

    getResizeDimensionsForFile(pathName: string): ResizeDimensions {
        /* we take the bigger side of the image and resize relatively to it.
         Max 1500px bigger side for instagram so we are under 8MB (~1500^2*1B) (1B/px png, 0.14B/px jpg) */
        const dimensions = imageSize(pathName);
        const width = dimensions.width || 0;
        const height = dimensions.height || 0;
        return this._computeResizeDimensions({ width, height });
    }

    private _computeResizeDimensions(dimensions: { width: number; height: number }): ResizeDimensions {
        const { width, height } = dimensions;
        const biggerSide = width >= height ? 'width' : 'height';
        const maxLength = Math.min(dimensions[biggerSide], IG_FIT_BIGGER_SIDE_MAX_SIZE);
        const igArgs = biggerSide === 'height' ? [undefined, maxLength] : [maxLength, undefined];
        const smallArgs =
            biggerSide === 'height' ? [undefined, SMALL_MEDIA_BIGGER_SIDE_MAX_SIZE] : [SMALL_MEDIA_BIGGER_SIDE_MAX_SIZE, undefined];
        return { igFit: igArgs, small: smallArgs, original: [width, height] };
    }

    private async _uploadMedium({
        type,
        pathWhereFileIsStored,
        extension,
        category,
        entityRelated,
        entityId,
        mimetype,
        keepOriginalAsIgFit = false,
    }: UploadMediumParams): Promise<UploadedMedia> {
        switch (type) {
            case MediaType.PHOTO:
                return this._uploadImage(
                    pathWhereFileIsStored,
                    extension,
                    category,
                    entityRelated,
                    entityId,
                    mimetype,
                    keepOriginalAsIgFit
                );
            case MediaType.VIDEO:
                return this._uploadVideo(pathWhereFileIsStored, this._getVideoExtensionFromFormat(extension), entityRelated, entityId);
            case MediaType.FILE:
                return this._uploadFile(pathWhereFileIsStored, extension, entityRelated, entityId);
            default:
                throw new MalouError(MalouErrorCode.WRONG_MEDIA_TYPE, {
                    metadata: { type },
                });
        }
    }

    private _getVideoExtensionFromFormat(format: string): string {
        if (format === FileFormat.QUICKTIME) {
            return FileFormat.MOV;
        }
        return format;
    }

    private async _uploadImage(
        pathWhereFileIsStored: string,
        extension: string,
        category: string = MediaCategory.ADDITIONAL,
        entityRelated: string,
        entityId: string,
        mimetype?: string,
        keepOriginalAsIgFit = false
    ): Promise<UploadedMedia> {
        const uuid = uuidv4();
        const remotePath = `${entityRelated}/${entityId}/media/${uuid}`;

        const pathName = pathWhereFileIsStored;
        let image = await fs.promises.readFile(pathName);

        // check if image has exif data (for multiframes images)
        const { orientation, height, width } = await this._sharpProvider.sharp(image).metadata();
        if (orientation) {
            // if image has exif data, rotate it to remove exif data
            await this._sharpProvider.sharp(image).rotate().toFile(pathName);
            image = await fs.promises.readFile(pathName);
        }
        const { igFit: igArgs, small: smallArgs, original: originalArgs } = this._computeResizeDimensions({ width, height });
        const smallPath = `./downloadedMedias/small-${uuid}.${extension}`;
        const smallImage = await this._resizeImageToPath(image, smallPath, smallArgs);
        const igFitPath = `./downloadedMedias/igfit-${uuid}.${extension}`;
        const igFitImage = keepOriginalAsIgFit
            ? await this._resizeImageToPath(image, igFitPath, originalArgs)
            : await this._resizeImageToPath(image, igFitPath, igArgs);
        const sizes: PictureSizeRecord<number> = {
            original: 0,
        };
        const dimensions = {
            [PictureSize.ORIGINAL]: { width, height },
            [PictureSize.SMALL]: { width: smallImage.width, height: smallImage.height },
            [PictureSize.IG_FIT]: { width: igFitImage.width, height: igFitImage.height },
        };
        const urlsPromises: Promise<string>[] = [];
        let coverPath;
        let smallCoverPath;
        if (category.toLowerCase() === MediaCategory.COVER) {
            coverPath = `./downloadedMedias/cover-${uuid}.${extension}`;
            const coverImage = await this._resizeImageToPath(image, coverPath, coverDimensions.cover);
            smallCoverPath = `./downloadedMedias/smallCover-${uuid}.${extension}`;
            const smallCoverImage = await this._resizeImageToPath(image, smallCoverPath, coverDimensions.smallCover);

            sizes.cover = coverImage.size;
            sizes.smallCover = smallCoverImage.size;
            dimensions[PictureSize.COVER] = { width: coverImage.width, height: coverImage.height };
            dimensions[PictureSize.SMALL_COVER] = { width: smallCoverImage.width, height: smallCoverImage.height };
        }
        urlsPromises.push(
            this._cloudStorage.uploadMedia({
                localImagePath: pathName,
                remotePath,
                mediaName: 'original',
                extension,
                mimetype,
            })
        );

        urlsPromises.push(
            this._cloudStorage.uploadMedia({
                localImagePath: smallPath,
                remotePath,
                mediaName: 'small',
                extension,
                mimetype,
            })
        );

        urlsPromises.push(
            this._cloudStorage.uploadMedia({
                localImagePath: igFitPath,
                remotePath,
                mediaName: 'igfit',
                extension,
                mimetype,
            })
        );

        if (category.toLowerCase() === MediaCategory.COVER) {
            urlsPromises.push(
                this._cloudStorage.uploadMedia({
                    localImagePath: coverPath,
                    remotePath,
                    mediaName: 'cover',
                    extension,
                })
            );

            urlsPromises.push(
                this._cloudStorage.uploadMedia({
                    localImagePath: smallCoverPath,
                    remotePath,
                    mediaName: 'smallCover',
                    extension,
                })
            );
        }

        const urlFields = ['original', 'small', 'igFit', 'cover', 'smallCover'];
        const urlsArray = await Promise.all(urlsPromises);
        const urls: PictureSizeRecord<string> = {
            original: '',
        };
        for (let i = 0; i < urlsArray.length; i++) {
            urls[urlFields[i]] = urlsArray[i];
        }

        sizes.original = image.toString().length;
        sizes.small = smallImage.size;
        sizes.igFit = igFitImage.size;

        return { urls, sizes, dimensions };
    }

    private async _uploadVideo(
        pathWhereFileIsStored: string,
        extension: string,
        entityRelated: string,
        entityId: string
    ): Promise<UploadedMedia> {
        const uuid = uuidv4();
        const remotePath = `${entityRelated}/${entityId}/media/${uuid}`;
        const pathName = pathWhereFileIsStored;
        const video = await fs.promises.readFile(pathName);

        const awsUrl = await this._cloudStorage.uploadMedia({
            localImagePath: pathName,
            remotePath,
            mediaName: 'original',
            extension,
        });
        const urls: PictureSizeRecord<string> = { original: awsUrl };
        const sizes = { original: video.toString().length };

        const info = await this._multimediaStreamsInformationService.getSize(awsUrl);

        return { urls, sizes, dimensions: { original: { width: info.width, height: info.height } } };
    }

    private async _uploadFile(
        pathWhereFileIsStored: string,
        extension: string,
        entityRelated: string,
        entityId: string
    ): Promise<UploadedMedia> {
        const uuid = uuidv4();
        const remotePath = `${entityRelated}/${entityId}/media/${uuid}`;
        const pathName = pathWhereFileIsStored;
        const file = await fs.promises.readFile(pathName);
        const awsUrl = await this._cloudStorage.uploadMedia({
            localImagePath: pathName,
            remotePath,
            mediaName: 'original',
            extension,
        });
        const urls = { original: awsUrl };
        const sizes = { original: file.toString().length };
        return { urls, sizes };
    }

    private async _resizeImageToPath(buffer: Buffer, pathName: string, args: (number | undefined)[]) {
        return this._sharpProvider
            .sharp(buffer)
            .resize(...args)
            .withMetadata()
            .toFile(pathName)
            .catch((e: any) => {
                logger.error('[RESIZE_ERROR] Error when resizing image with sharp', { path: pathName, args, e });
                return { size: 0 };
            });
    }
}
