import assert from 'node:assert/strict';
import { delay, inject, singleton } from 'tsyringe';

import { GetStoryMediaForEditionResponseDto } from '@malou-io/package-dto';
import { IMedia } from '@malou-io/package-models';
import { MediaType } from '@malou-io/package-utils';

import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';
import { MediasRepository } from ':modules/media/repository/medias.repository';

@singleton()
export class StoryMediaForEditionMapper {
    constructor(
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService,
        @inject(delay(() => MediasRepository)) private readonly _mediasRepository: MediasRepository
    ) {}

    async map(media: IMedia): Promise<GetStoryMediaForEditionResponseDto> {
        const url = this._getUrl(media);
        const base = {
            id: media._id.toString(),
            type: media.type,
            url,
            thumbnail1024Url: this._getThumbnail1024Url(media),
            thumbnail256Url: this._getThumbnail256Url(media),
        } as const;

        if (media.type === MediaType.VIDEO) {
            let durationInSeconds: number;
            if (media.duration) {
                durationInSeconds = media.duration;
            } else {
                durationInSeconds = await this._multimediaStreamsInformationService.getDuration(url);
                await this._mediasRepository.updateOne({ filter: { _id: media._id }, update: { duration: durationInSeconds } });
            }
            return {
                ...base,
                type: media.type,
                durationInSeconds,
            };
        }

        if (media.type === MediaType.PHOTO) {
            return {
                ...base,
                type: media.type,
            };
        }

        assert.fail('Media type should be photo or video');
    }

    private _getUrl(media: IMedia): string {
        return media.storedObjects?.normalized.publicUrl ?? media.urls.original;
    }

    private _getThumbnail1024Url(media: IMedia): string {
        if (media.type === MediaType.PHOTO) {
            return media.storedObjects?.thumbnail1024Outside.publicUrl ?? media.urls.igFit ?? media.urls.original;
        } else {
            const url = media.storedObjects?.thumbnail1024Outside.publicUrl ?? media.thumbnail;
            assert(!!url, '_getThumbnail1024Url not available');
            return url;
        }
    }

    private _getThumbnail256Url(media: IMedia): string {
        if (media.type === MediaType.PHOTO) {
            return media.storedObjects?.thumbnail256Outside.publicUrl ?? media.urls.small ?? media.urls.igFit ?? media.urls.original;
        } else {
            const url = media.storedObjects?.thumbnail256Outside.publicUrl ?? media.thumbnail;
            assert(!!url, '_getThumbnail256Url not available');
            return url;
        }
    }
}
