import * as aws from 'aws-sdk';
import { omit } from 'lodash';
import * as luxon from 'luxon';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { randomUUID } from 'node:crypto';
import { inject, singleton } from 'tsyringe';
import { z } from 'zod';

import { IMediaStoredObject } from '@malou-io/package-models';
import { TimeInSeconds, waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { AwsMediaConvert, AwsMediaConvertInterface } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert';
import { MediaConvertJobTracking } from ':modules/media/use-cases/upload-media-v2/media-convert-job-tracking';
import { ComputeFrameCaptureOptionsService } from ':modules/media/use-cases/upload-media-v2/services/compute-frame-capture-options.service';
import { cacheDecorator } from ':plugins/cache-decorator';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

/** In this object widths and heights are adjusted (swapped) according to rotation metadata. */
const inputInfoValidator = z.object({
    width: z.number(),
    height: z.number(),
});

/** In this object widths and heights are adjusted according to rotation metadata */
export type InputInfo = z.infer<typeof inputInfoValidator>;

type AwsMediaConvertResult<SuccessValue> =
    | { success: true; value: SuccessValue }
    | { success: false; type: 'canceled' }
    | { success: false; type: 'errored'; awsCode: number; awsMessage: string };

/** Raw result of a AWS MediaConvert job, with a mix of input metadata and output metadata */
type JobRawResult = AwsMediaConvertResult<{
    outputGroups: {
        durationInMs: number;

        /** for FRAME_CAPTURE we always have only one key here in practice (the last one) */
        s3keys: string[];

        width?: number;
        height?: number;
    }[];
}>;

/**
 * https://docs.aws.amazon.com/mediaconvert/latest/ug/setting-up-timecode.html
 *
 * Timecodes are in the following 24-hour format with a frame number (FF): HH:MM:SS:FF
 * The frame number is always 00 in our case.
 *
 * For instance: Converts 1234 to '00:20:34:00'
 */
const secondsToStringTimecode = (seconds: number): string => {
    return luxon.Duration.fromObject({ seconds }).toFormat('hh:mm:ss:00');
};

/** AWS MediaConvert does not support odd resolutions for thumbnails. */
const floorAndMakeEven = (float: number): number => {
    const floored = Math.floor(float);
    return floored % 2 === 1 ? floored - 1 : floored;
};

/**
 * This is ridiculous but this is actually what’s recommended by the AWS documentation 🥲
 *
 * https://docs.aws.amazon.com/mediaconvert/latest/ug/file-group-with-frame-capture-output.html
 *
 * This is needed because for some reason, with the codec FRAME_CAPTURE, the COMPLETE event
 * only tells us the key of the last image.
 *
 * This function is exported to be used in unit tests.
 */
export const listKeysForFrameCapture = (lastKey: string, keyExtension: string): string[] => {
    assert(lastKey.endsWith(keyExtension));
    const match = /^(.+?\.)([0-9]{7})$/.exec(lastKey.slice(0, lastKey.length - keyExtension.length));
    assert(match);
    const prefix = match[1];
    const lastNumber = parseInt(match[2], 10);
    assert(!isNaN(lastNumber));
    const keys = [...Array(lastNumber + 1)].map((_, i: number): string => prefix + i.toString().padStart(7, '0') + keyExtension);
    assert(keys.length >= 1);
    assert.equal(keys[keys.length - 1], lastKey);
    return keys;
};

@singleton()
export class AwsMediaConvertService {
    constructor(
        /**
         * We don’t use the DistantStorageService interface because AWS MediaConvert works
         * with AWS S3 only.
         */
        private readonly _s3Service: AwsS3DistantStorageService,
        @inject(AwsMediaConvert) private readonly _awsMediaConvert: AwsMediaConvertInterface,
        private readonly _computeFrameCaptureOptionsService: ComputeFrameCaptureOptionsService
    ) {}

    /**
     * s3NormalizedKeyPrefix: the key of the output S3 object without the '.mp4' extension
     * sourceWidth: the width in pixels of the source video
     * sourceHeight: the height in pixels of the source video
     *
     * The dimensions of the source video can be computed with the function getVideoMetadata.
     */
    public async normalizeVideo(params: { s3SourceKey: string; s3NormalizedKeyPrefix: string; isHdrVideo: boolean }): Promise<
        AwsMediaConvertResult<{
            storedObject: IMediaStoredObject;
            durationInMs: number;
            width: number;
            height: number;
        }>
    > {
        logger.info('[AwsMediaConvertService] normalizing...', { params });

        const maxDimension = 1920;
        const settings: aws.MediaConvert.JobSettings = {
            TimecodeConfig: { Source: 'ZEROBASED' },
            OutputGroups: [
                {
                    Outputs: [
                        {
                            ContainerSettings: { Container: 'MP4', Mp4Settings: {} },
                            VideoDescription: {
                                Width: maxDimension,
                                Height: maxDimension,
                                // We use FIT_NO_UPSCALE because we want the result to contained in the maxDimension x maxDimension box.
                                // As opposed to FILL (used in transformVideoForPublication function)
                                // we do want/care the result to be exactly the specified Width and Height.
                                // https://docs.aws.amazon.com/mediaconvert/latest/ug/video-scaling.html
                                ScalingBehavior: 'FIT_NO_UPSCALE',
                                VideoPreprocessors: {
                                    // Regarding AWS pricing: color correction requires the
                                    // “professional tier”
                                    //
                                    // https://docs.aws.amazon.com/mediaconvert/latest/ug/converting-the-color-space.html
                                    ColorCorrector: {
                                        // long story short: BT.709 means “SDR”. We want to
                                        // convert HDR content to SDR.
                                        ColorSpaceConversion: 'FORCE_709',
                                        SampleRangeConversion: 'LIMITED_RANGE_SQUEEZE',
                                        HdrToSdrToneMapper: 'VIBRANT',
                                        // Overall, clients are complaining about the hdr -> sdr conversion because the result video looks dull
                                        // So we adjust a little these 3 parameters to make it look better
                                        ...(params.isHdrVideo ? { Brightness: 51, Contrast: 54, Saturation: 54 } : {}),
                                    },
                                },
                                CodecSettings: {
                                    Codec: 'H_264',
                                    H264Settings: {
                                        FramerateControl: 'SPECIFIED',
                                        FramerateDenominator: 1,
                                        FramerateNumerator: 30,
                                        MaxBitrate: 23_000_000,
                                        RateControlMode: 'QVBR',
                                        SceneChangeDetect: 'TRANSITION_DETECTION',
                                    },
                                },
                            },
                            AudioDescriptions: [
                                {
                                    AudioSourceName: 'Audio Selector 1',
                                    CodecSettings: {
                                        Codec: 'AAC',
                                        AacSettings: {
                                            Bitrate: 96000,
                                            CodingMode: 'CODING_MODE_2_0',
                                            SampleRate: 48000,
                                        },
                                    },
                                },
                            ],
                        },
                    ],
                    OutputGroupSettings: {
                        Type: 'FILE_GROUP_SETTINGS',
                        FileGroupSettings: {
                            Destination: this._s3Service.getPrivateS3Url(params.s3NormalizedKeyPrefix),
                        },
                    },
                },
            ],
            FollowSource: 1,
            Inputs: [
                {
                    AudioSelectors: { 'Audio Selector 1': { DefaultSelection: 'DEFAULT' } },
                    VideoSelector: { Rotate: 'AUTO' },
                    TimecodeSource: 'ZEROBASED',
                    FileInput: this._s3Service.getPrivateS3Url(params.s3SourceKey),
                },
            ],
        };

        const result = await this._runJob(settings);
        if (result.success === false) {
            return result;
        }
        assert(result.value.outputGroups[0].width);
        assert(result.value.outputGroups[0].height);

        const publicUrl = await this._s3Service.getPublicAccessibleUrl(params.s3NormalizedKeyPrefix + '.mp4');
        return {
            success: true,
            value: {
                storedObject: { key: params.s3NormalizedKeyPrefix + '.mp4', publicUrl, provider: 'S3' },
                durationInMs: result.value.outputGroups[0].durationInMs,
                width: result.value.outputGroups[0].width,
                height: result.value.outputGroups[0].height,
            },
        };
    }

    /**
     * This cache serves two main purposes:
     *
     * 1. To prevent sending the same job to AWS MediaConvert multiple times in a short period,
     *    which can result in an error. This is also important to avoid wasting resources and
     *    minimize AWS costs.
     *
     * 2. To speed up the process by avoiding unnecessary requests to AWS MediaConvert.
     *    This is particularly useful for groups, when they duplicate a post on all of their restaurants.
     *    By caching the results for one hour, it let enough time to all the posts to publish.
     */
    @cacheDecorator({
        ttlInSeconds: TimeInSeconds.HOUR,
        computeCacheKeyArgs: (...args) => [omit(args[0], ['s3OutputKeyPrefix'])],
    })
    public async transformVideoForPublication(params: {
        s3NormalizedKey: string;
        s3OutputKeyPrefix: string;
        outputDimensions: { width: number; height: number };
        cropArea?: { left: number; top: number; width: number; height: number };
    }): Promise<AwsMediaConvertResult<{ s3OutputKey: string; width: number; height: number }>> {
        logger.info('[AwsMediaConvertService] transformVideoForPublication', params);

        const s3OutputKey = params.s3OutputKeyPrefix + '.mp4';

        const settings: aws.MediaConvert.JobSettings = {
            TimecodeConfig: { Source: 'ZEROBASED' },
            OutputGroups: [
                {
                    Outputs: [
                        {
                            ContainerSettings: { Container: 'MP4', Mp4Settings: {} },
                            VideoDescription: {
                                Width: floorAndMakeEven(params.outputDimensions.width),
                                Height: floorAndMakeEven(params.outputDimensions.height),
                                // We use FILL because we want the result to be exactly to the specified Width and Height.
                                // It will crop if necessary.
                                // https://docs.aws.amazon.com/mediaconvert/latest/ug/video-scaling.html
                                ScalingBehavior: 'FILL',
                                CodecSettings: {
                                    Codec: 'H_264',
                                    H264Settings: {
                                        FramerateDenominator: 1,
                                        FramerateNumerator: 30,
                                        // Meta wants 25 at most Mbps
                                        // https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/ig-user/media#video-specifications
                                        MaxBitrate: 23_000_000,
                                        RateControlMode: 'QVBR',
                                        SceneChangeDetect: 'TRANSITION_DETECTION',
                                    },
                                },
                            },
                            AudioDescriptions: [
                                {
                                    AudioSourceName: 'Audio Selector 1',
                                    CodecSettings: {
                                        Codec: 'AAC',
                                        AacSettings: {
                                            Bitrate: 96000,
                                            CodingMode: 'CODING_MODE_2_0',
                                            SampleRate: 48000,
                                        },
                                    },
                                },
                            ],
                        },
                    ],
                    OutputGroupSettings: {
                        Type: 'FILE_GROUP_SETTINGS',
                        FileGroupSettings: {
                            Destination: this._s3Service.getPrivateS3Url(params.s3OutputKeyPrefix),
                        },
                    },
                },
            ],
            FollowSource: 1,
            Inputs: [
                {
                    AudioSelectors: { 'Audio Selector 1': { DefaultSelection: 'DEFAULT' } },
                    VideoSelector: { Rotate: 'AUTO' },
                    Crop: params.cropArea
                        ? {
                              Height: floorAndMakeEven(params.cropArea.height),
                              Width: floorAndMakeEven(params.cropArea.width),
                              X: floorAndMakeEven(params.cropArea.left),
                              Y: floorAndMakeEven(params.cropArea.top),
                          }
                        : undefined,
                    TimecodeSource: 'ZEROBASED',
                    FileInput: this._s3Service.getPrivateS3Url(params.s3NormalizedKey),
                },
            ],
        };

        const result = await this._runJob(settings);
        if (result.success === false) {
            if (result.type === 'errored') {
                return result;
            }
            assert(result.type === 'canceled');
            return result;
        }
        const outputVideoWidth = result.value.outputGroups[0].width;
        const outputVideoHeight = result.value.outputGroups[0].height;
        assert(outputVideoWidth);
        assert(outputVideoHeight);
        return { success: true, value: { s3OutputKey, width: outputVideoWidth, height: outputVideoHeight } };
    }

    /**
     * Returns AWS S3 keys for each output group.
     *
     * The returned list is ordered to match the `outputGroups` parameter.
     */
    public async generateVideoThumbnails(
        source: {
            s3Key: string;
            durationInMilliseconds: number;
            widthInPixels: number;
            heightInPixels: number;
            startTimestampInMilliseconds?: number;
        },
        outputGroups: {
            maxDimensionsPx: number;
            s3OutputKeyPrefix: string;
            /**
             * How many frames to capture.
             * AWS capture the first frame of the video
             * then remaining thumbnails will be captured throughout the video (spaced evenly).
             */
            capturesCount: number;
        }[]
    ): Promise<Result<{ s3keys: string[]; width: number; height: number }[], void>> {
        const sourceAspectRatio = source.widthInPixels / source.heightInPixels;
        const outputGroupsRes = outputGroups.map((outputGroup) => {
            // We will use FIT_NO_UPSCALE as scaling behavior (https://docs.aws.amazon.com/mediaconvert/latest/ug/video-scaling.html)
            // So we compute here a box that will fit the source without cropping it.
            let fitHeight: number;
            let fitWidth: number;
            if (sourceAspectRatio > 1) {
                fitHeight = floorAndMakeEven(outputGroup.maxDimensionsPx);
                fitWidth = floorAndMakeEven(Math.round(outputGroup.maxDimensionsPx * sourceAspectRatio));
            } else {
                fitWidth = floorAndMakeEven(outputGroup.maxDimensionsPx);
                fitHeight = floorAndMakeEven(Math.round(outputGroup.maxDimensionsPx / sourceAspectRatio));
            }

            const frameCaptureOptionsRes = this._computeFrameCaptureOptionsService.execute(
                source.durationInMilliseconds,
                outputGroup.capturesCount
            );
            if (frameCaptureOptionsRes.isErr()) {
                logger.error('[AwsMediaConvertService] Error at _computeFrameCaptureOptionsService', {
                    error: frameCaptureOptionsRes.error,
                });
                return err(undefined);
            }
            return ok({
                Outputs: [
                    {
                        ContainerSettings: { Container: 'RAW' },
                        VideoDescription: {
                            Width: fitWidth,
                            Height: fitHeight,
                            // We use FIT_NO_UPSCALE because we want the result (thumbnails) to be at maximum the specified Width/Height.
                            // https://docs.aws.amazon.com/mediaconvert/latest/ug/video-scaling.html
                            ScalingBehavior: 'FIT_NO_UPSCALE',
                            CodecSettings: {
                                Codec: 'FRAME_CAPTURE',
                                FrameCaptureSettings: {
                                    FramerateNumerator: frameCaptureOptionsRes.value.numerator,
                                    FramerateDenominator: frameCaptureOptionsRes.value.denominator,
                                    MaxCaptures: outputGroup.capturesCount,
                                },
                            },
                        },
                        Extension: '.jpeg',
                    },
                ],
                OutputGroupSettings: {
                    Type: 'FILE_GROUP_SETTINGS',
                    FileGroupSettings: {
                        Destination: this._s3Service.getPrivateS3Url(outputGroup.s3OutputKeyPrefix),
                    },
                },
            });
        });

        const outputGroupsCombinedRes = Result.combine(outputGroupsRes);
        if (outputGroupsCombinedRes.isErr()) {
            return err(undefined);
        }

        const StartTimecode: string | undefined = source.startTimestampInMilliseconds
            ? secondsToStringTimecode(source.startTimestampInMilliseconds / 1000)
            : undefined;

        const settings: aws.MediaConvert.JobSettings = {
            TimecodeConfig: { Source: 'ZEROBASED' },
            OutputGroups: [this._dummyOutputGroup(), ...outputGroupsCombinedRes.value],
            FollowSource: 1,
            Inputs: [
                {
                    AudioSelectors: { 'Audio Selector 1': { DefaultSelection: 'DEFAULT' } },
                    VideoSelector: {
                        Rotate: 'AUTO',
                    },
                    TimecodeSource: 'ZEROBASED',
                    FileInput: this._s3Service.getPrivateS3Url(source.s3Key),
                    ...(StartTimecode ? { InputClippings: [{ StartTimecode }] } : {}),
                },
            ],
        };

        const result = await this._runJob(settings);
        if (result.success === false) {
            logger.error('[AwsMediaConvertService] Error at _runJob', { error: result.type });
            return err(undefined);
        }

        return ok(
            // ignore the first output group (the dummy one)
            result.value.outputGroups.slice(1).map((g) => {
                assert(g.width);
                assert(g.height);
                assert.equal(g.s3keys.length, 1);
                return { s3keys: listKeysForFrameCapture(g.s3keys[0], '.jpeg'), width: g.width, height: g.height };
            })
        );
    }

    /** Returns an AWS S3 key */
    public async generateVideoThumbnail(
        source: {
            s3Key: string;
            durationInMilliseconds: number;
            widthInPixels: number;
            heightInPixels: number;
            startTimestampInMilliseconds: number;
        },
        outputGroup: {
            maxDimensionsPx: number;
            s3OutputKeyPrefix: string;
        }
    ): Promise<Result<{ s3key: string; width: number; height: number }, void>> {
        const res = await this.generateVideoThumbnails(source, [{ ...outputGroup, capturesCount: 1 }]);
        return res.map((groups) => {
            assert(groups.length === 1);
            const group = groups[0];
            assert(group.s3keys.length === 1);
            return { s3key: group.s3keys[0], width: group.width, height: group.height };
        });
    }

    /**
     * Only for testing on development machines (were we can’t use SQS).
     *
     * This function is not used in production
     */
    private async _pollJob(uuid: string, settings: aws.MediaConvert.JobSettings): Promise<JobRawResult> {
        logger.info('[AwsMediaConvertService] _pollJob: polling...', { uuid });
        // most recent jobs are at the beginning of the list
        const jobs = await this._awsMediaConvert.listJobs({});
        const job = jobs.Jobs?.find((j) => j.UserMetadata?.malouUploadV2Uuid === uuid);
        if (job) {
            logger.info('[AwsMediaConvertService] _pollJob: found job', { uuid, jobStatus: job.Status });
            if (job.Status === 'ERROR') {
                if (job.ErrorCode === undefined || job.ErrorMessage === undefined) {
                    assert.fail(`invalid job returned by AWS MediaConvert: ${JSON.stringify(job)}`);
                }
                return {
                    success: false,
                    type: 'errored',
                    awsCode: job.ErrorCode,
                    awsMessage: job.ErrorMessage,
                };
            }

            if (job.Status === 'COMPLETE') {
                // a completed job must have this field
                assert(job.OutputGroupDetails);

                const outputGroups = job.OutputGroupDetails.map((group, index) => {
                    assert(settings.OutputGroups);
                    const groupSettings = settings.OutputGroups[index];
                    const prefixUrlString = groupSettings?.OutputGroupSettings?.FileGroupSettings?.Destination;
                    assert(prefixUrlString);
                    const prefixUrl = new URL(prefixUrlString);
                    assert.equal(prefixUrl.protocol, 's3:');
                    const prefix = prefixUrl.pathname.slice(1);

                    // Hack to guess S3 keys chosen by AWS MediaConvert. In production we
                    // don’t need this because we have these S3 keys in the event we receive
                    // when the job ends.
                    assert(groupSettings.Outputs);
                    const s3keys = groupSettings.Outputs.flatMap((outputSettings) => {
                        const videoCodecSettings = outputSettings?.VideoDescription?.CodecSettings;
                        if (videoCodecSettings?.Codec === 'FRAME_CAPTURE') {
                            assert(videoCodecSettings.FrameCaptureSettings);
                            assert(videoCodecSettings.FrameCaptureSettings.MaxCaptures !== undefined);
                            // For some reason AWS gives us the last key only in this situation.
                            // The S3 key is formatted like `timeline_preview_256.0000003.jpeg`.
                            const lastId = videoCodecSettings.FrameCaptureSettings.MaxCaptures - 1;
                            return [prefix + '.' + lastId.toString().padStart(7, '0') + outputSettings.Extension];
                        }
                        return [prefix + outputSettings.Extension];
                    });
                    assert(group?.OutputDetails?.[0].DurationInMs);

                    const videoDetails = group.OutputDetails?.[0]?.VideoDetails;
                    return {
                        durationInMs: group.OutputDetails[0].DurationInMs,
                        s3keys,
                        width: videoDetails?.WidthInPx,
                        height: videoDetails?.HeightInPx,
                    };
                });

                return { success: true, value: { outputGroups } };
            }
        } else {
            logger.info('[AwsMediaConvertService] _pollJob: job not found (we will retry in a few seconds)');
        }

        await waitFor(2_000);
        return await this._pollJob(uuid, settings);
    }

    /**
     * Creates a MediaConvert job and wait until it ends (with AWS EventBridge, AWS SQS and
     * Redis pubsub).
     */
    private async _runJob(settings: aws.MediaConvert.JobSettings): Promise<JobRawResult> {
        let onEnd: ((result: JobRawResult) => void) | null = null;

        const uuid = randomUUID();

        let subscription: MediaConvertJobTracking.Subscription | undefined;

        if (process.env.I_AM_A !== 'developer') {
            subscription = await MediaConvertJobTracking.subscribe(uuid, async (event) => {
                assert(onEnd);
                assert(event);
                if (event.type === 'input_information') {
                    // do nothing
                } else if (event.type === 'canceled') {
                    onEnd({ success: false, type: 'canceled' });
                } else if (event.type === 'completed') {
                    onEnd({
                        success: true,
                        value: {
                            outputGroups: event.outputGroups.map((g) => ({
                                durationInMs: g.durationInMs,
                                s3keys: g.s3keys,
                                width: g.width,
                                height: g.height,
                            })),
                        },
                    });
                } else {
                    assert(event.type === 'errored');
                    onEnd({ success: false, type: 'errored', awsCode: event.awsCode, awsMessage: event.awsMessage });
                }
            });
        }

        try {
            const [result] = await Promise.all([
                (() => {
                    if (subscription === undefined) {
                        // for testing only
                        return this._pollJob(uuid, settings);
                    } else {
                        return new Promise<JobRawResult>((resolve) => {
                            onEnd = (r: JobRawResult): void => resolve(r);
                        });
                    }
                })(),

                (async (): Promise<void> => {
                    await this._awsMediaConvert.createJob({
                        Role: Config.services.awsMediaConvert.role,
                        Settings: settings,
                        BillingTagsSource: 'JOB',
                        AccelerationSettings: { Mode: 'DISABLED' },
                        UserMetadata: { malouUploadV2Uuid: uuid },
                    });
                    logger.info('[AwsMediaConvertService] mediaconvert job created', { uuid });
                })(),
            ]);

            return result;
        } finally {
            subscription?.unsubscribe();
        }
    }

    /**
     * A video/audio output is mandatory with AWS Media Convert, even if only want to extract thumbnails.
     *
     * This function returns the most cheap audio output group, compatible with the Basic tier
     */
    private _dummyOutputGroup(): aws.MediaConvert.OutputGroup {
        return {
            Outputs: [
                {
                    ContainerSettings: {
                        Container: 'RAW',
                    },
                    AudioDescriptions: [
                        {
                            AudioSourceName: 'Audio Selector 1',
                            CodecSettings: {
                                Codec: 'AAC',
                                AacSettings: {
                                    Bitrate: 8000,
                                    CodingMode: 'CODING_MODE_1_0',
                                    SampleRate: 8000,
                                },
                            },
                        },
                    ],
                },
            ],
            OutputGroupSettings: {
                Type: 'FILE_GROUP_SETTINGS',
                FileGroupSettings: {
                    Destination: this._s3Service.getPrivateS3Url('dummy_outputs/dummy_audio_output'),
                },
            },
        };
    }
}
