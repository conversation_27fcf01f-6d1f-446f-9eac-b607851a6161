import { inject, singleton } from 'tsyringe';

import { GetVideoInformationResponseDto } from '@malou-io/package-dto';

import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';

@singleton()
export class GetVideoInformationUseCase {
    constructor(
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}

    async execute(url: string): Promise<GetVideoInformationResponseDto> {
        const videoInformation = await this._multimediaStreamsInformationService.getSizeAndDuration(url);
        return videoInformation;
    }
}
