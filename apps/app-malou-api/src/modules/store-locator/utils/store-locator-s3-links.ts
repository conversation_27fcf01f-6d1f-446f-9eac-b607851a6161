import { StoreLocatorLanguage } from '@malou-io/package-utils';

interface StoreLocatorS3LinksBaseParams {
    configurationId: string;
    restaurantId: string;
    lang: StoreLocatorLanguage;
}

const S3_STORE_LOCATOR_BASE_PATH = 'store-locator/configuration';

export const STORE_LOCATOR_S3_LINKS = {
    base: (configurationId: string) => `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}`,
    favicon: (configurationId: string) => `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/favicons`,
    profilePictures: ({ configurationId, restaurantId, lang }: StoreLocatorS3LinksBaseParams) =>
        `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/pages/local/${restaurantId}/${lang}/reviews/profile-pictures`,
    instagram: (configurationId: string) => `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/pages/local/shared/social-networks/instagram`,
    localPage: ({ configurationId, restaurantId, lang }: StoreLocatorS3LinksBaseParams) =>
        `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/pages/local/${restaurantId}/${lang}`,
    mapPage: (configurationId: string) => `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/pages/centralization/map`,
    tempFolderForImagesDuplication: ({ configurationId, lang }: Pick<StoreLocatorS3LinksBaseParams, 'configurationId' | 'lang'>) =>
        `${S3_STORE_LOCATOR_BASE_PATH}/${configurationId}/pages/local/duplicated/${lang}`,
};
