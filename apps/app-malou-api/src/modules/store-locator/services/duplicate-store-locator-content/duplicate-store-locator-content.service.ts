import { singleton } from 'tsyringe';

import {
    filterByRequiredKeys,
    MalouErrorCode,
    PlatformKey,
    SocialNetworkKey,
    StoreLocatorContentDuplicationType,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GetStoreCallToActionsSuggestionsService } from ':modules/store-locator/services/get-store-call-to-actions-suggestions/get-store-call-to-actions-suggestions.service';
import {
    StoreLocatorBlockCta,
    StoreLocatorBlockCtaWithKey,
    StoreLocatorBlockImage,
} from ':modules/store-locator/use-cases/duplicate-store-page-content/duplicate-store-page-content.interface';
import { STORE_LOCATOR_S3_LINKS } from ':modules/store-locator/utils/store-locator-s3-links';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class DuplicateStoreLocatorContentService {
    constructor(
        private readonly _getStoreCallToActionsSuggestionsService: GetStoreCallToActionsSuggestionsService,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async handleCtasContentDuplication({
        restaurantId,
        data,
        targetRestaurantIds,
    }: {
        data: StoreLocatorBlockCta[];
        restaurantId: string;
        targetRestaurantIds: string[];
    }): Promise<{
        updatedCtasPerRestaurantId: Record<string, StoreLocatorBlockCta[]>;
        nonUpdatedRestaurantIds: string[];
        missingCtas: string[];
    }> {
        const [restaurantSuggestedCtas, suggestedCtaPerTargetRestaurantId] = await Promise.all([
            this._getStoreCallToActionsSuggestionsService.execute(restaurantId),
            Promise.all(
                targetRestaurantIds.map(async (id) => {
                    return {
                        restaurantId: id,
                        ctaSuggestions: await this._getStoreCallToActionsSuggestionsService.execute(id),
                    };
                })
            ),
        ]);

        // Get the keys of the CTAs that we will verify for duplication
        // custom CTAs will overwrite the value of target restaurants CTAs
        const eligibleCtaKeysToCheck = this._getEligibleCtasKeysToCheck(restaurantSuggestedCtas);

        const filteredRestaurantSuggestedCtas: Record<string, string> = Object.fromEntries(
            Object.entries(restaurantSuggestedCtas).filter(([key]) => eligibleCtaKeysToCheck.includes(key))
        );

        const ctaDataWithOriginalKey: StoreLocatorBlockCtaWithKey[] = data.map((cta) => {
            const keyForCtaLink = Object.entries(filteredRestaurantSuggestedCtas).find(([, url]) => url === cta.url);
            if (keyForCtaLink) {
                return {
                    text: cta.text,
                    url: cta.url,
                    key: keyForCtaLink[0],
                };
            }
            return {
                ...cta,
                key: null,
            };
        });
        const ctaDataOriginalKeys = filterByRequiredKeys(ctaDataWithOriginalKey, ['key']).map((cta) => cta.key);

        const missingCtas: string[] = [];
        // Filter target restaurants that have at least all the matched keys in their suggested CTAs
        const eligibleTargetRestaurantsToUpdate = suggestedCtaPerTargetRestaurantId.filter(({ ctaSuggestions }) => {
            const eligibleSuggestedCtasKeys = this._getEligibleCtasKeysToCheck(ctaSuggestions);
            const missingKeys = ctaDataOriginalKeys.filter((key) => !eligibleSuggestedCtasKeys.includes(key));
            for (const key of missingKeys) {
                if (!missingCtas.includes(key)) {
                    missingCtas.push(key);
                }
            }
            return missingKeys.length === 0;
        });

        if (eligibleTargetRestaurantsToUpdate.length === 0) {
            logger.info('[STORE_LOCATOR_CONTENT_DUPLICATION] No target restaurants eligible for CTA duplication', { restaurantId });
            return {
                updatedCtasPerRestaurantId: {},
                nonUpdatedRestaurantIds: targetRestaurantIds,
                missingCtas: ctaDataOriginalKeys,
            };
        }

        const updatedCtasPerRestaurantId: Record<string, StoreLocatorBlockCta[]> = eligibleTargetRestaurantsToUpdate.reduce(
            (acc, { restaurantId: id, ctaSuggestions }) => {
                const ctaSuggestionsKeys = Object.keys(ctaSuggestions);
                const updatedCtas = ctaDataWithOriginalKey.map((cta) => {
                    const { text, url, key } = cta;
                    if (key && ctaSuggestionsKeys.includes(key)) {
                        return {
                            text,
                            url: ctaSuggestions[key],
                        };
                    }
                    return {
                        text,
                        url,
                    };
                });
                acc[id] = updatedCtas;
                return acc;
            },
            {} as Record<string, StoreLocatorBlockCta[]>
        );

        return {
            updatedCtasPerRestaurantId,
            nonUpdatedRestaurantIds: targetRestaurantIds.filter((id) => !Object.keys(updatedCtasPerRestaurantId).includes(id)),
            missingCtas,
        };
    }

    async handleImagesDuplication({
        data,
        configurationId,
        contentDuplicationType,
        lang,
    }: {
        configurationId: string;
        data: StoreLocatorBlockImage[];
        lang: StoreLocatorLanguage;
        contentDuplicationType: StoreLocatorContentDuplicationType;
    }): Promise<StoreLocatorBlockImage[]> {
        // create temp folder for it if does not exist
        const targetS3KeyBase = STORE_LOCATOR_S3_LINKS.tempFolderForImagesDuplication({ configurationId, lang });
        let targetS3Key = '';
        switch (contentDuplicationType) {
            case StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA:
                targetS3Key = `${targetS3KeyBase}/information`;
                break;
            case StoreLocatorContentDuplicationType.GALLERY_IMAGES:
                targetS3Key = `${targetS3KeyBase}/gallery`;
                break;
            default:
                throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                    message: `[STORE_LOCATOR_CONTENT_DUPLICATION] Unsupported content duplication type for images duplication`,
                    metadata: { contentDuplicationType },
                });
        }

        try {
            await this._cloudStorageService.createFolderIfNotExists(targetS3Key);
        } catch (error) {
            throw new MalouError(MalouErrorCode.INTERNAL_SERVER_ERROR, {
                message: `[STORE_LOCATOR_CONTENT_DUPLICATION] Error creating temp folder for images duplication`,
                metadata: { contentDuplicationType, targetS3Key },
            });
        }

        // Duplicate each image object in the new target folder
        return await Promise.all(
            data.map(async (image) => {
                try {
                    const suffix = image.url.split('/').pop();
                    const newUrl = await this._cloudStorageService.copyObject(image.url, `${targetS3Key}/${suffix}`);
                    return {
                        url: newUrl,
                        description: image.description,
                    };
                } catch (error) {
                    logger.error(`[STORE_LOCATOR_CONTENT_DUPLICATION] Error copying image object for duplication`, {
                        contentDuplicationType,
                        targetS3Key,
                        imageUrl: image.url,
                        error,
                    });
                    return image;
                }
            })
        );
    }

    private _getEligibleCtasKeysToCheck(ctas: Record<string, string>): string[] {
        return Object.keys(ctas).filter((key) =>
            [
                'itinerary',
                'orderUrl',
                'reservationUrl',
                'menuUrl',
                ...Object.values(PlatformKey),
                ...Object.values(SocialNetworkKey),
            ].includes(key)
        );
    }
}
