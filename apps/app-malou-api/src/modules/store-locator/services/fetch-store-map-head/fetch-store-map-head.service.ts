import { singleton } from 'tsyringe';

import { GetStoreLocatorCentralizationPageDto, storeLocatorHeadBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorMapPage } from '@malou-io/package-models';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GenerateMapSchemaOrgMicroDataService } from ':modules/store-locator/services/generate-schema-org-micro-data/generate-map-micro-data.service';
import { mapStoreLocatorLanguageToLocale } from ':modules/store-locator/shared/utils';
import { StoreLocatorCentralizationPageRepository } from ':modules/store-locator/store-locator-centralization-page.repository';
import { STORE_LOCATOR_S3_LINKS } from ':modules/store-locator/utils/store-locator-s3-links';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class FetchStoreLocatorMapHeadBlockService {
    constructor(
        private readonly _storeLocatorCentralizationPageRepository: StoreLocatorCentralizationPageRepository,
        private readonly _cloudStorageService: AwsS3,
        private readonly _generateMapSchemaOrgMicroDataService: GenerateMapSchemaOrgMicroDataService
    ) {}

    async execute({
        storeLocatorOrganizationConfig,
        storeLocatorMapPage,
        alternateStoreLocatorMapPages,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorMapPage: IStoreLocatorMapPage;
        alternateStoreLocatorMapPages: IStoreLocatorMapPage[];
    }): Promise<GetStoreLocatorCentralizationPageDto['headBlock'] | null> {
        try {
            const configurationId = storeLocatorOrganizationConfig.id;
            const groupName = storeLocatorOrganizationConfig.name;
            const facebookImageUrl = storeLocatorMapPage.blocks.head.facebookImageUrl;
            const twitterImageUrl = storeLocatorMapPage.blocks.head.twitterImageUrl;
            const snippetImageUrl = storeLocatorMapPage.blocks.head.snippetImageUrl;
            const title = storeLocatorMapPage.blocks.head.title;
            const description = storeLocatorMapPage.blocks.head.description;
            const twitterDescription = storeLocatorMapPage.blocks.head.twitterDescription;
            const keywords = storeLocatorMapPage.blocks.head.keywords;
            const locale = mapStoreLocatorLanguageToLocale(storeLocatorMapPage.lang);
            const url = storeLocatorMapPage.fullUrl;
            const isLive = storeLocatorOrganizationConfig.isLive;
            const googleAnalytics = {
                id: Config.storeLocator.googleAnalyticsId,
                // todo store-locator check this after names in store locator config
                organizationId: storeLocatorOrganizationConfig.organization.name.trim().toLowerCase().replaceAll(/ /g, '-'),
                pageCategory: 'map-page',
            };
            const alternatePageUrls = this._getAlternateStoreLocatorMapPages({
                storeLocatorMapPages: alternateStoreLocatorMapPages,
                storeLocatorOrganizationConfig,
            });

            const googleAnalyticsClientId = storeLocatorOrganizationConfig.plugins?.googleAnalytics?.trackingId;

            const microdata = await this._generateMapSchemaOrgMicroDataService.execute({
                storeLocatorMapPage,
                alternateStoreLocatorMapPages,
                title,
                description,
                group: {
                    name: groupName,
                    logo: `${this._cloudStorageService.getBucketBaseUrl()}/${STORE_LOCATOR_S3_LINKS.favicon(configurationId)}/favicon.png`,
                },
            });

            const headBlock = {
                groupName: storeLocatorOrganizationConfig.name,
                googleAnalytics,
                ...(googleAnalyticsClientId && { googleAnalyticsClientId }),
                title,
                description,
                twitterDescription,
                keywords,
                microdata,
                facebookImageUrl,
                twitterImageUrl,
                snippetImageUrl,
                locale,
                url,
                alternatePageUrls,
                isLive,
            };

            const parsedHeadBlock = await storeLocatorHeadBlockValidator.parseAsync(headBlock);

            logger.info('[STORE_LOCATOR] [CENTRALIZATION_PAGE] [Head block] Head block is valid, updating it as backup and returning it');
            await this._storeLocatorCentralizationPageRepository.updateOne({
                filter: { _id: storeLocatorMapPage._id },
                update: { 'blocks.head.backup': parsedHeadBlock },
            });

            return headBlock;
        } catch (err) {
            logger.error('[STORE_LOCATOR] [CENTRALIZATION_PAGE] [Head block] Failed to fetch map head block, try to return backup', {
                err,
            });

            const storeLocatorMapPageHeadBackupBlock = storeLocatorMapPage.blocks?.head?.backup;
            if (storeLocatorMapPageHeadBackupBlock) {
                try {
                    const parsedHeadBlock = await storeLocatorHeadBlockValidator.parseAsync(storeLocatorMapPageHeadBackupBlock);
                    return parsedHeadBlock;
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [CENTRALIZATION_PAGE] [Head block] Failed to validate backup', { err: error });
                }
            }

            return null;
        }
    }

    private _getAlternateStoreLocatorMapPages({
        storeLocatorMapPages,
        storeLocatorOrganizationConfig,
    }: {
        storeLocatorMapPages?: IStoreLocatorMapPage[];
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
    }): { lang: string; url: string }[] {
        // If only 1 page, no need to link to other localized pages
        if (!storeLocatorMapPages || storeLocatorMapPages.length <= 1) {
            return [];
        }

        const defaultAlternateStoreLocatorMapPage =
            storeLocatorMapPages.find(({ lang }) => lang === storeLocatorOrganizationConfig.languages.primary) ??
            storeLocatorMapPages.find(({ lang }) => lang === StoreLocatorLanguage.EN) ??
            storeLocatorMapPages.find(({ lang }) => lang === StoreLocatorLanguage.FR);

        return [
            ...(defaultAlternateStoreLocatorMapPage
                ? [
                      {
                          lang: 'x-default',
                          url: defaultAlternateStoreLocatorMapPage.fullUrl,
                      },
                  ]
                : []),
            ...(storeLocatorMapPages.map(({ lang, fullUrl }) => ({ lang, url: fullUrl })) ?? []),
        ];
    }
}
