import { Router } from 'express';
import { singleton } from 'tsyringe';

import { apiKeyAuthorize } from ':modules/api-keys/middlewares';
import { getPagesFromCacheMiddleware } from ':modules/store-locator/middlewares/get-pages-cache.middleware';
import StoreLocatorController from ':modules/store-locator/store-locator.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoreLocatorRouter {
    constructor(private _storeLocatorController: StoreLocatorController) {}

    init(router: Router): void {
        router.get(
            '/store-locator/pages/:configurationId',
            apiKeyAuthorize,
            (req: any, res, next) => getPagesFromCacheMiddleware(req, res, next),
            (req: any, res, next) => this._storeLocatorController.handleGetPages(req, res, next)
        );

        router.get('/store-locator/configuration/:configurationId', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationConfiguration(req, res, next)
        );

        router.get('/store-locator/edit/store-pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetPagesForEdit(req, res, next)
        );

        router.get('/store-locator/edit/centralization-pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorCentralizationPages(req, res, next)
        );

        router.post('/store-locator/organization-configurations', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorOrganizationConfigurations(req, res, next)
        );

        router.get('/store-locator/organization-configuration/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorOrganizationConfiguration(req, res, next)
        );

        router.get('/store-locator/check-for-restaurant-pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleCheckForStoreLocatorRestaurantPages(req, res, next)
        );

        router.put('/store-locator/organization-configuration/ai-settings/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationAiSettings(req, res, next)
        );

        router.put('/store-locator/all-pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateRestaurantPages(req, res, next)
        );

        router.put('/store-locator/centralization-pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateCentralizationPages(req, res, next)
        );

        router.post('/store-locator/generate-page-content/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGeneratePageContent(req, res, next)
        );

        router.post('/store-locator/duplicate-page-content/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleDuplicatePagesContent(req, res, next)
        );

        router.put('/store-locator/organization-configuration/pages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationPages(req, res, next)
        );

        router.put('/store-locator/organization-configuration/languages/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationLanguages(req, res, next)
        );

        router.get('/store-locator/organization-jobs/:configurationId', (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationJobs(req, res, next)
        );

        router.get('/store-locator/generate/start/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStartStoreLocatorPagesGeneration(req, res, next)
        );

        router.get('/store-locator/generate/watch/:jobId/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleWatchStoreLocatorPagesGeneration(req, res, next)
        );

        router.get('/store-locator/publish/start/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStartStoreLocatorPublication(req, res, next)
        );

        router.get('/store-locator/:organizationId/send-subscription-request', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStoreLocatorSendSubscriptionRequest(req, res, next)
        );

        router.get('/store-locator/publish/watch/:jobId/:configurationId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleWatchStoreLocatorStorePublication(req, res, next)
        );

        // Webhooks triggered by Github Actions
        router.post('/store-locator/deployment/update-status', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleUpdateDeploymentStatus(req, res, next)
        );
    }
}
