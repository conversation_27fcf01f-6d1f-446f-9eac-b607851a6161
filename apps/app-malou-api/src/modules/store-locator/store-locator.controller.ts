import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    CheckForStoreLocatorRestaurantPagesParamsDto,
    checkForStoreLocatorRestaurantPagesParamsValidator,
    CheckForStoreLocatorRestaurantPagesResponseDto,
    DuplicateStoreLocatorStorePageContentBodyDto,
    duplicateStoreLocatorStorePageContentBodyValidator,
    DuplicateStoreLocatorStorePageContentParamsDto,
    duplicateStoreLocatorStorePageContentParamsValidator,
    GenerateStoreLocatorContentResponseDto,
    GenerateStoreLocatorStorePageContentBodyDto,
    generateStoreLocatorStorePageContentBodyValidator,
    GetOrganizationConfigurationParamsDto,
    getOrganizationConfigurationParamsValidator,
    GetStoreLocatorCentralizationDraftDto,
    GetStoreLocatorDraftPagesDto,
    GetStoreLocatorOrganizationConfigurationDto,
    GetStoreLocatorOrganizationConfigurationsRequestDto,
    getStoreLocatorOrganizationConfigurationsRequestDtoValidator,
    GetStoreLocatorOrganizationConfigurationsResponseDto,
    GetStoreLocatorOrganizationJobResponseDto,
    GetStoreLocatorOrganizationJobsParamsDto,
    getStoreLocatorOrganizationJobsParamsValidator,
    GetStoreLocatorPagesDto,
    GetStoreLocatorStoresParamsDto,
    getStoreLocatorStoresParamsValidator,
    HandleStartStoreLocatorPublicationResponseDto,
    StartStoreLocatorPagesGenerationParamsDto,
    startStoreLocatorPagesGenerationParamsValidator,
    StartStoreLocatorPagesGenerationResponseDto,
    StartStoreLocatorStorePublicationParamsDto,
    startStoreLocatorStorePublicationParamsValidator,
    StoreLocatorOrganizationConfigurationResponseDto,
    StoreLocatorStorePageContentDuplicationResponseDto,
    SuccessResponse,
    UpdateDeploymentStatusBodyDto,
    updateDeploymentStatusBodyValidator,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    updateOrganizationConfigurationAiSettingsBodyValidator,
    UpdateOrganizationConfigurationLanguagesBodyDto,
    updateOrganizationConfigurationLanguagesBodyValidator,
    UpdateOrganizationConfigurationPagesBodyDto,
    updateOrganizationConfigurationPagesBodyValidator,
    UpdateOrganizationConfigurationParamsDto,
    updateOrganizationConfigurationParamsValidator,
    UpdateStoreLocatorCentralizationPageParamsDto,
    updateStoreLocatorCentralizationPageParamsValidator,
    UpdateStoreLocatorCentralizationPagesBodyDto,
    updateStoreLocatorCentralizationPagesBodyValidator,
    UpdateStoreLocatorStorePageParamsDto,
    updateStoreLocatorStorePageParamsValidator,
    UpdateStoreLocatorStorePagesBodyDto,
    updateStoreLocatorStorePagesBodyValidator,
    WatchStoreLocatorJobResponseDto,
    WatchStoreLocatorPagesGenerationParamsDto,
    watchStoreLocatorPagesGenerationParamsValidator,
    watchStoreLocatorStorePublicationParamsValidator,
} from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { CheckForStoreLocatorRestaurantPagesUseCase } from ':modules/store-locator/use-cases/check-for-store-locator-restaurant-pages/check-for-store-locator-restaurant-pages.use-case';
import DuplicateStorePageContentUseCase from ':modules/store-locator/use-cases/duplicate-store-page-content/duplicate-store-page-content.use-case';
import { StartPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/start-pages-generation.use-case';
import { WatchPagesGenerationUseCase } from ':modules/store-locator/use-cases/generate-store-locator-pages/watch-pages-generation.use-case';
import { default as GenerateStoreLocatorStorePageContentUseCase } from ':modules/store-locator/use-cases/generate-store-page-content/generate-store-page-content.use-case';
import { GetOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-organization-configuration/get-organization-configuration.use-case';
import { GetStoreLocatorOrganizationJobsUseCase } from ':modules/store-locator/use-cases/get-organization-jobs/get-organization-jobs.use-case';
import { GetStoreLocatorCentralizationPagesForEditUseCase } from ':modules/store-locator/use-cases/get-store-locator-centralization-pages-for-edit/get-store-locator-centralization-pages-for-edit.use-case';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';
import { GetStoreLocatorOrganizationConfigurationsUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configurations/get-store-locator-organization-configurations';
import { GetStoreLocatorPagesUseCase } from ':modules/store-locator/use-cases/get-store-locator-pages/get-store-locator-pages.use-case';
import { GetStoreLocatorStorePagesForEditUseCase } from ':modules/store-locator/use-cases/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.use-case';
import { StartStoreLocatorPublicationUseCase } from ':modules/store-locator/use-cases/publish-store/start-store-publication.use-case';
import { WatchStoreLocatorPublicationUseCase } from ':modules/store-locator/use-cases/publish-store/watch-store-publication.use-case';
import { sendSubscriptionRequestNotificationUseCase } from ':modules/store-locator/use-cases/send-subscription-request-notification/send-subscription-request-notification.use-case';
import UpdateStoreLocatorCentralizationPagesUseCase from ':modules/store-locator/use-cases/update-centralization-pages/update-centralization-pages.use-case';
import UpdateOrganizationConfigAISettingsUseCase from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';
import UpdateOrganizationConfigLanguagesUseCase from ':modules/store-locator/use-cases/update-organization-config-languages/update-organization-config-languages.use-case';
import UpdateOrganizationConfigPagesUseCase from ':modules/store-locator/use-cases/update-organization-config-store-pages/update-organization-config-store-pages.use-case';
import { UpdateDeploymentJobStatusUseCase } from ':modules/store-locator/use-cases/update-store-deployment-job-status/update-store-deployment-job-status.use-case';
import UpdateStoreLocatorStorePagesUseCase from ':modules/store-locator/use-cases/update-store-pages/update-store-pages.use-case';

@singleton()
export default class StoreLocatorController {
    constructor(
        private readonly _getOrganizationConfigurationUseCase: GetOrganizationConfigurationUseCase,
        private readonly _updateOrganizationConfigAISettingsUseCase: UpdateOrganizationConfigAISettingsUseCase,
        private readonly _getStoreLocatorPagesUseCase: GetStoreLocatorPagesUseCase,
        private readonly _getStoreLocatorOrganizationConfigurationUseCase: GetStoreLocatorOrganizationConfigurationUseCase,
        private readonly _updateStoreLocatorStorePagesUseCase: UpdateStoreLocatorStorePagesUseCase,
        private readonly _updateStoreLocatorCentralizationPagesUseCase: UpdateStoreLocatorCentralizationPagesUseCase,
        private readonly _generateStoreLocatorStorePageContentUseCase: GenerateStoreLocatorStorePageContentUseCase,
        private readonly _updateOrganizationConfigPagesUseCase: UpdateOrganizationConfigPagesUseCase,
        private readonly _startPagesGenerationUseCase: StartPagesGenerationUseCase,
        private readonly _watchPagesGenerationUseCase: WatchPagesGenerationUseCase,
        private readonly _startStoreLocatorPublicationUseCase: StartStoreLocatorPublicationUseCase,
        private readonly _watchStoreLocatorStorePublicationUseCase: WatchStoreLocatorPublicationUseCase,
        private readonly _checkForStoreLocatorRestaurantPagesUseCase: CheckForStoreLocatorRestaurantPagesUseCase,
        private readonly _getStoreLocatorStorePagesForEditUseCase: GetStoreLocatorStorePagesForEditUseCase,
        private readonly _updateDeploymentJobStatusUseCase: UpdateDeploymentJobStatusUseCase,
        private readonly _getStoreLocatorOrganizationJobsUseCase: GetStoreLocatorOrganizationJobsUseCase,
        private readonly _updateOrganizationConfigLanguagesUseCase: UpdateOrganizationConfigLanguagesUseCase,
        private readonly _sendSubscriptionRequestNotificationUseCase: sendSubscriptionRequestNotificationUseCase,
        private readonly _getStoreLocatorCentralizationPagesForEditUseCase: GetStoreLocatorCentralizationPagesForEditUseCase,
        private readonly _getStoreLocatorOrganizationConfigurationsUseCase: GetStoreLocatorOrganizationConfigurationsUseCase,
        private readonly _duplicateStorePageContentUseCase: DuplicateStorePageContentUseCase
    ) {}

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPages(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._getStoreLocatorPagesUseCase.execute(configurationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetPagesForEdit(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorDraftPagesDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._getStoreLocatorStorePagesForEditUseCase.execute(configurationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetStoreLocatorCentralizationPages(
        req: Request<GetStoreLocatorStoresParamsDto, GetStoreLocatorCentralizationDraftDto, never>,
        res: Response<ApiResultV2<GetStoreLocatorCentralizationDraftDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._getStoreLocatorCentralizationPagesForEditUseCase.execute(configurationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Body(getStoreLocatorOrganizationConfigurationsRequestDtoValidator)
    async handleGetStoreLocatorOrganizationConfigurations(
        req: Request<never, GetStoreLocatorOrganizationConfigurationsResponseDto, GetStoreLocatorOrganizationConfigurationsRequestDto>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationConfigurationsResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationsIds } = req.body;
            const data = await this._getStoreLocatorOrganizationConfigurationsUseCase.execute(organizationsIds);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationConfigurationDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._getOrganizationConfigurationUseCase.execute(configurationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetStoreLocatorOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._getStoreLocatorOrganizationConfigurationUseCase.execute(configurationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationAiSettingsBodyValidator)
    async handleUpdateOrganizationConfigurationAiSettings(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationAiSettingsBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const { aiSettings } = req.body;
            const organizationConfig = await this._updateOrganizationConfigAISettingsUseCase.execute({ configurationId, aiSettings });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorStorePageParamsValidator)
    @Body(updateStoreLocatorStorePagesBodyValidator)
    async handleUpdateRestaurantPages(
        req: Request<UpdateStoreLocatorStorePageParamsDto, never, UpdateStoreLocatorStorePagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            await this._updateStoreLocatorStorePagesUseCase.execute({ configurationId, pages: req.body });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorCentralizationPageParamsValidator)
    @Body(updateStoreLocatorCentralizationPagesBodyValidator)
    async handleUpdateCentralizationPages(
        req: Request<UpdateStoreLocatorCentralizationPageParamsDto, never, UpdateStoreLocatorCentralizationPagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            await this._updateStoreLocatorCentralizationPagesUseCase.execute({ configurationId, pages: req.body });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoreLocatorStorePageParamsValidator)
    @Body(generateStoreLocatorStorePageContentBodyValidator)
    async handleGeneratePageContent(
        req: Request<UpdateStoreLocatorStorePageParamsDto, never, GenerateStoreLocatorStorePageContentBodyDto>,
        res: Response<ApiResultV2<GenerateStoreLocatorContentResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const { updates, type, params } = req.body;

            const data = await this._generateStoreLocatorStorePageContentUseCase.execute({
                configurationId,
                updates,
                type,
                params,
            });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(duplicateStoreLocatorStorePageContentParamsValidator)
    @Body(duplicateStoreLocatorStorePageContentBodyValidator)
    async handleDuplicatePagesContent(
        req: Request<DuplicateStoreLocatorStorePageContentParamsDto, never, DuplicateStoreLocatorStorePageContentBodyDto>,
        res: Response<ApiResultV2<StoreLocatorStorePageContentDuplicationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const data = await this._duplicateStorePageContentUseCase.execute({ data: req.body, configurationId });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationPagesBodyValidator)
    async handleUpdateOrganizationConfigurationPages(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationPagesBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            await this._updateOrganizationConfigPagesUseCase.execute({ configurationId, pages: req.body });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationParamsValidator)
    @Body(updateOrganizationConfigurationLanguagesBodyValidator)
    async handleUpdateOrganizationConfigurationLanguages(
        req: Request<UpdateOrganizationConfigurationParamsDto, never, UpdateOrganizationConfigurationLanguagesBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const { languages } = req.body;
            const organizationConfig = await this._updateOrganizationConfigLanguagesUseCase.execute({ configurationId, languages });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }

    @Params(checkForStoreLocatorRestaurantPagesParamsValidator)
    async handleCheckForStoreLocatorRestaurantPages(
        req: Request<CheckForStoreLocatorRestaurantPagesParamsDto, never, never>,
        res: Response<ApiResultV2<CheckForStoreLocatorRestaurantPagesResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { configurationId } = req.params;
        try {
            const data = await this._checkForStoreLocatorRestaurantPagesUseCase.execute(configurationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoreLocatorOrganizationJobsParamsValidator)
    async handleGetOrganizationJobs(
        req: Request<GetStoreLocatorOrganizationJobsParamsDto, GetStoreLocatorOrganizationJobResponseDto[]>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationJobResponseDto[], ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const jobs = await this._getStoreLocatorOrganizationJobsUseCase.execute(configurationId);
            return res.json({ data: jobs ?? [] });
        } catch (err) {
            next(err);
        }
    }

    @Params(startStoreLocatorPagesGenerationParamsValidator)
    async handleStartStoreLocatorPagesGeneration(
        req: Request<StartStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<StartStoreLocatorPagesGenerationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const jobId = await this._startPagesGenerationUseCase.execute(configurationId);
            return res.json({ data: { jobId } });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchStoreLocatorPagesGenerationParamsValidator)
    async handleWatchStoreLocatorPagesGeneration(
        req: Request<WatchStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<WatchStoreLocatorJobResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { configurationId, jobId } = req.params;
        try {
            const data = await this._watchPagesGenerationUseCase.execute({ configurationId, jobId });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(startStoreLocatorStorePublicationParamsValidator)
    async handleStartStoreLocatorPublication(
        req: Request<StartStoreLocatorStorePublicationParamsDto, never, never>,
        res: Response<ApiResultV2<HandleStartStoreLocatorPublicationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { configurationId } = req.params;
            const jobId = await this._startStoreLocatorPublicationUseCase.execute(configurationId);
            return res.json({ data: { jobId } });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchStoreLocatorStorePublicationParamsValidator)
    async handleWatchStoreLocatorStorePublication(
        req: Request<WatchStoreLocatorPagesGenerationParamsDto, never, never>,
        res: Response<ApiResultV2<WatchStoreLocatorJobResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        const { configurationId, jobId } = req.params;
        try {
            const data = await this._watchStoreLocatorStorePublicationUseCase.execute({ configurationId, jobId });
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Body(updateDeploymentStatusBodyValidator)
    async handleUpdateDeploymentStatus(
        req: Request<never, never, UpdateDeploymentStatusBodyDto>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { jobId, status, workflowRunId } = req.body;
            await this._updateDeploymentJobStatusUseCase.execute({ jobId, status, workflowRunId });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleStoreLocatorSendSubscriptionRequest(
        req: Request<{ organizationId: string }, never, never>,
        res: Response<ApiResultV2<SuccessResponse, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const userId = req.user._id;

            await this._sendSubscriptionRequestNotificationUseCase.execute({ organizationId, userId });
            return res.json({ data: { success: true } });
        } catch (err) {
            next(err);
        }
    }
}
