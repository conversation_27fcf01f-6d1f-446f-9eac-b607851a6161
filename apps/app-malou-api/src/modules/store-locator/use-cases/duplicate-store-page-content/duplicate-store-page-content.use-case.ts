import { singleton } from 'tsyringe';

import { DuplicateStoreLocatorStorePageContentBodyDto, StoreLocatorStorePageContentDuplicationResponseDto } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, StoreLocatorContentDuplicationType, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { DuplicateStoreLocatorContentService } from ':modules/store-locator/services/duplicate-store-locator-content/duplicate-store-locator-content.service';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import {
    StoreLocatorBlockCta,
    StoreLocatorBlockImage,
} from ':modules/store-locator/use-cases/duplicate-store-page-content/duplicate-store-page-content.interface';

@singleton()
export default class DuplicateStorePageContentUseCase {
    constructor(
        private readonly _storeLocatorRestaurantPagesRepository: StoreLocatorRestaurantPageRepository,
        private readonly _duplicateStoreLocatorContentService: DuplicateStoreLocatorContentService
    ) {}

    async execute({
        configurationId,
        data,
    }: {
        configurationId: string;
        data: DuplicateStoreLocatorStorePageContentBodyDto;
    }): Promise<StoreLocatorStorePageContentDuplicationResponseDto> {
        const { lang, restaurantId, contentDuplicationType } = data;

        const pagesToUpdate = await this._storeLocatorRestaurantPagesRepository.find({
            filter: {
                configurationId: toDbId(configurationId),
                lang: lang,
                restaurantId: { $ne: toDbId(restaurantId) },
                status: StoreLocatorPageStatus.DRAFT,
            },
            options: {
                lean: true,
            },
        });

        if (pagesToUpdate.length === 0) {
            logger.warn('[STORE_LOCATOR_CONTENT_DUPLICATION] No draft pages found to duplicate content into', { configurationId, lang });
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: 'No draft pages found to duplicate content into',
                metadata: { configurationId, lang },
            });
        }

        switch (contentDuplicationType) {
            case StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA:
                return this._handleInformationContentDuplication({
                    pagesToUpdate,
                    data,
                });
            case StoreLocatorContentDuplicationType.REVIEWS_CTA:
                return this._handleCtasDuplication({
                    pagesToUpdate,
                    restaurantId,
                    ctasData: data.reviews?.cta ? [data.reviews.cta] : undefined,
                    contentDuplicationType,
                });
            case StoreLocatorContentDuplicationType.CTAS_BUTTONS:
                return this._handleCtasDuplication({
                    pagesToUpdate,
                    restaurantId,
                    ctasData: data.callToActions?.links,
                    contentDuplicationType,
                });
            case StoreLocatorContentDuplicationType.GALLERY_IMAGES:
                return this._handleImagesDuplication({
                    pagesToUpdate,
                    imagesData: data.gallery?.images,
                    contentDuplicationType,
                });
            default:
                throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                    message: `[STORE_LOCATOR_CONTENT_DUPLICATION] Unsupported content duplication type: ${contentDuplicationType}`,
                    metadata: { contentDuplicationType },
                });
        }
    }

    private async _handleInformationContentDuplication({
        pagesToUpdate,
        data,
    }: {
        pagesToUpdate: IStoreLocatorRestaurantPage[];
        data: DuplicateStoreLocatorStorePageContentBodyDto;
    }): Promise<StoreLocatorStorePageContentDuplicationResponseDto> {
        const restaurantId = data.restaurantId;
        const targetRestaurantIds = pagesToUpdate.map((page) => page.restaurantId.toString());

        const informationBlockImages = await this._duplicateStoreLocatorContentService.handleImagesDuplication({
            data: data.information?.image ? [data.information.image] : [],
            configurationId: pagesToUpdate[0].configurationId.toString(),
            lang: pagesToUpdate[0].lang,
            contentDuplicationType: StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA,
        });

        const informationBlockCtas = data.information?.ctas;
        if (!informationBlockCtas) {
            logger.error('[STORE_LOCATOR_CONTENT_DUPLICATION] CTAs data is required for duplication in information block', {
                restaurantId,
            });
            return {
                nonUpdatedRestaurantIds: targetRestaurantIds,
                updatedRestaurantsData: pagesToUpdate.map((page) => ({
                    restaurantId: page.restaurantId.toString(),
                    lang: page.lang,
                    information: {
                        ...page.blocks.information,
                        image: informationBlockImages[0],
                    },
                })),
            };
        }

        const { nonUpdatedRestaurantIds, updatedCtasPerRestaurantId, missingCtas } =
            await this._duplicateStoreLocatorContentService.handleCtasContentDuplication({
                restaurantId,
                data: informationBlockCtas,
                targetRestaurantIds,
            });

        return {
            nonUpdatedRestaurantIds,
            updatedRestaurantsData: pagesToUpdate.map((page) => {
                const ctas = updatedCtasPerRestaurantId[page.restaurantId.toString()];
                return {
                    restaurantId: page.restaurantId.toString(),
                    lang: page.lang,
                    information: {
                        ...page.blocks.information,
                        ...(isNotNil(ctas) && {
                            ctas,
                        }),
                        image: informationBlockImages[0],
                    },
                };
            }),
            missingDuplicationData: { ctas: missingCtas },
        };
    }

    private async _handleCtasDuplication({
        pagesToUpdate,
        ctasData,
        restaurantId,
        contentDuplicationType,
    }: {
        pagesToUpdate: IStoreLocatorRestaurantPage[];
        restaurantId: string;
        ctasData: StoreLocatorBlockCta[] | undefined;
        contentDuplicationType: StoreLocatorContentDuplicationType;
    }): Promise<StoreLocatorStorePageContentDuplicationResponseDto> {
        if (!ctasData) {
            throw new MalouError(MalouErrorCode.BODY_VALIDATION_ERROR, {
                message: `[STORE_LOCATOR_CONTENT_DUPLICATION] CTAs data is required for duplication`,
                metadata: { contentDuplicationType },
            });
        }
        const targetRestaurantIds = pagesToUpdate.map((page) => page.restaurantId.toString());

        const { nonUpdatedRestaurantIds, updatedCtasPerRestaurantId, missingCtas } =
            await this._duplicateStoreLocatorContentService.handleCtasContentDuplication({
                restaurantId,
                data: ctasData,
                targetRestaurantIds,
            });

        if (Object.keys(updatedCtasPerRestaurantId).length === 0) {
            return {
                nonUpdatedRestaurantIds,
                updatedRestaurantsData: [],
                missingDuplicationData: { ctas: missingCtas },
            };
        }

        const updatedRestaurantsData: StoreLocatorStorePageContentDuplicationResponseDto['updatedRestaurantsData'] = pagesToUpdate
            .filter((page) => !nonUpdatedRestaurantIds.includes(page.restaurantId.toString()))
            .map((page) => ({
                restaurantId: page.restaurantId.toString(),
                lang: page.lang,
                ...(contentDuplicationType === StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA && {
                    information: {
                        ...page.blocks.information,
                        ctas: updatedCtasPerRestaurantId[page.restaurantId.toString()] || null,
                    },
                }),
                ...(contentDuplicationType === StoreLocatorContentDuplicationType.CTAS_BUTTONS && {
                    callToActions: {
                        ...page.blocks.callToActions,
                        links: updatedCtasPerRestaurantId[page.restaurantId.toString()] || null,
                    },
                }),
                ...(contentDuplicationType === StoreLocatorContentDuplicationType.REVIEWS_CTA && {
                    reviews: {
                        ...page.blocks.reviews,
                        cta: updatedCtasPerRestaurantId[page.restaurantId.toString()]?.[0] || null,
                    },
                }),
            }));

        return {
            nonUpdatedRestaurantIds,
            updatedRestaurantsData,
            missingDuplicationData: { ctas: missingCtas },
        };
    }

    private async _handleImagesDuplication({
        pagesToUpdate,
        imagesData,
        contentDuplicationType,
    }: {
        pagesToUpdate: IStoreLocatorRestaurantPage[];
        imagesData: StoreLocatorBlockImage[] | undefined;
        contentDuplicationType: StoreLocatorContentDuplicationType;
    }): Promise<StoreLocatorStorePageContentDuplicationResponseDto> {
        if (!imagesData) {
            throw new MalouError(MalouErrorCode.BODY_VALIDATION_ERROR, {
                message: `[STORE_LOCATOR_CONTENT_DUPLICATION] images data is required for duplication`,
                metadata: { contentDuplicationType },
            });
        }

        const newImagesData = await this._duplicateStoreLocatorContentService.handleImagesDuplication({
            data: imagesData,
            configurationId: pagesToUpdate[0].configurationId.toString(),
            lang: pagesToUpdate[0].lang,
            contentDuplicationType,
        });

        const updatedRestaurantsData: StoreLocatorStorePageContentDuplicationResponseDto['updatedRestaurantsData'] = pagesToUpdate.map(
            (page) => ({
                restaurantId: page.restaurantId.toString(),
                lang: page.lang,
                ...(contentDuplicationType === StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA && {
                    information: {
                        ...page.blocks.information,
                        image: newImagesData[0],
                    },
                }),
                ...(contentDuplicationType === StoreLocatorContentDuplicationType.GALLERY_IMAGES && {
                    gallery: {
                        ...page.blocks.gallery,
                        images: newImagesData,
                    },
                }),
            })
        );

        return {
            nonUpdatedRestaurantIds: [],
            updatedRestaurantsData,
        };
    }
}
