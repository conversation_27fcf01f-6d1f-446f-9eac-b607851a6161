import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import {
    CountryCode,
    PlatformKey,
    SocialNetworkKey,
    StoreLocatorContentDuplicationType,
    StoreLocatorLanguage,
    StoreLocatorPageStatus,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultStoreLocatorRestaurantPage } from ':modules/store-locator/builders/store-locator-restaurant-page.builder';
import DuplicateStorePageContentUseCase from ':modules/store-locator/use-cases/duplicate-store-page-content/duplicate-store-page-content.use-case';
import { CloudStorage } from ':plugins/cloud-storage/cloud-storage.interface';
import { AwsS3 } from ':plugins/cloud-storage/s3';

describe('DuplicateStorePageContentUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PlatformsRepository', 'RestaurantsRepository', 'StoreLocatorRestaurantPageRepository']);
        container.registerInstance(AwsS3, {
            createFolderIfNotExists: jest.fn().mockResolvedValue(void 0),
            copyObject: jest.fn().mockImplementation((_sourceKey: string, targetKey: string) => {
                return targetKey;
            }),
        } as unknown as CloudStorage);
    });

    describe('execute', () => {
        it('should correctly duplicate information block data from restaurant 1 to restaurant 2 ', async () => {
            const useCase = container.resolve(DuplicateStorePageContentUseCase);
            const restaurant2Id = newDbId();
            const restaurant1Id = newDbId();

            const configurationId = newDbId();

            const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'storeLocatorRestaurantPage'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .name('Restaurant 1')
                                    ._id(restaurant1Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant1' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant1' },
                                    ])
                                    .address({
                                        formattedAddress: '123 Main St, Cityville',
                                        locality: 'Cityville',
                                        postalCode: '12345',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                                getDefaultRestaurant()
                                    .name('Restaurant 2')
                                    ._id(restaurant2Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant2' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant2' },
                                    ])
                                    .address({
                                        formattedAddress: '456 Side St, Townsville',
                                        locality: 'Townsville',
                                        postalCode: '67890',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurant1Id)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialLink('https://www.facebook.com/platform-restaurant1')
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorRestaurantPage: {
                        data() {
                            return [
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant1Id)
                                    .relativePath('/restaurant-page-1')
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 1',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image.jpg',
                                                description: 'A beautiful restaurant image',
                                            },
                                            ctas: [
                                                {
                                                    text: 'custom link',
                                                    url: 'https://example.com/book',
                                                },
                                                {
                                                    text: 'Instagram',
                                                    url: 'https://www.instagram.com/restaurant1',
                                                },
                                                {
                                                    text: 'Facebook',
                                                    url: 'https://www.facebook.com/restaurant1',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant2Id)
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .relativePath('/restaurant-page-2')
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 2',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image2.jpg',
                                                description: 'A beautiful restaurant image2',
                                            },
                                            ctas: [
                                                {
                                                    text: 'instagram',
                                                    url: 'https://www.instagram.com/restaurant2',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const restaurant2page = dependencies.storeLocatorRestaurantPage[1];
                    return {
                        nonUpdatedRestaurantIds: [],
                        missingDuplicationData: {
                            ctas: [],
                        },
                        updatedRestaurantsData: [
                            {
                                restaurantId: restaurant2Id.toString(),
                                lang: StoreLocatorLanguage.FR,
                                information: {
                                    ...restaurant2page.blocks.information,
                                    ctas: [
                                        {
                                            text: 'custom link',
                                            url: 'https://example.com/book',
                                        },
                                        {
                                            text: 'Instagram',
                                            url: 'https://www.instagram.com/restaurant2',
                                        },
                                        {
                                            text: 'Facebook',
                                            url: 'https://www.facebook.com/restaurant2',
                                        },
                                    ],
                                    image: {
                                        url: `store-locator/configuration/${restaurant2page.configurationId}/pages/local/duplicated/${restaurant2page.lang}/information/image.jpg`,
                                        description: 'A beautiful restaurant image',
                                    },
                                },
                            },
                        ],
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurant1Page = seededObjects.storeLocatorRestaurantPage[0];

            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                configurationId: configurationId.toString(),
                data: {
                    restaurantId: restaurant1Id.toString(),
                    lang: StoreLocatorLanguage.FR,
                    information: restaurant1Page.blocks.information,
                    contentDuplicationType: StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA,
                },
            });
            expect(result).toEqual(expectedResult);
        });

        it('should correctly duplicate information block data from restaurant 1 to restaurant 2 and restaurant 3', async () => {
            const useCase = container.resolve(DuplicateStorePageContentUseCase);

            const restaurant1Id = newDbId();
            const restaurant2Id = newDbId();
            const restaurant3Id = newDbId();

            const configurationId = newDbId();

            const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'storeLocatorRestaurantPage'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .name('Restaurant 1')
                                    ._id(restaurant1Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant1' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant1' },
                                    ])
                                    .address({
                                        formattedAddress: '123 Main St, Cityville',
                                        locality: 'Cityville',
                                        postalCode: '12345',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                                getDefaultRestaurant()
                                    .name('Restaurant 2')
                                    ._id(restaurant2Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant2' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant2' },
                                    ])
                                    .address({
                                        formattedAddress: '456 Side St, Townsville',
                                        locality: 'Townsville',
                                        postalCode: '67890',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                                getDefaultRestaurant()
                                    .name('Restaurant 3')
                                    ._id(restaurant3Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant3' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant3' },
                                    ])
                                    .address({
                                        formattedAddress: '789 Another Rd, Villageville',
                                        locality: 'Villageville',
                                        postalCode: '10112',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurant1Id)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialLink('https://www.facebook.com/platform-restaurant1')
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurant3Id)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialLink('https://www.facebook.com/platform-restaurant2')
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorRestaurantPage: {
                        data() {
                            return [
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant1Id)
                                    .relativePath('/restaurant-page-1')
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 1',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image.jpg',
                                                description: 'A beautiful restaurant image',
                                            },
                                            ctas: [
                                                {
                                                    text: 'custom link',
                                                    url: 'https://example.com/book',
                                                },
                                                {
                                                    text: 'Instagram',
                                                    url: 'https://www.instagram.com/restaurant1',
                                                },
                                                {
                                                    text: 'Facebook',
                                                    url: 'https://www.facebook.com/restaurant1',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant2Id)
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .relativePath('/restaurant-page-2')
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 2',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image2.jpg',
                                                description: 'A beautiful restaurant image2',
                                            },
                                            ctas: [
                                                {
                                                    text: 'Instagram',
                                                    url: 'https://www.instagram.com/restaurant2',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant3Id)
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .relativePath('/restaurant-page-3')
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 2',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image3.jpg',
                                                description: 'A beautiful restaurant image3',
                                            },
                                            ctas: [],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const restaurant2page = dependencies.storeLocatorRestaurantPage[1];
                    return {
                        nonUpdatedRestaurantIds: [],
                        missingDuplicationData: {
                            ctas: [],
                        },
                        updatedRestaurantsData: [
                            {
                                restaurantId: restaurant2Id.toString(),
                                lang: StoreLocatorLanguage.FR,
                                information: {
                                    ...restaurant2page.blocks.information,
                                    ctas: [
                                        {
                                            text: 'custom link',
                                            url: 'https://example.com/book',
                                        },
                                        {
                                            text: 'Instagram',
                                            url: 'https://www.instagram.com/restaurant2',
                                        },
                                        {
                                            text: 'Facebook',
                                            url: 'https://www.facebook.com/restaurant2',
                                        },
                                    ],
                                    image: {
                                        url: `store-locator/configuration/${restaurant2page.configurationId}/pages/local/duplicated/${restaurant2page.lang}/information/image.jpg`,
                                        description: 'A beautiful restaurant image',
                                    },
                                },
                            },
                            {
                                restaurantId: restaurant3Id.toString(),
                                lang: StoreLocatorLanguage.FR,
                                information: {
                                    ...restaurant2page.blocks.information,
                                    ctas: [
                                        {
                                            text: 'custom link',
                                            url: 'https://example.com/book',
                                        },
                                        {
                                            text: 'Instagram',
                                            url: 'https://www.instagram.com/restaurant3',
                                        },
                                        {
                                            text: 'Facebook',
                                            url: 'https://www.facebook.com/restaurant3',
                                        },
                                    ],
                                    image: {
                                        url: `store-locator/configuration/${restaurant2page.configurationId}/pages/local/duplicated/${restaurant2page.lang}/information/image.jpg`,
                                        description: 'A beautiful restaurant image',
                                    },
                                },
                            },
                        ],
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurant1Page = seededObjects.storeLocatorRestaurantPage[0];

            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                configurationId: configurationId.toString(),
                data: {
                    restaurantId: restaurant1Id.toString(),
                    lang: StoreLocatorLanguage.FR,
                    information: restaurant1Page.blocks.information,
                    contentDuplicationType: StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA,
                },
            });
            expect(result).toEqual(expectedResult);
        });

        it('should not duplicate information block cta data from restaurant 1 to restaurant 2 because of missing necessary ctas', async () => {
            const useCase = container.resolve(DuplicateStorePageContentUseCase);

            const restaurant1Id = newDbId();
            const restaurant2Id = newDbId();

            const configurationId = newDbId();

            const testCase = new TestCaseBuilderV2<'platforms' | 'restaurants' | 'storeLocatorRestaurantPage'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant()
                                    .name('Restaurant 1')
                                    ._id(restaurant1Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([
                                        { key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant1' },
                                        { key: SocialNetworkKey.INSTAGRAM, url: 'https://www.instagram.com/restaurant1' },
                                    ])
                                    .address({
                                        formattedAddress: '123 Main St, Cityville',
                                        locality: 'Cityville',
                                        postalCode: '12345',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                                getDefaultRestaurant()
                                    .name('Restaurant 2')
                                    ._id(restaurant2Id)
                                    .storeLocatorConfigId(configurationId)
                                    .socialNetworkUrls([{ key: SocialNetworkKey.FACEBOOK, url: 'https://www.facebook.com/restaurant2' }])
                                    .address({
                                        formattedAddress: '456 Side St, Townsville',
                                        locality: 'Townsville',
                                        postalCode: '67890',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurant1Id)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialLink('https://www.facebook.com/platform-restaurant1')
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorRestaurantPage: {
                        data() {
                            return [
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant1Id)
                                    .relativePath('/restaurant-page-1')
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 1',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image1.jpg',
                                                description: 'A beautiful restaurant image1',
                                            },
                                            ctas: [
                                                {
                                                    text: 'custom link',
                                                    url: 'https://example.com/book',
                                                },
                                                {
                                                    text: 'Instagram',
                                                    url: 'https://www.instagram.com/restaurant1',
                                                },
                                                {
                                                    text: 'Facebook',
                                                    url: 'https://www.facebook.com/restaurant1',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                                getDefaultStoreLocatorRestaurantPage()
                                    .configurationId(configurationId)
                                    .restaurantId(restaurant2Id)
                                    .lang(StoreLocatorLanguage.FR)
                                    .status(StoreLocatorPageStatus.DRAFT)
                                    .relativePath('/restaurant-page-2')
                                    .blocks({
                                        head: {
                                            title: 'Restaurant Page 2',
                                            facebookImageUrl: 'https://example.com/facebook-image.jpg',
                                            twitterImageUrl: 'https://example.com/twitter-image.jpg',
                                            description: 'A description of the restaurant page',
                                            keywords: 'restaurant, food, dining',
                                            snippetImageUrl: 'https://example.com/snippet-image.jpg',
                                            twitterDescription: 'A brief description for Twitter',
                                        },
                                        information: {
                                            title: 'Restaurant Name',
                                            image: {
                                                url: 'https://example.com/image.jpg',
                                                description: 'A beautiful restaurant image',
                                            },
                                            ctas: [
                                                {
                                                    text: 'Instagram',
                                                    url: 'https://www.instagram.com/restaurant2',
                                                },
                                            ],
                                        },
                                        gallery: {
                                            title: 'Gallery',
                                            subtitle: 'Our Photos',
                                            images: [],
                                        },
                                        reviews: {
                                            title: 'Reviews',
                                            cta: {
                                                text: 'Leave a Review',
                                                url: 'https://example.com/review',
                                            },
                                        },
                                        callToActions: {
                                            title: 'Actions',
                                            ctas: [],
                                        },
                                        descriptions: {
                                            items: [],
                                        },
                                        socialNetworks: {
                                            title: 'Social Networks',
                                        },
                                        faq: {
                                            title: 'Frequently Asked Questions',
                                            items: [],
                                        },
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const restaurant2page = dependencies.storeLocatorRestaurantPage[1];
                    return {
                        nonUpdatedRestaurantIds: [restaurant2Id.toString()],
                        missingDuplicationData: {
                            ctas: ['instagram', 'facebook'],
                        },
                        updatedRestaurantsData: [
                            {
                                restaurantId: restaurant2Id.toString(),
                                lang: StoreLocatorLanguage.FR,
                                information: {
                                    ...restaurant2page.blocks.information,
                                    image: {
                                        url: `store-locator/configuration/${restaurant2page.configurationId}/pages/local/duplicated/${restaurant2page.lang}/information/image1.jpg`,
                                        description: 'A beautiful restaurant image1',
                                    },
                                },
                            },
                        ],
                    };
                },
            });
            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const restaurant1Page = seededObjects.storeLocatorRestaurantPage[0];

            const expectedResult = testCase.getExpectedResult();

            const result = await useCase.execute({
                configurationId: configurationId.toString(),
                data: {
                    restaurantId: restaurant1Id.toString(),
                    lang: StoreLocatorLanguage.FR,
                    information: restaurant1Page.blocks.information,
                    contentDuplicationType: StoreLocatorContentDuplicationType.INFORMATION_IMAGE_AND_CTA,
                },
            });
            expect(result).toEqual(expectedResult);
        });
    });
});
