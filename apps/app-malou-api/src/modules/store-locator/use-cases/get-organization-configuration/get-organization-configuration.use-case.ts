import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';

import { Config } from ':config';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { STORE_LOCATOR_S3_LINKS } from ':modules/store-locator/utils/store-locator-s3-links';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class GetOrganizationConfigurationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute(configurationId: string): Promise<GetStoreLocatorOrganizationConfigurationDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(configurationId);
        assert(storeLocatorOrganizationConfig?.organization);

        const favIconUrl = `${this._cloudStorageService.getBucketBaseUrl()}/${STORE_LOCATOR_S3_LINKS.favicon(configurationId)}/favicon.png`;
        const s3BucketName = `s3://store-locator-${Config.env}-${configurationId}`;

        return {
            cloudfrontDistributionId: storeLocatorOrganizationConfig.cloudfrontDistributionId,
            groupName: storeLocatorOrganizationConfig.name,
            baseUrl: storeLocatorOrganizationConfig.baseUrl,
            styles: storeLocatorOrganizationConfig.styles,
            favIconUrl,
            s3BucketName,
        };
    }
}
