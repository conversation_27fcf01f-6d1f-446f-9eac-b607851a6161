import { Job } from 'agenda';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorMapPage, IStoreLocatorRestaurantPageWithRestaurant, toDbId } from '@malou-io/package-models';
import { isNotNil, StoreLocatorJobStatus, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { GenerateMediaDescriptionImageType } from ':modules/ai/interfaces/ai.interfaces';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { PublishStoreLocatorDataAttributes } from ':modules/store-locator/jobs/publish-store-locator';
import { PreprocessStoreLocatorPictureService } from ':modules/store-locator/services/preprocess-store-locator-picture/preprocess-store-locator-picture.service';
import { StoreLocatorCentralizationPageRepository } from ':modules/store-locator/store-locator-centralization-page.repository';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';
import { STORE_LOCATOR_S3_LINKS } from ':modules/store-locator/utils/store-locator-s3-links';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class ProcessStoreLocatorPublicationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorCentralizationPageRepository: StoreLocatorCentralizationPageRepository,
        private readonly _cloudStorageService: AwsS3,
        private readonly _preprocessStoreLocatorPictureService: PreprocessStoreLocatorPictureService,
        private readonly _agendaSingleton: AgendaSingleton
    ) {}

    async execute({ configurationId, job }: { configurationId: string; job: Job<PublishStoreLocatorDataAttributes> }): Promise<void> {
        const [storeLocatorOrganizationConfig, storeLocatorRestaurantsPages, storeLocatorCentralizationPages] = await Promise.all([
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(configurationId),
            this._storeLocatorRestaurantPageRepository.find({
                filter: { configurationId: toDbId(configurationId), status: StoreLocatorPageStatus.DRAFT, hasBeenUpdated: true },
                options: { populate: [{ path: 'restaurant' }], lean: true },
            }),
            this._storeLocatorCentralizationPageRepository.find({
                filter: { configurationId: toDbId(configurationId), status: StoreLocatorPageStatus.DRAFT, hasBeenUpdated: true },
                options: { lean: true },
            }),
        ]);

        assert(storeLocatorOrganizationConfig, 'Store Locator Organization Config not found');
        assert(storeLocatorRestaurantsPages, 'Store Locator Restaurants Pages not found');

        // Process and publish all modified store locator restaurant pages
        const promises = storeLocatorRestaurantsPages.map((storeLocatorRestaurantPage) =>
            this._processStoreLocatorPage({
                storeLocatorOrganizationConfig,
                storeLocatorRestaurantPage,
            })
        );

        if (storeLocatorCentralizationPages) {
            promises.push(
                ...storeLocatorCentralizationPages.map((storeLocatorCentralizationPage) =>
                    this._processStoreLocatorCentralizationPage({
                        storeLocatorOrganizationConfig,
                        storeLocatorCentralizationPage,
                    })
                )
            );
        }

        await Promise.all(promises);

        // Update configuration configurations styles
        await this._updateConfigurationStyles(storeLocatorOrganizationConfig);

        // Trigger deployment job and attach to the publication job
        await this._triggerDeploymentJobAndAttachToPublicationJob({ configurationId, job });
    }

    private async _updateConfigurationStyles(storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration): Promise<void> {
        const newPageStyles = {
            ...storeLocatorOrganizationConfig.styles.pages,
            store: {
                ...storeLocatorOrganizationConfig.styles.pages.store,
                ...storeLocatorOrganizationConfig.styles.pages.storeDraft,
            },
            map: {
                ...storeLocatorOrganizationConfig.styles.pages.map,
                ...storeLocatorOrganizationConfig.styles.pages.mapDraft,
            },
            storeDraft: {},
            mapDraft: {},
        };
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: { _id: toDbId(storeLocatorOrganizationConfig.id) },
            update: { 'styles.pages': newPageStyles },
        });
    }

    private async _triggerDeploymentJobAndAttachToPublicationJob({
        configurationId,
        job,
    }: {
        configurationId: string;
        job: Job<PublishStoreLocatorDataAttributes>;
    }): Promise<void> {
        const deploymentJob = await this._agendaSingleton.now(AgendaJobName.STORE_LOCATOR_DEPLOYMENT, {
            configurationId,
            status: StoreLocatorJobStatus.PENDING,
        });
        if (deploymentJob.attrs._id && job.attrs.data) {
            job.attrs.data.deploymentJobId = deploymentJob.attrs._id.toString();
        }
        await job.save();
    }

    private async _processStoreLocatorPage({
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPageWithRestaurant;
    }): Promise<void> {
        const images = await this._processImages({
            storeLocatorOrganizationConfig,
            storeLocatorRestaurantPage,
        });

        // Update current draft
        const draftWithProcessedImages = this._storeLocatorRestaurantPageRepository.toDocumentWithModifications(
            storeLocatorRestaurantPage,
            {
                restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                lang: storeLocatorRestaurantPage.lang,
                information: {
                    title: storeLocatorRestaurantPage.blocks.information.title,
                    image: images.information[0],
                    ctas: storeLocatorRestaurantPage.blocks.information.ctas,
                },
                gallery: {
                    title: storeLocatorRestaurantPage.blocks.gallery.title,
                    subtitle: storeLocatorRestaurantPage.blocks.gallery.subtitle,
                    images: images.gallery,
                },
                descriptions: {
                    items: storeLocatorRestaurantPage.blocks.descriptions.items.map((item, index) => ({
                        ...item,
                        image: images.descriptions[index],
                    })),
                },
            }
        );
        const updatedDraft = await this._storeLocatorRestaurantPageRepository.findOneAndUpdate({
            filter: {
                _id: storeLocatorRestaurantPage._id,
            },
            update: {
                ...draftWithProcessedImages,
                hasBeenUpdated: false,
            },
            options: { lean: true },
        });

        // Remove published page and replace it with the updated draft
        if (updatedDraft) {
            const { _id, createdAt: _createdAt, updatedAt: _updatedAt, status: _status, ...newPageToPublish } = updatedDraft;

            await this._storeLocatorRestaurantPageRepository.atomicUpsert({
                filter: {
                    restaurantId: updatedDraft.restaurantId,
                    configurationId: toDbId(storeLocatorOrganizationConfig.id),
                    lang: updatedDraft.lang,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
                update: {
                    ...newPageToPublish,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
            });
        }
    }

    private async _processStoreLocatorCentralizationPage({
        storeLocatorOrganizationConfig,
        storeLocatorCentralizationPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorCentralizationPage: IStoreLocatorMapPage;
    }): Promise<void> {
        const updatedImages = await this._processCentralizationPageImages(storeLocatorCentralizationPage);

        // Remove published page and replace it with the updated draft

        const updatedDraft = await this._storeLocatorCentralizationPageRepository.findOneAndUpdate({
            filter: {
                _id: storeLocatorCentralizationPage._id,
            },
            update: {
                hasBeenUpdated: false,
                'blocks.map': updatedImages.map,
                'blocks.head.facebookImageUrl': updatedImages.head.facebookImageUrl,
                'blocks.head.twitterImageUrl': updatedImages.head.twitterImageUrl,
                'blocks.head.snippetImageUrl': updatedImages.head.snippetImageUrl,
            },
            options: { lean: true },
        });

        if (updatedDraft) {
            const { _id, createdAt: _createdAt, updatedAt: _updatedAt, status: _status, ...newPageToPublish } = updatedDraft;

            await this._storeLocatorCentralizationPageRepository.atomicUpsert({
                filter: {
                    configurationId: toDbId(storeLocatorOrganizationConfig.id),
                    lang: updatedDraft.lang,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
                update: {
                    ...newPageToPublish,
                    status: StoreLocatorPageStatus.PUBLISHED,
                },
            });
        }
    }

    private async _processImages({
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPageWithRestaurant;
    }): Promise<Record<'information' | 'gallery' | 'descriptions', { url: string; description: string }[]>> {
        const configurationId = storeLocatorOrganizationConfig.id;

        const restaurantId = storeLocatorRestaurantPage.restaurantId.toString();
        // todo store-locator: intersection with restaurant keywords ?
        const keywords = storeLocatorOrganizationConfig.aiSettings.keywords.map((keyword) => keyword.text);
        const lang = storeLocatorRestaurantPage.lang;
        const restaurantName = storeLocatorRestaurantPage.restaurant.name;

        const medias: Record<'information' | 'gallery' | 'descriptions', { url: string; description: string }[]> = {
            information: [storeLocatorRestaurantPage.blocks.information.image],
            gallery: storeLocatorRestaurantPage.blocks.gallery.images,
            descriptions: storeLocatorRestaurantPage.blocks.descriptions.items.map((item) => item.image),
        };
        const imageTypeMapping: Record<'information' | 'gallery' | 'descriptions', GenerateMediaDescriptionImageType> = {
            information: GenerateMediaDescriptionImageType.ALT_TEXT_INFORMATION_BLOCK,
            gallery: GenerateMediaDescriptionImageType.ALT_TEXT_GALLERY_BLOCK,
            descriptions: GenerateMediaDescriptionImageType.ALT_TEXT_DESCRIPTIONS_BLOCK,
        };

        await Promise.all(
            Object.entries(medias).map(async ([blockType, images]) => {
                medias[blockType] = await Promise.all(
                    images.map(async (image, index) => {
                        try {
                            if (
                                !this._shouldBeProcessed({
                                    imageUrl: image.url,
                                    configurationId,
                                })
                            ) {
                                return image;
                            }

                            const s3Key = `${STORE_LOCATOR_S3_LINKS.localPage({
                                configurationId,
                                restaurantId,
                                lang,
                            })}/${blockType}/photo${index}`;
                            const imageType = imageTypeMapping[blockType];

                            const processedImage = await this._preprocessStoreLocatorPictureService.execute({
                                image: { url: image.url, s3Key },
                                metadata: {
                                    imageType,
                                    restaurantName,
                                    keywords,
                                    lang,
                                },
                            });

                            return isNotNil(processedImage)
                                ? {
                                      url: processedImage.url,
                                      description: processedImage.text,
                                  }
                                : image;
                        } catch (error) {
                            logger.error(`[STORE_LOCATOR] Failed to process image for ${blockType}`, {
                                error,
                                metadata: {
                                    configurationId,
                                    restaurantId,
                                    imageUrl: image.url,
                                    blockType,
                                    index,
                                },
                            });

                            return image;
                        }
                    })
                );
            })
        );

        return medias;
    }

    private async _processCentralizationPageImages(storeLocatorCentralizationPage: IStoreLocatorMapPage): Promise<{
        map: IStoreLocatorMapPage['blocks']['map'];
        head: Pick<IStoreLocatorMapPage['blocks']['head'], 'facebookImageUrl' | 'twitterImageUrl' | 'snippetImageUrl'>;
    }> {
        const configurationId = storeLocatorCentralizationPage.configurationId.toString();

        try {
            await Promise.all([
                this._cloudStorageService.createFolderIfNotExists(`${STORE_LOCATOR_S3_LINKS.mapPage(configurationId)}/assets/`),
                this._cloudStorageService.createFolderIfNotExists(`${STORE_LOCATOR_S3_LINKS.mapPage(configurationId)}/head/`),
            ]);
        } catch (error) {
            logger.error(`[STORE_LOCATOR] Failed to create folders for centralization map page`, {
                error,
            });
        }

        const medias: {
            assets: Record<'activePin' | 'inactivePin' | 'noStoreImage', string>;
            head: Record<'facebookImageUrl' | 'twitterImageUrl' | 'snippetImageUrl', string>;
        } = {
            assets: {
                activePin: storeLocatorCentralizationPage.blocks.map.pins.activePin.url,
                inactivePin: storeLocatorCentralizationPage.blocks.map.pins.inactivePin.url,
                noStoreImage: storeLocatorCentralizationPage.blocks.map.popup.noStoreImage.url,
            },
            head: {
                facebookImageUrl: storeLocatorCentralizationPage.blocks.head.facebookImageUrl,
                twitterImageUrl: storeLocatorCentralizationPage.blocks.head.twitterImageUrl,
                snippetImageUrl: storeLocatorCentralizationPage.blocks.head.snippetImageUrl,
            },
        };

        const updatedMedias: Record<string, string>[] = await Promise.all(
            Object.entries(medias).map(async ([block, images]) => {
                const entries = await Promise.all(
                    Object.entries(images).map(async ([imageType, imageUrl]) => {
                        if (
                            !this._shouldBeProcessed({
                                imageUrl: imageUrl,
                                configurationId,
                            })
                        ) {
                            return [imageType, imageUrl];
                        }

                        const extension = imageUrl.split('.').pop();
                        const s3Key = `${STORE_LOCATOR_S3_LINKS.mapPage(configurationId)}/${block}/${imageType.replace('Url', '')}.${extension}`;

                        try {
                            // TODO: add proper process for images quality in the map using AI [@hamza]
                            const newUrl = await this._cloudStorageService.copyObject(imageUrl, s3Key);
                            return [imageType, newUrl];
                        } catch (error) {
                            logger.error(`[STORE_LOCATOR] Failed to copy picture for map page`, {
                                error,
                                metadata: {
                                    configurationId,
                                    imageUrl,
                                    imageType,
                                },
                            });

                            return [imageType, imageUrl];
                        }
                    })
                );

                return entries.reduce((acc, [key, value]) => {
                    acc[key] = value;
                    return acc;
                }, {});
            })
        );

        return {
            map: {
                pins: {
                    activePin: {
                        ...storeLocatorCentralizationPage.blocks.map.pins.activePin,
                        url: updatedMedias[0].activePin,
                    },

                    inactivePin: {
                        ...storeLocatorCentralizationPage.blocks.map.pins.inactivePin,
                        url: updatedMedias[0].inactivePin,
                    },
                },
                popup: {
                    noStoreImage: {
                        ...storeLocatorCentralizationPage.blocks.map.popup.noStoreImage,
                        url: updatedMedias[0].noStoreImage,
                    },
                },
            },
            head: {
                facebookImageUrl: updatedMedias[1].facebookImageUrl,
                twitterImageUrl: updatedMedias[1].twitterImageUrl,
                snippetImageUrl: updatedMedias[1].snippetImageUrl,
            },
        };
    }

    private _shouldBeProcessed({ imageUrl, configurationId }: { imageUrl: string; configurationId: string }): boolean {
        return (
            !imageUrl.startsWith(`${this._cloudStorageService.getBucketBaseUrl()}/${STORE_LOCATOR_S3_LINKS.base(configurationId)}`) ||
            imageUrl.includes('/pages/local/duplicated/')
        );
    }
}
