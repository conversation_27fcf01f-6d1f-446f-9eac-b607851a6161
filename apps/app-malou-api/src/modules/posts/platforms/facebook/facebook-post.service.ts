import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPost } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostType } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookPostMapper } from ':modules/posts/platforms/facebook/facebook-post-mapper';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { IPlatformPostService } from ':modules/posts/platforms/platform-post.service.interface';
import { MalouPostData } from ':modules/posts/posts.interface';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    FbComments,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class FacebookPostService implements IPlatformPostService {
    constructor(
        private readonly _facebookPostMapper: FacebookPostMapper,
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async fetchPost({ post }: { post: IPost }): Promise<MalouPostData> {
        assert(post.restaurantId, 'Missing restaurantId on post');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            post.restaurantId.toString(),
            PlatformKey.FACEBOOK
        );
        if (!platform || !platform.id) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postSocialId: post.socialId,
                    restaurantId: post.restaurantId.toString(),
                    platformKey: PlatformKey.FACEBOOK,
                },
            });
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(post.socialId, 'Missing socialId on post');

        if (post.postType === PostType.REEL) {
            return this._fetchReel({ credentialId, reelSocialId: post.socialId, platform });
        }
        return this._fetchPost({ credentialId, postSocialId: post.socialId, platform });
    }

    private async _fetchPost({
        credentialId,
        postSocialId,
        platform,
    }: {
        credentialId: string;
        postSocialId: string;
        platform: Platform;
    }): Promise<MalouPostData> {
        assert(platform.socialId, 'Missing socialId on platform');
        const fbPostResult = await this._metaGraphApiHelper.fetchFacebookPost(credentialId, postSocialId, platform.socialId);
        if (fbPostResult.isErr()) {
            logger.error('FacebookPostService _fetchPost', { error: fbPostResult.error });
            throw new MalouError(MalouErrorCode.FACEBOOK_API_EXCEPTION, {
                message: 'FacebookPostService _fetchPost',
                metadata: { error: fbPostResult.error },
            });
        }
        return this._facebookPostMapper.mapToMalouPost({ post: fbPostResult.value, platform });
    }

    private async _fetchReel({
        credentialId,
        reelSocialId,
        platform,
    }: {
        credentialId: string;
        reelSocialId: string;
        platform: Platform;
    }): Promise<MalouPostData> {
        assert(platform.socialId, 'Missing socialId on platform');
        assert(platform.id, 'Missing id on platform');
        const fbReelResult = await this._metaGraphApiHelper.fetchFacebookReel(credentialId, reelSocialId, platform.socialId);
        if (fbReelResult.isErr()) {
            logger.error('FacebookPostService _fetchReel', { error: fbReelResult.error });
            return this._fetchPost({ credentialId, postSocialId: reelSocialId, platform });
        }

        return this._facebookPostMapper.mapPublishedFbReelToMalouPost(fbReelResult.value as FacebookApiTypes.Reels.GetReelResponse, {
            restaurantId: platform.restaurantId.toString(),
            platformId: platform.id,
        });
    }

    async fetchPostFromSocialId(socialId: string, pageId: string, platforms: Platform[]): Promise<FbPostData | undefined> {
        if (platforms.length === 0) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postSocialId: socialId,
                    platformKey: PlatformKey.FACEBOOK,
                },
            });
        }
        let fbPost: FbPostData | undefined;
        let error: MetaGraphApiHelperErrorObject | undefined;

        for (const platform of platforms) {
            const credentialId = platform.credentials?.[0];
            if (!credentialId || !platform.socialId) {
                continue;
            }
            const fbPostResult = await this._metaGraphApiHelper.getFbPagePostWithCommentsAndInsights(credentialId, pageId, socialId);
            if (fbPostResult.isErr()) {
                logger.error('FacebookPostService fetchPostFromSocialId', { error: fbPostResult.error });
                error = fbPostResult.error;
                continue;
            }
            fbPost = fbPostResult.value;
            break;
        }

        if (!fbPost) {
            logger.error('FacebookPostService fetchPostFromSocialId', { error });
        }

        return fbPost;
    }

    async fetchPagePostsWithComments({ credentialId, pageId }: { credentialId: string; pageId: string }): Promise<FbPostData[]> {
        const fbPostsResult = await this._metaGraphApiHelper.getFbPagePostsWithComments(credentialId, pageId);
        if (fbPostsResult.isErr()) {
            logger.error('FacebookPostService fetchPagePostsWithComments', { error: fbPostsResult.error });
            throw new MalouError(MalouErrorCode.FACEBOOK_API_EXCEPTION, {
                message: 'FacebookPostService fetchPagePostsWithComments',
                metadata: { error: fbPostsResult.error },
            });
        }
        return fbPostsResult.value;
    }

    async fetchCommentsFromPhoto({
        platform,
        photoId,
        pageId,
    }: {
        platform: Platform;
        photoId: string;
        pageId: string;
    }): Promise<{ id: string; comments?: FbComments }> {
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const fbPhotoResult = await this._metaGraphApiHelper.getFbPhotoWithComments({ credentialId, photoId, pageId });
        if (fbPhotoResult.isErr()) {
            logger.error('FacebookPostService fetchCommentsFromPhoto', { error: fbPhotoResult.error });
            throw new MalouError(MalouErrorCode.FACEBOOK_API_EXCEPTION, {
                message: 'FacebookPostService fetchCommentsFromPhoto',
                metadata: { error: fbPhotoResult.error },
            });
        }
        return fbPhotoResult.value;
    }
}
