import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost, IPost, IPostWithAttachments, IPostWithAttachmentsAndThumbnail } from '@malou-io/package-models';
import { isBetween, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersApi } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { UpsertPlatformPostInsightsService } from ':modules/post-insights/v2/services/upsert-platform-post-insights/upsert-platform-post-insights.service';
import { PostInsight } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { TiktokPostMapper } from ':modules/posts/platforms/tiktok/tiktok-post-mapper';
import { TiktokPostInitPublishUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-post-init-publish.use-case';
import { MalouPostData, PlatformPostUseCases } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

@singleton()
export class TiktokPostsUseCases implements PlatformPostUseCases {
    constructor(
        private _tiktokPostInitPublishUseCase: TiktokPostInitPublishUseCase,
        private _platformsRepository: PlatformsRepository,
        private _callTiktokApiService: CallTiktokApiService,
        private _tiktokProvider: TiktokProvider,
        private _postsRepository: PostsRepository,
        private readonly _upsertPlatformPostInsightsService: UpsertPlatformPostInsightsService
    ) {}

    async updatePost(post: IPost): Promise<IPost> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_UPDATE_POST] Cant update Tiktok post when already published');
        return post;
    }

    async deletePost(post: IPost): Promise<IPost> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_DELETE_POST] Cant delete Tiktok post when already published');
        return post;
    }

    async fetchPost({ post }: { post: IPost }): Promise<MalouPostData | null> {
        assert(post.socialId, 'Missing socialId on post');

        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            post.restaurantId.toString(),
            PlatformKey.TIKTOK
        );
        if (!platform || !platform.id) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postSocialId: post.socialId,
                    restaurantId: post.restaurantId.toString(),
                    platformKey: PlatformKey.TIKTOK,
                },
            });
        }

        const { data } = await this._callTiktokApiService.execute({
            restaurantId: post.restaurantId.toString(),
            method: this._tiktokProvider.queryVideos,
            args: {
                ids: [post.socialId],
            },
        });

        const fetchedPost = data.videos[0];

        return new TiktokPostMapper().mapToMalouPost({ post: fetchedPost, platform });
    }

    async completePublish(_post: IPopulatedPost): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_COMPLETE_PUBLISH] TikTok does not support completing publish');
    }

    async getCompetitorsPosts(): Promise<MalouPostData[]> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_GET_COMPETITORS_POSTS] TikTok does not support fetching competitors posts');
        return [];
    }

    async searchAccounts(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_SEARCH_ACCOUNTS] TikTok does not support searching accounts');
    }

    async oembed(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_OEMBED] TikTok does not support oEmbed');
    }

    async synchronize({ platform }: { platform: Platform }): Promise<MalouPostData[]> {
        assert(platform.socialId, '[TiktokPostsUseCases] Missing socialId on platform');
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found in tiktok synchronize use case',
            });
        }
        const postMapper = new TiktokPostMapper();
        const {
            data: { videos },
        } = await this._callTiktokApiService.execute({
            credentialId: credentialId.toString(),
            method: this._tiktokProvider.getAllVideos,
            args: {},
        });

        if (!videos.length) {
            return [];
        }

        this._upsertPlatformPostInsightsService
            .upsertTiktokPostInsights({ posts: videos, platformSocialId: platform.socialId })
            .catch((error) => {
                logger.error('[InstagramPostsUseCases][synchronize] Error while upserting post insights', {
                    error,
                    platformSocialId: platform.socialId,
                });
            });
        const mappedPosts = videos.map((p) => postMapper.mapToMalouPost({ post: p, platform }));
        return mappedPosts;
    }

    async publish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<void> {
        await this._tiktokPostInitPublishUseCase.execute({ post: post as IPopulatedPost });
    }

    async fetchPostsWithInsights(
        restaurantId: string,
        tiktokFilters: PlatformInsightFiltersApi,
        _shouldRaiseError?: boolean
    ): Promise<PostInsight[]> {
        const { startDate, endDate } = tiktokFilters;
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TIKTOK);
        if (!platform || !platform.socialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, key: PlatformKey.TIKTOK, socialId: platform?.socialId },
            });
        }

        const { data } = await this._callTiktokApiService.execute({
            restaurantId: restaurantId.toString(),
            method: this._tiktokProvider.getAllVideos,
            args: {},
        });

        return data.videos
            .filter((post) => !!post)
            .map((post) => new TiktokPostMapper().mapToInsightPost(post))
            .filter((post) => isBetween(post.createdAt, startDate, endDate));
    }
}
