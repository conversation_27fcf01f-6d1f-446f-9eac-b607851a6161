import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { errorReplacer, RetryError, retryResult } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class UploadVideoStoryOnFacebookService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        platform: Platform,
        credentialId: string,
        videoUrl: string
    ): Promise<Result<{ videoId: string }, MetaGraphApiHelperErrorObject>> {
        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');

        const initUploadRes = await this._metaGraphApiHelper.initFbVideoStoryUploadSession({
            credentialId,
            fbPageId: pageId,
        });
        if (initUploadRes.isErr()) {
            return err(initUploadRes.error);
        }

        await this._metaGraphApiHelper.uploadFbVideoStory({
            credentialId,
            fbPageId: pageId,
            videoUrl,
            fbUploadUrl: initUploadRes.value.fbUploadUrl,
        });

        const getVideoRes = await retryResult(
            () => this._metaGraphApiHelper.getFbVideo(credentialId, pageId, initUploadRes.value.videoId),
            {
                attempts: 10,
                backoffStrategy: 'exponential',
                isSuccess: (res) => res.status.video_status === 'upload_complete',
                shouldRetrySuccess: (res) => res.status.video_status !== 'error',
            }
        );
        if (getVideoRes.isErr()) {
            if (getVideoRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_FB_VIDEO,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(getVideoRes.error, errorReplacer),
                });
            }
            assert(getVideoRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(getVideoRes.error.lastResult.isErr());
            return err(getVideoRes.error.lastResult.error);
        }

        return ok({ videoId: getVideoRes.value.id });
    }
}
