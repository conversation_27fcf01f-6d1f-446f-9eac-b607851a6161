import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { removeAndAddHashtagsToText, RetryError, retryResult } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import { FbVideoErrorName, MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { MetaGraphApiHelperErrorObject } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

// TODO posts-v2 write a test for this
@singleton()
export class PublishReelOnFacebookService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    /**
     * https://developers.facebook.com/docs/video-api/guides/reels-publishing/
     */
    async execute(params: {
        post: IPopulatedPost;
        platform: Platform;
        credentialId: string;
        videoUrl: string;
    }): Promise<Result<{ pagePostId: string }, FbVideoErrorName | MetaGraphApiHelperErrorObject>> {
        logger.info('[PublishReelOnFacebookService] begin', params);

        const result = await retryResult(() => this._tryToPublish(params), {
            isSuccess: () => true,
            shouldRetryError: (e) => e === 'video_creation_failed_please_try_again',
        });
        if (result.isErr()) {
            logger.info('[PublishReelOnFacebookService] retryResult failed', result.error);
            if (result.error.error === RetryError.STILL_ERROR_AFTER_RETRIES) {
                logger.error('[PublishReelOnFacebookService] max attempts reached');
                return result.error.lastResult;
            }
            assert(result.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_ERROR);
            return err(result.error.originalError);
        }
        return ok(result.value);
    }

    private async _tryToPublish(params: {
        post: IPopulatedPost;
        platform: Platform;
        credentialId: string;
        videoUrl: string;
    }): Promise<Result<{ pagePostId: string }, FbVideoErrorName | MetaGraphApiHelperErrorObject>> {
        logger.info('[PublishReelOnFacebookService] _tryToPublish', params);

        const { credentialId } = params;
        const fbPageId = params.platform.socialId;
        assert(fbPageId, 'Missing socialId on platform');

        let videoId: string;
        let fbUploadUrl: string;
        {
            const res = await this._metaGraphApiHelper.fbVideoReelUploadInit({ fbPageId, credentialId });
            if (res.isErr()) {
                return err(res.error);
            }
            videoId = res.value.videoId;
            fbUploadUrl = res.value.fbUploadUrl;
        }

        {
            const res = await this._metaGraphApiHelper.fbVideoReelUploadSetUrl({
                fbPageId,
                credentialId,
                fbUploadUrl,
                malouPublicVideoUrl: params.videoUrl,
            });
            if (res.isErr()) {
                return err(res.error);
            }
        }

        {
            const res = await this._metaGraphApiHelper.fbVideoReelUploadWait({ fbPageId, credentialId, videoId });
            if (res.isErr()) {
                return err(res.error);
            }
        }

        {
            const selectedHashtagTexts = params.post.hashtags?.selected?.map((hashtag) => hashtag.text) ?? [];
            const textWithHashtags = removeAndAddHashtagsToText(params.post.text ?? '', selectedHashtagTexts);

            const res = await this._metaGraphApiHelper.fbVideoReelPublish({
                fbPageId,
                credentialId,
                videoId,
                description: textWithHashtags ?? '',
                locationId: params.post.location?.id,
            });
            if (res.isErr()) {
                return err(res.error);
            }
        }

        let postId: string;
        {
            const res = await this._metaGraphApiHelper.fbVideoReelPublishWait({ fbPageId, credentialId, videoId });
            if (res.isErr()) {
                return err(res.error);
            }
            postId = res.value.postId;
        }

        // In v1 we directly use the videoId instead of post_id, that works, but it's not referenced in the documentation
        const pagePostId = `${fbPageId}_${postId}`;

        logger.info('[PublishReelOnFacebookService] reel published', { videoId, postId, pagePostId });

        return ok({ pagePostId });
    }
}
