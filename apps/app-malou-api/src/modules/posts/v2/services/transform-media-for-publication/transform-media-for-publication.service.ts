import assert from 'node:assert/strict';
import { randomUUID } from 'node:crypto';
import sharp from 'sharp';
import { inject, singleton } from 'tsyringe';

import { IMedia, IMediaStoredObject } from '@malou-io/package-models';
import { MediaType, MimeType, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';
import { AwsMediaConvertService } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert.service';
import {
    GetTransformInfoService,
    TransformInfo,
} from ':modules/posts/v2/services/transform-media-for-publication/get-transform-info/get-transform-info.service';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import {
    AwsS3DistantStorageService,
    ExpireIn7DaysAwsS3Tag,
} from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

const FOLDER_NAME = 'media-folder-for-publication';

export interface MediaStoredObjectWithType {
    type: 'photo' | 'video';
    storedObject: IMediaStoredObject;
    dimensions: {
        width: number;
        height: number;
    };
}

@singleton()
export class TransformMediaForPublicationService {
    constructor(
        private readonly _getTransformInfoService: GetTransformInfoService,
        @inject(AwsS3DistantStorageService)
        private readonly _distantStorageService: DistantStorageService,
        private readonly _awsMediaConvertService: AwsMediaConvertService,
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}

    async formatMediasForPost(
        medias: IMedia[],
        platformKey: PlatformKey,
        forcedAreaAspectRatio?: number
    ): Promise<MediaStoredObjectWithType[]> {
        logger.info('[TransformMediaForPublicationService.formatMediasForPost] Start', { mediaIds: medias.map((m) => m._id.toString()) });
        const storedObjects: MediaStoredObjectWithType[] = [];
        for (const media of medias) {
            const transformInfo = await this._getTransformInfoService.getForPost(media, platformKey, forcedAreaAspectRatio);
            const formatRes = await this._formatMedia(media.type, transformInfo);
            const type = media.type === MediaType.PHOTO ? 'photo' : media.type === MediaType.VIDEO ? 'video' : undefined;
            assert(!!type);
            storedObjects.push({ type, storedObject: formatRes.storedObject, dimensions: formatRes.dimensions });
        }
        return storedObjects;
    }

    async formatMediaForReel(media: IMedia): Promise<MediaStoredObjectWithType> {
        logger.info('[TransformMediaForPublicationService.formatMediaForReel] Start', { mediaId: media._id.toString() });
        const transformInfo = await this._getTransformInfoService.getForReel(media);
        const formatRes = await this._formatMedia(media.type, transformInfo);
        return { type: 'video', storedObject: formatRes.storedObject, dimensions: formatRes.dimensions };
    }

    async formatMediaForStory(media: IMedia): Promise<MediaStoredObjectWithType> {
        logger.info('[TransformMediaForPublicationService.formatMediaForStory] Start', { mediaId: media._id.toString() });
        const transformInfo = await this._getTransformInfoService.getForStory(media);
        const formatRes = await this._formatMedia(media.type, transformInfo);
        const type = media.type === MediaType.PHOTO ? 'photo' : media.type === MediaType.VIDEO ? 'video' : undefined;
        assert(!!type);
        return { type, storedObject: formatRes.storedObject, dimensions: formatRes.dimensions };
    }

    private async _formatMedia(
        mediaType: MediaType,
        transformInfo: TransformInfo
    ): Promise<{ storedObject: IMediaStoredObject; dimensions: { width: number; height: number } }> {
        if (mediaType === MediaType.PHOTO) {
            return await this._formatImage(transformInfo);
        }
        if (mediaType === MediaType.VIDEO) {
            return await this._formatVideo(transformInfo);
        }
        assert.fail('media type not supported');
    }

    private async _formatImage(
        transformInfo: TransformInfo
    ): Promise<{ storedObject: IMediaStoredObject; dimensions: { width: number; height: number } }> {
        logger.info('[TransformMediaForPublicationService._formatImage] Start', { transformInfo });

        const inputBuffer = await this._distantStorageService.getBuffer(transformInfo.storedObject.key);

        /**
         * The first rotate() is necessary to correctly apply any Exif-based rotation metadata.
         * In one of the v1 upload process (handled on the frontend with Uppy), images may retain their Exif rotation data.
         * Ideally, we wouldn't upload images with Exif data, but since this is legacy v1 code that will soon be deprecated,
         * and stripping Exif data in Uppy is not straightforward, we won't fix it.
         * Plus, media with Exif data already exist in base, so fixing the Uppy part will not solve all the problem.
         *
         * In v2 (and all future implementations), we always strip out Exif data upon upload, but only after manually applying the necessary rotation.
         * This ensures that images are properly oriented without relying on Exif metadata.
         *
         * This initial rotate() call specifically handles cases where the image has Exif rotation metadata,
         * but the media dimensions attributes are already correct (i.e., the dimensions.width and dimensions.height are computed based on the Exif rotation).
         * By applying rotate() here, we ensure a consistent orientation across all uploaded images.
         *
         * The initial rotate() will have no effect for images without Exif data.
         */
        const inputWithExifRotationBuffer = await sharp(inputBuffer).rotate().toBuffer();
        let sharpInstance = sharp(inputWithExifRotationBuffer);
        if (transformInfo.cropArea) {
            // sharp needs int values
            const transformAreaRounded = {
                left: Math.floor(transformInfo.cropArea.left),
                top: Math.floor(transformInfo.cropArea.top),
                width: Math.floor(transformInfo.cropArea.width),
                height: Math.floor(transformInfo.cropArea.height),
            };
            sharpInstance = sharpInstance.extract(transformAreaRounded);
        }
        sharpInstance = sharpInstance.resize(
            Math.round(transformInfo.outputDimensions.width),
            transformInfo.outputDimensions.height ? Math.round(transformInfo.outputDimensions.height) : undefined
        );

        const sharpRes = await sharpInstance.jpeg({ progressive: true }).toBuffer({ resolveWithObject: true });

        const outputBuffer = sharpRes.data;

        const key = this._getRandomKey();
        await this._distantStorageService.saveFromBuffer(key, outputBuffer, { contentType: MimeType.IMAGE_JPEG });
        const publicUrl = await this._distantStorageService.getPublicAccessibleUrl(key);
        return {
            storedObject: {
                provider: 'S3',
                key,
                publicUrl,
            },
            dimensions: { width: sharpRes.info.width, height: sharpRes.info.height },
        };
    }

    private async _formatVideo(
        transformInfo: TransformInfo
    ): Promise<{ storedObject: IMediaStoredObject; dimensions: { width: number; height: number } }> {
        logger.info('[TransformMediaForPublicationService._formatVideo] Start', { transformInfo });

        const result = await this._awsMediaConvertService.transformVideoForPublication({
            s3NormalizedKey: transformInfo.storedObject.key,
            s3OutputKeyPrefix: this._getRandomKey(),
            outputDimensions: transformInfo.outputDimensions,
            cropArea: transformInfo.cropArea,
        });
        assert(result.success);
        const outputKey = result.value.s3OutputKey;
        await this._distantStorageService.putTag(outputKey, { [ExpireIn7DaysAwsS3Tag.key]: ExpireIn7DaysAwsS3Tag.value });
        const width = result.value.width;
        const height = result.value.height;
        return {
            storedObject: {
                provider: 'S3',
                key: outputKey,
                publicUrl: await this._distantStorageService.getPublicAccessibleUrl(outputKey),
            },
            dimensions: {
                width,
                height,
            },
        };
    }

    private _getRandomKey(): string {
        return `${FOLDER_NAME}/${randomUUID()}`;
    }
}
