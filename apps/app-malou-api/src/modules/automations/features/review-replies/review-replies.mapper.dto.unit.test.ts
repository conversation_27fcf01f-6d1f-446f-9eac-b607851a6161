import * as lodash from 'lodash';
import { container } from 'tsyringe';

import { ReviewReplyAutomationDto } from '@malou-io/package-dto';
import { IReviewReplyAutomation, toDbId } from '@malou-io/package-models';
import {
    AutomationFeature,
    getPlatformKeysAutomatableWithoutValidation,
    PlatformKey,
    ReviewReplyAutomationComment,
    ReviewReplyAutomationMethod,
    ReviewReplyAutomationRatingCategory,
} from '@malou-io/package-utils';

import { INewReviewReplyAutomation, ReviewReplyAutomationsDtoMapper } from './review-replies.mapper.dto';

const REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS = getPlatformKeysAutomatableWithoutValidation(ReviewReplyAutomationComment.WITH_COMMENT);

describe('ReviewReplyAutomationsDtoMapper', () => {
    const reviewReplyAutomationsDtoMapper = container.resolve(ReviewReplyAutomationsDtoMapper);

    describe('toDtoList', () => {
        it('should return default automations if no data is given', () => {
            const dtos = reviewReplyAutomationsDtoMapper.toDtoList([], 'restaurantId', REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);
            expect(dtos).toStrictEqual(DEFAULT_REVIEW_REPLY_AUTOMATIONS('restaurantId'));
        });

        it('should throw an error if restaurantId in model is different from restaurantId input', () => {
            const model: IReviewReplyAutomation = {
                _id: toDbId('6777a9ff02c9aa5d57abf111'),
                restaurantId: toDbId('6999a9ff02c9aa5d57abf111'),
                active: true,
                feature: AutomationFeature.REPLY_TO_REVIEW,
                ratingCategory: 3,
                platformKey: PlatformKey.GMB,
                withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                replyMethod: ReviewReplyAutomationMethod.AI,
                shouldValidateAiBeforeSend: false,
                templateIds: [],
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            const call = () =>
                reviewReplyAutomationsDtoMapper.toDtoList([model], 'anotherRestaurantId', REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);

            expect(call).toThrowError();
        });

        it('should return the correct DTO list', () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';

            const automations: IReviewReplyAutomation[] = [
                {
                    _id: toDbId('6777abcabcabca1111111111'),
                    restaurantId: toDbId(restaurantId),
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: 3,
                    platformKey: PlatformKey.GMB,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    shouldValidateAiBeforeSend: false,
                    templateIds: [],
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    _id: toDbId('6777abcabcabca3333333333'),
                    restaurantId: toDbId(restaurantId),
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: 4,
                    platformKey: PlatformKey.UBEREATS,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
                    shouldValidateAiBeforeSend: true,
                    templateIds: [toDbId('6999abcdefed111111111111'), toDbId('6999abcdefed222222222222')],
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                {
                    _id: toDbId('6777abcabcabca4444444444'),
                    restaurantId: toDbId(restaurantId),
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: 2,
                    platformKey: PlatformKey.ZENCHEF,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
                    shouldValidateAiBeforeSend: true,
                    templateIds: [toDbId('6999abcdefed222222222222')],
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            ];

            const expectedDtos: ReviewReplyAutomationDto[] = [
                {
                    restaurantId,
                    active: false,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [],
                },
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [
                        {
                            template: '6999abcdefed222222222222',
                            platformKeys: [PlatformKey.ZENCHEF],
                        },
                    ],
                },
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [PlatformKey.GMB],
                    },
                    templateConfigs: [],
                },
                {
                    restaurantId,
                    active: false,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [],
                },
                {
                    restaurantId,
                    active: false,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [],
                },
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [
                        {
                            template: '6999abcdefed111111111111',
                            platformKeys: [PlatformKey.UBEREATS],
                        },
                        {
                            template: '6999abcdefed222222222222',
                            platformKeys: [PlatformKey.UBEREATS],
                        },
                    ],
                },
                {
                    restaurantId,
                    active: false,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [],
                },
                {
                    restaurantId,
                    active: false,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
                    withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [],
                    },
                    templateConfigs: [],
                },
            ];

            const dtos = reviewReplyAutomationsDtoMapper.toDtoList(automations, restaurantId, REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);

            expect(dtos).toStrictEqual(expectedDtos);
        });
    });

    describe('toList', () => {
        it('should return an empty array if no DTO is given', () => {
            const automations = reviewReplyAutomationsDtoMapper.toList([], 'restaurantId', REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);

            expect(automations).toStrictEqual([]);
        });

        it('should throw an error if restaurantId in DTO is different from restaurantId input', () => {
            const dto: ReviewReplyAutomationDto = {
                restaurantId: '6999a9ff02c9aa5d57abf111',
                active: true,
                feature: AutomationFeature.REPLY_TO_REVIEW,
                ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
                withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
                replyMethod: ReviewReplyAutomationMethod.AI,
                aiConfig: {
                    sendAutomaticallyToThesePlatformKeys: [],
                },
                templateConfigs: [],
            };

            const call = () => reviewReplyAutomationsDtoMapper.toList([dto], 'anotherRestaurantId', REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);

            expect(call).toThrowError();
        });

        it('should return a list of automations for all platformKeys that accept a reply', () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';
            const dtos: ReviewReplyAutomationDto[] = buildDtosExample(restaurantId);

            const automations = reviewReplyAutomationsDtoMapper.toList(dtos, restaurantId, REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);

            const sortModels = (m: INewReviewReplyAutomation[]): INewReviewReplyAutomation[] =>
                lodash.chain(m).sortBy('platformKey').sortBy('ratingCategory').sortBy('withComment').value();
            const expectedModels = buildModelsExample(restaurantId);
            expect(sortModels(automations)).toEqual(sortModels(expectedModels));
        });

        it("should not create automations if platform doesn't accept replies", () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';
            const dtos: ReviewReplyAutomationDto[] = [
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [PlatformKey.RESY],
                    },
                    templateConfigs: [],
                },
            ];

            const automations = reviewReplyAutomationsDtoMapper.toList(dtos, restaurantId, [PlatformKey.RESY]);

            const resyAutomations = automations.filter((m) => m.platformKey === PlatformKey.RESY);
            expect(resyAutomations.length).toBe(0);
        });

        it("should only create automations with text if platform doesn't accept replies only if there is a comment", () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';
            const dtos: ReviewReplyAutomationDto[] = [
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [PlatformKey.RESY],
                    },
                    templateConfigs: [],
                },
            ];

            const automations = reviewReplyAutomationsDtoMapper.toList(dtos, restaurantId, [PlatformKey.DELIVEROO]);

            const deliverooModelsWithComments = automations.filter(
                (m) => m.platformKey === PlatformKey.DELIVEROO && m.withComment === ReviewReplyAutomationComment.WITH_COMMENT
            );
            const deliverooModelsWithoutComments = automations.filter(
                (m) => m.platformKey === PlatformKey.DELIVEROO && m.withComment === ReviewReplyAutomationComment.WITHOUT_COMMENT
            );
            expect(deliverooModelsWithComments.length).toBe(5);
            // Deliveroo does not accept replies without comments so we should not create any automation for this case
            expect(deliverooModelsWithoutComments.length).toBe(0);
        });

        it('should always validate AI before sending for private automations even if it was in the sendAutomaticallyToThesePlatformKeys of the dto', () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';
            const dtos: ReviewReplyAutomationDto[] = [
                {
                    restaurantId,
                    active: true,
                    feature: AutomationFeature.REPLY_TO_REVIEW,
                    ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
                    withComment: ReviewReplyAutomationComment.WITH_COMMENT,
                    replyMethod: ReviewReplyAutomationMethod.AI,
                    aiConfig: {
                        sendAutomaticallyToThesePlatformKeys: [PlatformKey.PRIVATE],
                    },
                    templateConfigs: [],
                },
            ];

            const automations = reviewReplyAutomationsDtoMapper.toList(dtos, restaurantId, [PlatformKey.PRIVATE]);

            const privateAutomations = automations.filter((m) => m.platformKey === PlatformKey.PRIVATE);
            expect(privateAutomations.length).toBe(10);
            const activePrivateAutomations = privateAutomations.filter((automation) => automation.active);
            expect(activePrivateAutomations.length).toBe(2);
            const activeAutomationsWithValidation = activePrivateAutomations.filter((automation) => automation.shouldValidateAiBeforeSend);
            // All active automations should always have the validation activated even if it was in the sendAutomaticallyToThesePlatformKeys
            expect(activeAutomationsWithValidation.length).toBe(2);
        });
    });

    describe('toList & toDtoList', () => {
        it('should not change the original object when called one after the other', () => {
            const restaurantId = '6999a9ff02c9aa5d57abf111';

            const originalModels = buildModelsExample(restaurantId);
            const originalDtos = reviewReplyAutomationsDtoMapper.toDtoList(
                originalModels as IReviewReplyAutomation[],
                restaurantId,
                REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS
            );
            const automations = reviewReplyAutomationsDtoMapper.toList(originalDtos, restaurantId, REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS);
            const dtos = reviewReplyAutomationsDtoMapper.toDtoList(
                automations as IReviewReplyAutomation[],
                restaurantId,
                REVIEW_REPLY_AUTOMATION_PLATFORM_KEYS
            );

            expect(_sortUpdateModelsTemplateIds(automations)).toIncludeAllMembers(_sortUpdateModelsTemplateIds(originalModels));
            expect(_sortDtoPlatformKeys(dtos)).toEqual(_sortDtoPlatformKeys(originalDtos));
        });
    });
});

const _sortDtoPlatformKeys = (dtos: ReviewReplyAutomationDto[]): ReviewReplyAutomationDto[] => {
    return dtos.map((d) => ({
        ...d,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: d.aiConfig.sendAutomaticallyToThesePlatformKeys.sort(),
        },
        templateConfigs: d.templateConfigs.map((tc) => ({ ...tc, platformKeys: tc.platformKeys.sort() })),
    }));
};

const _sortUpdateModelsTemplateIds = (models: INewReviewReplyAutomation[]): INewReviewReplyAutomation[] =>
    models.map((m) => ({ ...m, templateIds: m.templateIds.sort() }));

const DEFAULT_REVIEW_REPLY_AUTOMATIONS = (restaurantId: string) => [
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
];

const buildModelsExample = (restaurantId: string): INewReviewReplyAutomation[] => [
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: false,
        templateIds: [toDbId('123456789123456789123456')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.DELIVEROO,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: false,
        templateIds: [toDbId('123456789123456789123456')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.LAFOURCHETTE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 1,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: false,
        templateIds: [toDbId('123456789123456789123456')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.DELIVEROO,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: false,
        templateIds: [toDbId('123456789123456789123456')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.LAFOURCHETTE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('987654321987654321987654')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('123456789123456789123456')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.DELIVEROO,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('123456789123456789123456'), toDbId('987654321987654321987654')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('123456789123456789123456'), toDbId('456789123456789123456792')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.LAFOURCHETTE,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.DELIVEROO,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.LAFOURCHETTE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: false,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.DELIVEROO,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.LAFOURCHETTE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.AI,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.GMB,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.UBEREATS,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('456789123456789123456789')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.ZENCHEF,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [toDbId('456789123456789123456789')],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.FACEBOOK,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.TRIPADVISOR,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.OPENTABLE,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.YELP,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.SEVENROOMS,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
    {
        restaurantId: toDbId(restaurantId),
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: 5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        platformKey: PlatformKey.PRIVATE,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        shouldValidateAiBeforeSend: true,
        templateIds: [],
    },
];

const buildDtosExample = (restaurantId: string) => [
    {
        restaurantId,
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [PlatformKey.UBEREATS, PlatformKey.DELIVEROO],
        },
        templateConfigs: [
            {
                template: '123456789123456789123456',
                platformKeys: [PlatformKey.UBEREATS, PlatformKey.DELIVEROO],
            },
        ],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_1_2,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [
            {
                template: '123456789123456789123456',
                platformKeys: [PlatformKey.UBEREATS, PlatformKey.DELIVEROO, PlatformKey.ZENCHEF],
            },
            {
                template: '987654321987654321987654',
                platformKeys: [PlatformKey.GMB, PlatformKey.DELIVEROO],
            },
            {
                template: '456789123456789123456792',
                platformKeys: [PlatformKey.ZENCHEF],
            },
        ],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_3,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_4,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [PlatformKey.ZENCHEF, PlatformKey.DELIVEROO],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: false,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
        withComment: ReviewReplyAutomationComment.WITH_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.AI,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [],
    },
    {
        restaurantId,
        active: true,
        feature: AutomationFeature.REPLY_TO_REVIEW,
        ratingCategory: ReviewReplyAutomationRatingCategory.REVIEW_5,
        withComment: ReviewReplyAutomationComment.WITHOUT_COMMENT,
        replyMethod: ReviewReplyAutomationMethod.TEMPLATES,
        aiConfig: {
            sendAutomaticallyToThesePlatformKeys: [],
        },
        templateConfigs: [
            {
                template: '456789123456789123456789',
                platformKeys: [PlatformKey.UBEREATS, PlatformKey.ZENCHEF],
            },
        ],
    },
];
