import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CommentsRepository } from ':modules/comments/comments.repository';
import {
    mapCommentDataToMalou as igMapCommentDataToMalou,
    mapReplyDataToMalou as igMapReplyDataToMalou,
} from ':modules/comments/platforms/instagram/instagram.use-cases';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import * as fbCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { CreateCommentNotificationProducer } from ':modules/notifications/queues/create-comment-notification/create-comment-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

const credentialsRepository = container.resolve(CredentialsRepository);
const postsRepository = container.resolve(PostsRepository);
const restaurantsRepository = container.resolve(RestaurantsRepository);
const commentsRepository = container.resolve(CommentsRepository);
const platformsRepository = container.resolve(PlatformsRepository);
const instagramPostsUseCases = container.resolve(InstagramPostsUseCases);
const createCommentNotificationProducer = container.resolve(CreateCommentNotificationProducer);

export const igHandlePostAndComments = async (webhookValue, time, pageId) => {
    if (webhookValue.parent_id) {
        const platforms = await platformsRepository.find({
            filter: { key: PlatformKey.INSTAGRAM, socialId: pageId },
            options: { lean: true },
        });
        const comment = {
            ...webhookValue,
            message: webhookValue.text,
            created_time: time * 1000,
        };

        const promises = platforms.map(async (platform) => {
            const mappedReply = igMapReplyDataToMalou({
                ...comment,
            });
            const existingCommentInDb = await commentsRepository.findOne({
                filter: {
                    socialId: webhookValue.parent_id,
                    platformId: platform._id,
                },
                options: { lean: true },
            });
            if (!existingCommentInDb) {
                await commentsRepository.findOneAndUpdate({
                    filter: {
                        socialId: webhookValue.parent_id,
                        platformId: platform._id,
                    },
                    update: { $push: { replies: mappedReply } },
                });
                return;
            }

            const existingReply = existingCommentInDb?.replies.find((reply) => reply.socialId === mappedReply.socialId);

            const updatedReplies = existingReply
                ? existingCommentInDb?.replies.map((reply) =>
                      reply.socialId === mappedReply.socialId
                          ? {
                                ...reply,
                                ...mappedReply,
                            }
                          : reply
                  )
                : existingCommentInDb?.replies.concat([{ _id: newDbId(), ...mappedReply }]);

            await commentsRepository.findOneAndUpdate({
                filter: {
                    socialId: webhookValue.parent_id,
                    platformId: platform._id,
                },
                update: {
                    replies: updatedReplies,
                },
            });
        });
        await Promise.all(promises);
        const restaurantId = platforms[0]?.restaurantId;
        if (restaurantId) {
            await restaurantsRepository.findOneAndUpdate({ filter: { _id: restaurantId }, update: { commentsLastUpdate: new Date() } });
        }
        return 'ok';
    }
    const validCredentials = await credentialsRepository.find({
        filter: {
            key: PlatformKey.FACEBOOK,
            pageAccess: { $exists: true },
            'pageAccess.igPageId': pageId,
        },
        options: { lean: true, sort: { lastSeenWorking: -1 } },
    });
    let validCredential;
    let count = 0;
    let post;
    let comment;
    const MAX_COUNT = 100;
    logger.info('[WHILE_TRACKER] - ig handle posts and comments started');
    while (!post && count < validCredentials.length && count < MAX_COUNT) {
        try {
            validCredential = validCredentials[count];
            const { media: mediaData, ...commentData } = await fbCredentialsUseCases.genericFbApiCall(
                // eslint-disable-next-line max-len
                `${webhookValue.id}?fields=media{caption,media_url,media_type,permalink,timestamp,children{media_url,media_type}},username,timestamp,like_count,text,replies{username,user,text,like_count,timestamp}`,
                'get',
                { access_token: validCredential.userAccessToken }
            );
            post = mediaData;
            comment = commentData;
            count += 1;
        } catch (error) {
            logger.error('[WHILE_TRACKER_ERROR][POSTS][INSTAGRAM]', { webhookId: webhookValue.id, error });
            count += 1;
        }
    }
    logger.info('[WHILE_TRACKER] - ig handle posts and comments ended');
    if (!post) {
        logger.warn('[WEBHOOK_HANDLE_POST_ERROR][INSTAGRAM] - did not work', { webhookValue, pageId });
        return 'did not work';
    }
    await credentialsRepository.findOneAndUpdate({ filter: { _id: validCredential._id }, update: { lastSeenWorking: new Date() } });
    const platforms = await platformsRepository.getPlatformsBySocialIdAndPlatformKey(pageId, PlatformKey.INSTAGRAM);
    const promises = platforms.map(async (platform) => {
        const mappedPost = instagramPostsUseCases.mapPostDataToMalou(post, platform);
        const mappedComment = igMapCommentDataToMalou(
            {
                ...comment,
                postId: post.id,
            },
            platform
        );

        if (mappedComment) {
            const upsertedComment = await commentsRepository.upsert({
                filter: {
                    socialId: webhookValue.id,
                    platformId: platform._id,
                },
                update: mappedComment,
                options: { lean: true, new: true },
            });

            if (upsertedComment) {
                await createCommentNotificationProducer.execute({ commentId: upsertedComment._id.toString() });
            }
        } else {
            logger.warn('[WEBHOOK_HANDLE_POST_ERROR][INSTAGRAM] - upsert comment did not work', { webhookValue, pageId });
        }

        await postsRepository.upsertPostByRestaurantIdAndSocialId(mappedPost);
    });

    await Promise.all(promises);

    const restaurantId = platforms[0]?.restaurantId;
    if (restaurantId)
        await restaurantsRepository.findOneAndUpdate({ filter: { _id: restaurantId }, update: { commentsLastUpdate: new Date() } });
    return 'ok';
};
