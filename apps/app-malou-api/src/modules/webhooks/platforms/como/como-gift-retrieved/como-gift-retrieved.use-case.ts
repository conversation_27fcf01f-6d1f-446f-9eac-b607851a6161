import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { ProviderClientSource } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import ClientsRepository from ':modules/clients/clients.repository';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import { GiftDrawsUseCases } from ':modules/gift-draws/gift-draws.use-cases';
import { comoGiftRetrievedEventValidator } from ':modules/webhooks/platforms/como/como-gift-retrieved/como-gift-retrieved.interface';

@singleton()
export class ComoGiftRetrievedUseCase {
    constructor(
        private readonly _giftDrawsUseCases: GiftDrawsUseCases,
        private readonly _clientsRepository: ClientsRepository,
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _providerClientsRepository: ProviderClientsRepository
    ) {}

    // https://docs.comosense.com/d/docs/advanced-apis/export-event-webhooks/event-webhook-payloads
    async execute(body: any): Promise<void> {
        const parsedBody = await comoGiftRetrievedEventValidator.safeParseAsync(body);
        if (!parsedBody.success) {
            logger.error('[WEBHOOKS] [COMO] - Invalid body', { body });
            return;
        }
        const giftName = parsedBody.data.giftName;
        const comoMemberId = parsedBody.data.comoMemberId;

        const giftId = this._mapComoGiftNameToGiftId(giftName);
        if (!giftId) {
            logger.error('[WEBHOOKS] [COMO] - Gift name not found', { giftName });
            return;
        }

        const providerClients = await this._providerClientsRepository.getProviderClientsByProviderClientId(comoMemberId);
        const providerClient = providerClients.find((pc) => pc.source === ProviderClientSource.COMO);
        if (!providerClient) {
            logger.error('[WEBHOOKS] [COMO] - Provider client not found', { comoMemberId });
            return;
        }

        // TODO CRM migrate to providerClients collection instead of clients collection
        const client = await this._clientsRepository.findOne({
            filter: { email: providerClient.email, restaurantId: toDbId(providerClient.restaurantId) },
            options: { lean: true },
        });
        if (!client) {
            logger.error('[WEBHOOKS] [COMO] - Client not found', {
                email: providerClient.email,
                restaurantId: providerClient.restaurantId,
            });
            return;
        }

        const giftDraw = await this._giftDrawsRepository.findOne({
            filter: { clientId: client._id, restaurantId: toDbId(providerClient.restaurantId), giftId: toDbId(giftId) },
            options: { lean: true },
        });
        if (!giftDraw) {
            logger.error('[WEBHOOKS] [COMO] - Gift draw not found', {
                clientId: client._id,
                restaurantId: providerClient.restaurantId,
                giftId,
            });
            return;
        }

        await this._giftDrawsUseCases.setGiftDrawRetrieved(giftDraw._id.toString());
    }

    private _mapComoGiftNameToGiftId(giftName: string): string | null {
        switch (giftName) {
            case 'wof-dev-boite6OG':
                return '67fd08ec721bd35f1ae9d6ca';
            case 'wof-dev-doughnutauchoix':
                return '67fd08ec721bd35f1ae9d6c6';
            case 'wof-dev-OG':
                return '67fd08ec721bd35f1ae9d6d6';
            case 'wof-dev-boissongourmande':
                return '67fd08ec721bd35f1ae9d6ce';
            case 'wof-dev-espresso':
                return '67fd08ec721bd35f1ae9d6c2';
        }
        return null;
    }
}
