import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import { getRestaurantsReviewsExternalApiQueryValidator } from '@malou-io/package-dto';
import { IRestaurant } from '@malou-io/package-models';
import { ApiKeyRole, PlatformKey } from '@malou-io/package-utils';

import { Query } from ':helpers/decorators/validators';
import { ReviewPagination } from ':helpers/pagination';
import { GetMapstrRestaurantsUseCase } from ':modules/api-keys/use-cases/get-mapstr-restaurants.use-case';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import UsersUseCases from ':modules/users/users.use-cases';

@singleton()
export default class ApiKeysController {
    constructor(
        private readonly _usersUseCases: UsersUseCases,
        private readonly _reviewsRepository: ReviewsRepository,
        private _restaurantsUseCases: RestaurantsUseCases,
        private _getMapstrRestaurantsUseCase: GetMapstrRestaurantsUseCase
    ) {}

    async getRestaurantsWithPlatforms(_req: Request, res: Response, next: NextFunction) {
        try {
            const { apiKey } = res.locals;
            if (apiKey.name === PlatformKey.MAPSTR) {
                const restaurants = await this._getMapstrRestaurantsUseCase.execute();
                res.json({ msg: 'success', data: restaurants });
                return;
            }
            let filter = {};
            if (apiKey.role === ApiKeyRole.CLIENT) {
                const userWithRestaurants = await this._usersUseCases.getUserWithActualRestaurants(apiKey.userId);
                const restaurantIds = userWithRestaurants.restaurants.map((r) => r.restaurantId);
                filter = { _id: { $in: restaurantIds } };
            }
            const excludedFields: (keyof IRestaurant)[] = [
                'access',
                'active',
                'coverChanged',
                'logoChanged',
                'bricks',
                'currentState',
                'calendarEvents',
                'ai',
                'organizationId',
                'commentsLastUpdate',
                'bookmarkedPosts',
            ];
            const restaurantsWithPlatforms = await this._restaurantsUseCases.getRestaurantsWithPlatformsIds(filter, excludedFields);
            res.json({ msg: 'success', data: restaurantsWithPlatforms });
        } catch (err) {
            next(err);
        }
    }

    @Query(getRestaurantsReviewsExternalApiQueryValidator)
    async getReviews(req: Request, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            const {
                page_number: pageNumber,
                page_size: pageSize,
                total,
                text,
                ratings,
                with_text: withText,
                without_text: withoutText,
                sort_order: sortOrder,
                sort_by: sortBy,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                pending,
                archived,
            } = req.query as any;
            const pagination = new ReviewPagination({ pageNumber, pageSize, total });
            const filters: ReviewFiltersInput = {
                text,
                ratings,
                withText: withText == 'true',
                withoutText: withoutText == 'true',
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                restaurantIds: [restaurantId],
            };

            const reviews = await this._reviewsRepository.getRestaurantReviewsPaginated({
                pagination,
                filters,
                sort: { sortBy: sortBy ?? 'date', sortOrder },
            });

            return res.json({ data: reviews });
        } catch (err) {
            next(err);
        }
    }
}
