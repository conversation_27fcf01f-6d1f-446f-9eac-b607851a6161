import { singleton } from 'tsyringe';

import { ZeltyCredentialDto } from '@malou-io/package-dto';

import { ZeltyCredentialsRepository } from ':modules/credentials/platforms/zelty/zelty.repository';

@singleton()
export class UpsertRestaurantZeltyCredentialUseCase {
    constructor(private readonly _zeltyCredentialsRepository: ZeltyCredentialsRepository) {}

    async execute(restaurantId: string, accessToken: string): Promise<ZeltyCredentialDto> {
        const credential = await this._zeltyCredentialsRepository.upsertZeltyCredential(restaurantId, accessToken);
        return credential.toDto();
    }
}
