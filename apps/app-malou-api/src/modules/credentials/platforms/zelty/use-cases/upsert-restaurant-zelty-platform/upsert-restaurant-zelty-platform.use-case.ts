import { singleton } from 'tsyringe';

import { PlatformDto } from '@malou-io/package-dto';
import { PlatformAccessStatus, PlatformAccessType, PlatformKey } from '@malou-io/package-utils';

import { PlatformsDtoMapper } from ':modules/platforms/platforms.dto-mapper';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class UpsertRestaurantZeltyPlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsDtoMapper: PlatformsDtoMapper
    ) {}

    async execute(restaurantId: string, zeltyRestaurantId: string, credentialId: string): Promise<PlatformDto> {
        const platform = await this._platformsRepository.upsertZeltyPlatform(restaurantId, zeltyRestaurantId, credentialId);
        await this._restaurantsRepository.updateRestaurantAccessByPlatformKey({
            restaurantId,
            platformKey: PlatformKey.ZELTY,
            status: PlatformAccessStatus.VERIFIED,
            accessType: PlatformAccessType.AUTO,
            active: true,
        });
        return this._platformsDtoMapper.toDto(platform);
    }
}
