import { singleton } from 'tsyringe';

import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { CredentialValidityStatus } from ':modules/credentials/platforms/interfaces';
import { ZeltyCredentialsRepository } from ':modules/credentials/platforms/zelty/zelty.repository';
import { Platform } from ':modules/platforms/platforms.entity';

@singleton()
export class GetZeltyAccountPermissionsStatusUseCase {
    constructor(
        private readonly _zeltyProviderWrapper: ZeltyProviderWrapper,
        private readonly _zeltyCredentialsRepository: ZeltyCredentialsRepository
    ) {}

    async execute(platform: Platform): Promise<CredentialValidityStatus> {
        try {
            const credential = await this._zeltyCredentialsRepository.getCredentialByRestaurantId(platform.restaurantId.toString());
            if (!credential) {
                return {
                    isValid: false,
                    missing: [],
                };
            }

            await this._zeltyProviderWrapper.getZeltyRestaurants(credential.accessToken);
            return {
                isValid: true,
                missing: [],
            };
        } catch (error) {
            return {
                isValid: false,
                missing: [],
            };
        }
    }
}
