import { singleton } from 'tsyringe';

import { ZeltyRestaurantDto } from '@malou-io/package-dto';
import { PlatformKey } from '@malou-io/package-utils';

import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import PlatformsRepository from ':modules/platforms/platforms.repository';

@singleton()
export class GetZeltyRestaurantsForConnectionUseCase {
    constructor(
        private readonly _zeltyProviderWrapper: ZeltyProviderWrapper,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute(apiKey: string): Promise<ZeltyRestaurantDto[]> {
        const zeltyRestaurants = await this._zeltyProviderWrapper.getZeltyRestaurants(apiKey);
        const zeltyRestaurantsWithData = zeltyRestaurants.filter((restaurant) => restaurant.id !== undefined && restaurant.name);

        // Get already connected platforms
        const connectedPlatforms = await this._platformsRepository.getPlatformsBySocialIdsAndPlatformKey(
            zeltyRestaurantsWithData.map((restaurant) => restaurant.id!.toString()),
            PlatformKey.ZELTY
        );

        return zeltyRestaurantsWithData.map((restaurant) => ({
            id: restaurant.id!.toString(),
            name: restaurant.name!,
            address: restaurant.address ?? null,
            restaurantId:
                connectedPlatforms.find((platform) => platform.socialId === restaurant.id!.toString())?.restaurantId?.toString() ?? null,
        }));
    }
}
