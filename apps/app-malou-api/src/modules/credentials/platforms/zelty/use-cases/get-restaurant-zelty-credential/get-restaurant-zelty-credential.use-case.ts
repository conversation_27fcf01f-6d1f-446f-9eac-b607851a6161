import { singleton } from 'tsyringe';

import { ZeltyCredentialDto } from '@malou-io/package-dto';

import { ZeltyCredentialsRepository } from ':modules/credentials/platforms/zelty/zelty.repository';

@singleton()
export class GetRestaurantZeltyCredentialUseCase {
    constructor(private readonly _zeltyCredentialsRepository: ZeltyCredentialsRepository) {}

    async execute(restaurantId: string): Promise<ZeltyCredentialDto | null> {
        const credential = await this._zeltyCredentialsRepository.getCredentialByRestaurantId(restaurantId);
        return credential ? credential.toDto() : null;
    }
}
