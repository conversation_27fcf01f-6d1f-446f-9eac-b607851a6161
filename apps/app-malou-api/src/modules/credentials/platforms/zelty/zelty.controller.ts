import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetRestaurantZeltyCredentialParamsDto,
    getRestaurantZeltyCredentialParamsValidator,
    GetZeltyRestaurantsForConnectionQueryDto,
    getZeltyRestaurantsForConnectionQueryValidator,
    PlatformDto,
    UpsertRestaurantZeltyCredentialBodyDto,
    upsertRestaurantZeltyCredentialBodyValidator,
    UpsertRestaurantZeltyCredentialParamsDto,
    upsertRestaurantZeltyCredentialParamsValidator,
    UpsertRestaurantZeltyPlatformBodyDto,
    upsertRestaurantZeltyPlatformBodyValidator,
    UpsertRestaurantZeltyPlatformParamsDto,
    upsertRestaurantZeltyPlatformParamsValidator,
    ZeltyCredentialDto,
    ZeltyRestaurantDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params, Query } from ':helpers/decorators/validators';
import { GetRestaurantZeltyCredentialUseCase } from ':modules/credentials/platforms/zelty/use-cases/get-restaurant-zelty-credential/get-restaurant-zelty-credential.use-case';
import { GetZeltyRestaurantsForConnectionUseCase } from ':modules/credentials/platforms/zelty/use-cases/get-zelty-restaurants-for-connection/get-zelty-restaurants-for-connection.use-case';
import { UpsertRestaurantZeltyCredentialUseCase } from ':modules/credentials/platforms/zelty/use-cases/upsert-restaurant-zelty-credential/upsert-restaurant-zelty-credential.use-case';
import { UpsertRestaurantZeltyPlatformUseCase } from ':modules/credentials/platforms/zelty/use-cases/upsert-restaurant-zelty-platform/upsert-restaurant-zelty-platform.use-case';

@singleton()
export default class ZeltyCredentialsController {
    constructor(
        private readonly _getRestaurantZeltyCredentialUseCase: GetRestaurantZeltyCredentialUseCase,
        private readonly _getZeltyRestaurantsForConnectionUseCase: GetZeltyRestaurantsForConnectionUseCase,
        private readonly _upsertRestaurantZeltyCredentialUseCase: UpsertRestaurantZeltyCredentialUseCase,
        private readonly _upsertRestaurantZeltyPlatformUseCase: UpsertRestaurantZeltyPlatformUseCase
    ) {}

    @Params(getRestaurantZeltyCredentialParamsValidator)
    async handleGetRestaurantZeltyCredential(
        req: Request<GetRestaurantZeltyCredentialParamsDto>,
        res: Response<ApiResultV2<{ credential: ZeltyCredentialDto | null }>>,
        next: NextFunction
    ) {
        try {
            const credential = await this._getRestaurantZeltyCredentialUseCase.execute(req.params.restaurantId);
            res.status(200).json({ data: { credential } });
        } catch (err) {
            next(err);
        }
    }

    @Query(getZeltyRestaurantsForConnectionQueryValidator)
    async handleGetZeltyRestaurantsForConnection(
        req: Request<any, any, any, GetZeltyRestaurantsForConnectionQueryDto>,
        res: Response<ApiResultV2<ZeltyRestaurantDto[]>>,
        next: NextFunction
    ) {
        try {
            const apiKey = req.query.apiKey;
            const zeltyRestaurants = await this._getZeltyRestaurantsForConnectionUseCase.execute(apiKey);
            res.status(200).json({ data: zeltyRestaurants });
        } catch (err) {
            next(err);
        }
    }

    @Params(upsertRestaurantZeltyCredentialParamsValidator)
    @Body(upsertRestaurantZeltyCredentialBodyValidator)
    async handleUpsertRestaurantZeltyCredential(
        req: Request<UpsertRestaurantZeltyCredentialParamsDto, any, UpsertRestaurantZeltyCredentialBodyDto>,
        res: Response<ApiResultV2<ZeltyCredentialDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { accessToken } = req.body;
            const credential = await this._upsertRestaurantZeltyCredentialUseCase.execute(restaurantId, accessToken);
            res.status(200).json({ data: credential });
        } catch (err) {
            next(err);
        }
    }

    @Params(upsertRestaurantZeltyPlatformParamsValidator)
    @Body(upsertRestaurantZeltyPlatformBodyValidator)
    async handleUpsertRestaurantZeltyPlatform(
        req: Request<UpsertRestaurantZeltyPlatformParamsDto, any, UpsertRestaurantZeltyPlatformBodyDto>,
        res: Response<ApiResultV2<PlatformDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { zeltyRestaurantId, credentialId } = req.body;
            const platform = await this._upsertRestaurantZeltyPlatformUseCase.execute(restaurantId, zeltyRestaurantId, credentialId);
            res.status(200).json({ data: platform });
        } catch (err) {
            next(err);
        }
    }
}
