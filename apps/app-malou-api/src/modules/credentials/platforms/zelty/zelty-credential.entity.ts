import { ZeltyCredentialDto } from '@malou-io/package-dto';
import { RemoveMethodsFromClass } from '@malou-io/package-utils';

type ZeltyCredentialProps = RemoveMethodsFromClass<ZeltyCredential>;

export class ZeltyCredential {
    id: string;
    restaurantId: string;
    accessToken: string;

    constructor(data: ZeltyCredentialProps) {
        this.id = data.id;
        this.restaurantId = data.restaurantId;
        this.accessToken = data.accessToken;
    }

    toDto(): ZeltyCredentialDto {
        return {
            id: this.id,
            restaurantId: this.restaurantId,
            accessToken: this.accessToken,
        };
    }
}
