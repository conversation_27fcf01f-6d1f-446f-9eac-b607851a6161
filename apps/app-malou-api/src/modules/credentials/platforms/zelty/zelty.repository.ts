import { singleton } from 'tsyringe';

import { EntityRepository, IZeltyCredential, toDbId, ZeltyCredentialModel } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { ZeltyCredential } from ':modules/credentials/platforms/zelty/zelty-credential.entity';

@singleton()
export class ZeltyCredentialsRepository extends EntityRepository<IZeltyCredential> {
    constructor() {
        super(ZeltyCredentialModel);
    }

    async getCredentialByRestaurantId(restaurantId: string): Promise<ZeltyCredential | null> {
        const credential = await this.findOne({
            filter: { restaurantId: toDbId(restaurantId), key: PlatformKey.ZELTY },
            options: { lean: true },
        });

        return credential ? this._toEntity(credential) : null;
    }

    async upsertZeltyCredential(restaurantId: string, accessToken: string): Promise<ZeltyCredential> {
        const credential = await this.upsert({
            filter: { restaurantId: toDbId(restaurantId), key: PlatformKey.ZELTY },
            update: { accessToken, authId: accessToken },
            options: { lean: true },
        });

        return this._toEntity(credential);
    }

    private _toEntity(data: IZeltyCredential): ZeltyCredential {
        return new ZeltyCredential({
            id: data._id.toString(),
            restaurantId: data.restaurantId.toString(),
            accessToken: data.accessToken,
        });
    }
}
