import { Router } from 'express';
import { singleton } from 'tsyringe';

import ZeltyCredentialsController from ':modules/credentials/platforms/zelty/zelty.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class ZeltyCredentialsRouter {
    constructor(private _zeltyCredentialsController: ZeltyCredentialsController) {}

    init(router: Router): void {
        router.get('/zelty/restaurant/:restaurant_id', authorize(), (req, res, next) =>
            this._zeltyCredentialsController.handleGetRestaurantZeltyCredential(req, res, next)
        );

        router.get('/zelty/restaurants', authorize(), (req, res, next) =>
            this._zeltyCredentialsController.handleGetZeltyRestaurantsForConnection(req, res, next)
        );

        router.post('/zelty/restaurant/:restaurant_id/credential', authorize(), (req, res, next) =>
            this._zeltyCredentialsController.handleUpsertRestaurantZeltyCredential(req, res, next)
        );

        router.post('/zelty/restaurant/:restaurant_id/platform', authorize(), (req, res, next) =>
            this._zeltyCredentialsController.handleUpsertRestaurantZeltyPlatform(req, res, next)
        );
    }
}
