import { autoInjectable } from 'tsyringe';

import { ProcessKeywordsScoreBodyDto } from '@malou-io/package-dto';
import { IKeywordAnalysis } from '@malou-io/package-models';

import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { ProcessKeywordsScoreUseCase } from ':modules/keywords/use-cases/process-keywords-score/process-keywords-score.use-case';
import { RestaurantKeyword } from ':modules/restaurant-keywords/entities/restaurant-keywords.entity';

@autoInjectable()
export class KeywordsScoreUseCases {
    constructor(private readonly _processKeywordsScoreUseCase: ProcessKeywordsScoreUseCase) {}

    generateKeywordAnalysis = async (data: ProcessKeywordsScoreBodyDto): Promise<IKeywordAnalysis> => {
        const result = await this._processKeywordsScoreUseCase.execute(data);
        const bricksFound = result.bricksFound ?? [];
        return {
            keywords: bricksFound,
            score: result.score,
            count: bricksFound.length,
        };
    };

    buildBricksFromKeywords = (restaurantKeywords: RestaurantKeyword[]): Breakdown[] => {
        const bricks = restaurantKeywords.flatMap((restaurantKeyword: RestaurantKeyword) => {
            const keywordBricks = restaurantKeyword.keyword.bricks ?? [];
            const customKeyword = restaurantKeyword.keyword.isCustomerInput
                ? new Breakdown({ text: restaurantKeyword.keyword.text.toLowerCase(), category: 'customerInput' })
                : null;
            if (customKeyword) {
                return [...keywordBricks, customKeyword];
            }
            return [...keywordBricks];
        });
        return bricks
            .filter((brick) => brick.text)
            .sort((a, _b) => {
                // used for next step, customerInput is prio if another brick has same text
                if (a.category === 'customerInput') {
                    return -1;
                }
                return 0;
            })
            .filter(
                // remove duplicate text bricks
                (brick, index, self) => index === self.findIndex((t) => t.text === brick.text)
            )
            .sort((a, b) => {
                if (a.text.length < b.text.length) {
                    return 1;
                }
                return -1;
            });
    };
}
