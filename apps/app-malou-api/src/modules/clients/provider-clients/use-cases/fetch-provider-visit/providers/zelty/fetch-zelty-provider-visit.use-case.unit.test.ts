import { container } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { ZeltyOrder } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.interfaces';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { FetchZeltyProviderVisitUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit/providers/zelty/fetch-zelty-provider-visit.use-case';

describe('FetchZeltyProviderVisitUseCase', () => {
    let zeltyProviderWrapperMock: jest.Mocked<ZeltyProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ZeltyProviderWrapper
        zeltyProviderWrapperMock = {
            getZeltyClient: jest.fn(),
            getZeltyClients: jest.fn(),
            getZeltyOrder: jest.fn(),
        } as unknown as jest.Mocked<ZeltyProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ZeltyProviderWrapper, zeltyProviderWrapperMock);
    });

    const createMockZeltyOrder = (overrides: Partial<ZeltyOrder> = {}): ZeltyOrder => ({
        id: 12345,
        remote_id: 'remote-order-123',
        id_delivery_address: null,
        id_delivery_zone: null,
        id_restaurant: 1,
        id_promotion: null,
        address: null,
        buzzer_ref: null,
        comment: 'Special instructions for the order',
        current_course: 1,
        customer: {
            id: 'customer-123',
            uuid: 'customer-uuid-456',
            remote_id: 'customer-remote-789',
            nice_name: 'John Doe',
            updated_at: '2023-01-01T00:00:00Z',
            name: 'Doe',
            fname: 'John',
            company: null,
            card: 'card-123',
            phone: '+33123456789',
            phone2: null,
            mail: '<EMAIL>',
            birthday: '1990-01-01',
            balance: 100,
            personal_info: null,
            loyalty: 50,
            registration: '2023-01-01T00:00:00Z',
            default_address: 1,
            sms_optin: true,
            mail_optin: true,
            turnover: 500,
            nb_orders: 10,
            last_order_date: '2023-12-01T00:00:00Z',
            vip: false,
            other: null,
            metadata: null,
            addresses: [],
            country_code: 'FR',
            last_restaurant_id: 1,
        },
        display_id: 'ORDER-001',
        due_date: new Date('2023-12-25T19:00:00Z'),
        first_name: 'John',
        fulfillment_type: null,
        items: [],
        mode: 'eat_in' as any,
        needed_transactions: [],
        phone: '+33123456789',
        promotion_discount: null,
        restaurant_remote_id: 'restaurant-remote-123',
        seats: 4,
        source: 'web' as any,
        table: 5,
        total: 85.5,
        transactions: [],
        virtual_brand_name: null,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert Zelty order to ProviderVisit', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 12345,
                due_date: new Date('2023-12-25T19:00:00Z'),
                seats: 4,
                comment: 'Birthday celebration',
                total: 125.75,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result).toBeInstanceOf(ProviderVisit);
            expect(result.providerVisitId).toBe('12345');
            expect(result.visitDate).toEqual(new Date('2023-12-25T19:00:00Z'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields).toEqual(mockZeltyOrder);
            expect(result.providerVisitFields.comment).toBe('Birthday celebration');
        });

        it('should throw MalouError when Zelty order is not found', async () => {
            const visitId = 'non-existent-visit';
            const restaurantId = 'test-restaurant-id';

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(null);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.ZELTY_ORDER_NOT_FOUND,
                    metadata: {
                        visitId,
                        restaurantId,
                    },
                })
            );

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
        });

        it('should handle Zelty order with complete data correctly', async () => {
            const visitId = 'complete-visit';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 67890,
                remote_id: 'complete-remote-order',
                due_date: new Date('2023-08-15T20:30:00Z'),
                seats: 6,
                comment: 'Anniversary dinner - vegetarian options needed',
                total: 350.75,
                mode: 'delivery' as any,
                source: 'mobile' as any,
                table: 12,
                display_id: 'ORDER-067890',
                promotion_discount: 25.5,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result).toBeInstanceOf(ProviderVisit);
            expect(result.providerVisitId).toBe('67890');
            expect(result.visitDate).toEqual(new Date('2023-08-15T20:30:00Z'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields).toEqual(mockZeltyOrder);
            expect(result.providerVisitFields.total).toBe(350.75);
            expect(result.providerVisitFields.comment).toBe('Anniversary dinner - vegetarian options needed');
            expect(result.providerVisitFields.mode).toBe('delivery');
        });

        it('should handle Zelty order with null/undefined fields correctly', async () => {
            const visitId = 'minimal-visit';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 11111,
                due_date: new Date('2023-06-10T19:00:00Z'),
                seats: 2,
                comment: null,
                total: null,
                promotion_discount: null,
                table: null,
                display_id: null,
                virtual_brand_name: null,
                buzzer_ref: null,
                fulfillment_type: null,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result).toBeInstanceOf(ProviderVisit);
            expect(result.providerVisitId).toBe('11111');
            expect(result.visitDate).toEqual(new Date('2023-06-10T19:00:00Z'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields.comment).toBeNull();
            expect(result.providerVisitFields.total).toBeNull();
            expect(result.providerVisitFields.table).toBeNull();
            expect(result.providerVisitFields.display_id).toBeNull();
        });

        it('should handle different visit IDs correctly', async () => {
            const visitId = 'different-visit-id';
            const restaurantId = 'different-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 99999,
                due_date: new Date('2023-09-20T18:30:00Z'),
                seats: 8,
                comment: 'Corporate dinner',
                total: 450.0,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result.providerVisitId).toBe('99999');
            expect(result.visitDate).toEqual(new Date('2023-09-20T18:30:00Z'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields.seats).toBe(8);
            expect(result.providerVisitFields.comment).toBe('Corporate dinner');
        });

        it('should propagate errors from Zelty service', async () => {
            const visitId = 'test-visit-id';
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('Zelty service error');

            zeltyProviderWrapperMock.getZeltyOrder.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId)).rejects.toThrow(mockError);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
        });

        it('should handle long visit IDs correctly', async () => {
            const visitId = 'very-long-visit-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 77777,
                due_date: new Date('2023-07-05T19:00:00Z'),
                seats: 3,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result.providerVisitId).toBe('77777');
            expect(result.providerVisitFields.seats).toBe(3);
        });

        it('should handle special characters in visit ID correctly', async () => {
            const visitId = 'visit-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 55555,
                due_date: new Date('2023-11-12T20:00:00Z'),
                seats: 5,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
            expect(result.providerVisitId).toBe('55555');
            expect(result.providerVisitFields.seats).toBe(5);
        });

        it('should preserve all Zelty order data during conversion', async () => {
            const visitId = 'detailed-visit';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 88888,
                remote_id: 'detailed-remote-order',
                due_date: new Date('2023-10-31T19:30:00Z'),
                seats: 4,
                comment: 'Halloween themed dinner - allergic to nuts',
                total: 220.5,
                mode: 'takeaway' as any,
                source: 'kiosk' as any,
                table: 8,
                display_id: 'ORDER-088888',
                promotion_discount: 15.0,
                phone: '+33987654321',
                first_name: 'Alice',
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(result).toBeInstanceOf(ProviderVisit);
            expect(result.providerVisitId).toBe('88888');
            expect(result.visitDate).toEqual(new Date('2023-10-31T19:30:00Z'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.providerVisitFields).toEqual(mockZeltyOrder);
            expect(result.providerVisitFields.comment).toBe('Halloween themed dinner - allergic to nuts');
            expect(result.providerVisitFields.total).toBe(220.5);
            expect(result.providerVisitFields.mode).toBe('takeaway');
        });

        it('should handle undefined Zelty order response', async () => {
            const visitId = 'undefined-visit';
            const restaurantId = 'test-restaurant-id';

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(undefined as any);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);

            await expect(useCase.execute(visitId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.ZELTY_ORDER_NOT_FOUND,
                    metadata: {
                        visitId,
                        restaurantId,
                    },
                })
            );

            expect(zeltyProviderWrapperMock.getZeltyOrder).toHaveBeenCalledWith(visitId);
        });

        it('should handle orders with different modes correctly', async () => {
            const visitId = 'takeaway-order';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 33333,
                due_date: new Date('2023-05-20T19:00:00Z'),
                seats: 2,
                mode: 'takeaway' as any,
                comment: 'Takeaway order - ready in 15 minutes',
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(result).toBeInstanceOf(ProviderVisit);
            expect(result.providerVisitFields.mode).toBe('takeaway');
            expect(result.providerVisitFields.comment).toBe('Takeaway order - ready in 15 minutes');
        });

        it('should handle orders with different party sizes correctly', async () => {
            const visitId = 'large-party-order';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 44444,
                due_date: new Date('2023-12-31T21:00:00Z'),
                seats: 12,
                comment: 'New Year celebration - large group',
                total: 850.0,
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(result.providerVisitFields.seats).toBe(12);
            expect(result.providerVisitFields.comment).toBe('New Year celebration - large group');
            expect(result.providerVisitFields.total).toBe(850.0);
        });

        it('should handle orders with due_date as null correctly', async () => {
            const visitId = 'no-due-date-order';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyOrder = createMockZeltyOrder({
                id: 22222,
                due_date: null,
                seats: 4,
                comment: 'Order without specific due date',
            });

            zeltyProviderWrapperMock.getZeltyOrder.mockResolvedValue(mockZeltyOrder);

            const useCase = container.resolve(FetchZeltyProviderVisitUseCase);
            const result = await useCase.execute(visitId, restaurantId);

            expect(result.providerVisitId).toBe('22222');
            const now = Date.now();
            const diff = Math.abs(result.visitDate.getTime() - now);
            expect(diff).toBeLessThan(500); // Should use current date when due_date is null
            expect(result.providerVisitFields.comment).toBe('Order without specific due date');
        });
    });
});
