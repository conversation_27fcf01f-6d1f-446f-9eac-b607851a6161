import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { IFetchProviderVisitUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-visit/providers/fetch-provider-visit.use-case.interface';

@singleton()
export class FetchZeltyProviderVisitUseCase implements IFetchProviderVisitUseCase {
    constructor(private readonly _zeltyProviderWrapper: ZeltyProviderWrapper) {}

    async execute(visitId: string, restaurantId: string): Promise<ProviderVisit> {
        const zeltyVisit = await this._zeltyProviderWrapper.getZeltyOrder(visitId);
        if (!zeltyVisit) {
            throw new MalouError(MalouErrorCode.ZELTY_ORDER_NOT_FOUND, {
                metadata: {
                    visitId,
                    restaurantId,
                },
            });
        }
        return ProviderVisit.fromZeltyVisit(zeltyVisit, restaurantId);
    }
}
