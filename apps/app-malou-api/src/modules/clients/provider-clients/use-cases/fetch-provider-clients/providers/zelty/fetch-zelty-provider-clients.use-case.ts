import { singleton } from 'tsyringe';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { IFetchProviderClientsListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-clients/providers/fetch-provider-clients.use-case.interface';

@singleton()
export class FetchZeltyProviderClientsUseCase implements IFetchProviderClientsListUseCase {
    constructor(private readonly _zeltyProviderWrapper: ZeltyProviderWrapper) {}

    async execute(restaurantId: string): Promise<ProviderClient[]> {
        const zeltyClients = await this._zeltyProviderWrapper.getZeltyClients();
        return zeltyClients.map((zeltyClient) => ProviderClient.fromZeltyClient(zeltyClient, restaurantId));
    }
}
