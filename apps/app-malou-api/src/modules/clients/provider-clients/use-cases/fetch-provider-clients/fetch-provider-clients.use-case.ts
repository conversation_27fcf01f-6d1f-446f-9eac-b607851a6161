import { inject, singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { IFetchProviderClientsListUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-clients/providers/fetch-provider-clients.use-case.interface';
import { FetchZeltyProviderClientsUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-clients/providers/zelty/fetch-zelty-provider-clients.use-case';

@singleton()
export class FetchProviderClientsUseCase {
    constructor(
        @inject(FetchZeltyProviderClientsUseCase) private readonly _fetchZeltyProviderClientsUseCase: IFetchProviderClientsListUseCase
    ) {}

    async execute(restaurantId: string, platformKey: PlatformKey): Promise<ProviderClient[]> {
        let fetchProviderClientsUseCase: IFetchProviderClientsListUseCase;
        switch (platformKey) {
            case PlatformKey.ZELTY:
                fetchProviderClientsUseCase = this._fetchZeltyProviderClientsUseCase;
                break;
            default:
                throw new MalouError(MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY, {
                    metadata: {
                        platformKey,
                    },
                });
        }
        return fetchProviderClientsUseCase.execute(restaurantId);
    }
}
