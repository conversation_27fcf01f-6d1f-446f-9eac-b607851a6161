import { container } from 'tsyringe';

import { ContactMode, ProviderClientSource } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ZeltyClient } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.interfaces';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { FetchZeltyProviderClientsUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-clients/providers/zelty/fetch-zelty-provider-clients.use-case';

describe('FetchZeltyProviderClientsUseCase', () => {
    let zeltyProviderWrapperMock: jest.Mocked<ZeltyProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ZeltyProviderWrapper
        zeltyProviderWrapperMock = {
            getZeltyClient: jest.fn(),
            getZeltyClients: jest.fn(),
        } as unknown as jest.Mocked<ZeltyProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ZeltyProviderWrapper, zeltyProviderWrapperMock);
    });

    const createMockZeltyClient = (overrides: Partial<ZeltyClient> = {}): ZeltyClient => ({
        id: 'test-zelty-id',
        uuid: 'test-uuid',
        remote_id: 'remote-id',
        nice_name: 'John Doe',
        updated_at: '2023-01-01T00:00:00Z',
        name: 'Doe',
        fname: 'John',
        company: null,
        card: 'card-123',
        phone: '+33123456789',
        phone2: null,
        mail: '<EMAIL>',
        birthday: '1990-01-01',
        balance: 100,
        personal_info: null,
        loyalty: 50,
        registration: '2023-01-01T00:00:00Z',
        default_address: 1,
        sms_optin: true,
        mail_optin: true,
        turnover: 500,
        nb_orders: 10,
        last_order_date: '2023-12-01T00:00:00Z',
        vip: false,
        other: null,
        metadata: null,
        addresses: [],
        country_code: 'FR',
        last_restaurant_id: 1,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert Zelty clients to ProviderClients', async () => {
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClients = [
                createMockZeltyClient({ id: 'client-1', fname: 'John', name: 'Doe' }),
                createMockZeltyClient({ id: 'client-2', fname: 'Jane', name: 'Smith' }),
            ];

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue(mockZeltyClients);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toHaveLength(2);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[1]).toBeInstanceOf(ProviderClient);
            expect(result[0].providerClientId).toBe('client-1');
            expect(result[0].firstName).toBe('John');
            expect(result[0].lastName).toBe('Doe');
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].source).toBe(ProviderClientSource.ZELTY);
            expect(result[1].providerClientId).toBe('client-2');
            expect(result[1].firstName).toBe('Jane');
            expect(result[1].lastName).toBe('Smith');
            expect(result[1].restaurantId).toBe(restaurantId);
            expect(result[1].source).toBe(ProviderClientSource.ZELTY);
        });

        it('should return empty array when Zelty service returns empty array', async () => {
            const restaurantId = 'test-restaurant-id';

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue([]);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toEqual([]);
            expect(result).toHaveLength(0);
        });

        it('should handle single Zelty client correctly', async () => {
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: 'single-client',
                fname: 'Alice',
                name: 'Johnson',
                mail: '<EMAIL>',
            });

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue([mockZeltyClient]);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[0].providerClientId).toBe('single-client');
            expect(result[0].firstName).toBe('Alice');
            expect(result[0].lastName).toBe('Johnson');
            expect(result[0].email).toBe('<EMAIL>');
            expect(result[0].restaurantId).toBe(restaurantId);
        });

        it('should handle large number of Zelty clients', async () => {
            const restaurantId = 'test-restaurant-id';

            // Create 50 mock clients
            const mockZeltyClients = Array.from({ length: 50 }, (_, index) =>
                createMockZeltyClient({
                    id: `client-${index}`,
                    fname: `Client${index}`,
                    name: `LastName${index}`,
                    mail: `client${index}@example.com`,
                })
            );

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue(mockZeltyClients);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toHaveLength(50);
            expect(result.every((client) => client instanceof ProviderClient)).toBe(true);
            expect(result.every((client) => client.restaurantId === restaurantId)).toBe(true);
            expect(result.every((client) => client.source === ProviderClientSource.ZELTY)).toBe(true);

            // Check first and last clients
            expect(result[0].providerClientId).toBe('client-0');
            expect(result[0].firstName).toBe('Client0');
            expect(result[49].providerClientId).toBe('client-49');
            expect(result[49].firstName).toBe('Client49');
        });

        it('should handle Zelty clients with null/undefined fields correctly', async () => {
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: 'client-with-nulls',
                fname: null,
                name: null,
                mail: null,
                phone: null,
                birthday: null,
                mail_optin: null,
                sms_optin: null,
            });

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue([mockZeltyClient]);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(ProviderClient);
            expect(result[0].providerClientId).toBe('client-with-nulls');
            expect(result[0].firstName).toBeUndefined();
            expect(result[0].lastName).toBeUndefined();
            expect(result[0].email).toBeUndefined();
            expect(result[0].phone).toBeUndefined();
            expect(result[0].birthday).toBeUndefined();
            expect(result[0].contactOptions).toEqual([]);
            expect(result[0].restaurantId).toBe(restaurantId);
            expect(result[0].source).toBe(ProviderClientSource.ZELTY);
        });

        it('should handle different restaurant IDs correctly', async () => {
            const restaurantId = 'different-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: 'december-client',
                fname: 'December',
                name: 'Client',
            });

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue([mockZeltyClient]);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
            expect(result).toHaveLength(1);
            expect(result[0].providerClientId).toBe('december-client');
            expect(result[0].firstName).toBe('December');
            expect(result[0].lastName).toBe('Client');
            expect(result[0].restaurantId).toBe(restaurantId);
        });

        it('should preserve all Zelty client data during conversion', async () => {
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: 'detailed-client',
                fname: 'Detailed',
                name: 'Client',
                mail: '<EMAIL>',
                phone: '+33123456789',
                birthday: '1985-05-15',
                mail_optin: true,
                sms_optin: false,
                balance: 250,
                loyalty: 100,
                turnover: 1500,
                nb_orders: 25,
                vip: true,
            });

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue([mockZeltyClient]);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(result).toHaveLength(1);
            const providerClient = result[0];

            expect(providerClient.providerClientId).toBe('detailed-client');
            expect(providerClient.firstName).toBe('Detailed');
            expect(providerClient.lastName).toBe('Client');
            expect(providerClient.email).toBe('<EMAIL>');
            expect(providerClient.birthday).toEqual(new Date('1985-05-15'));
            expect(providerClient.restaurantId).toBe(restaurantId);
            expect(providerClient.source).toBe(ProviderClientSource.ZELTY);
            expect(providerClient.contactOptions).toEqual([ContactMode.EMAIL]); // only mail_optin is true
            expect(providerClient.visits).toEqual([]);
        });

        it('should handle contact options correctly', async () => {
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClients = [
                createMockZeltyClient({
                    id: 'email-only',
                    fname: 'Email',
                    name: 'Only',
                    mail_optin: true,
                    sms_optin: false,
                }),
                createMockZeltyClient({
                    id: 'sms-only',
                    fname: 'SMS',
                    name: 'Only',
                    mail_optin: false,
                    sms_optin: true,
                }),
                createMockZeltyClient({
                    id: 'both-options',
                    fname: 'Both',
                    name: 'Options',
                    mail_optin: true,
                    sms_optin: true,
                }),
                createMockZeltyClient({
                    id: 'no-options',
                    fname: 'No',
                    name: 'Options',
                    mail_optin: false,
                    sms_optin: false,
                }),
            ];

            zeltyProviderWrapperMock.getZeltyClients.mockResolvedValue(mockZeltyClients);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);
            const result = await useCase.execute(restaurantId);

            expect(result).toHaveLength(4);
            expect(result[0].contactOptions).toEqual([ContactMode.EMAIL]);
            expect(result[1].contactOptions).toEqual([ContactMode.SMS]);
            expect(result[2].contactOptions).toEqual([ContactMode.EMAIL, ContactMode.SMS]);
            expect(result[3].contactOptions).toEqual([]);
        });

        it('should propagate errors from Zelty service', async () => {
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('Zelty service error');

            zeltyProviderWrapperMock.getZeltyClients.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchZeltyProviderClientsUseCase);

            await expect(useCase.execute(restaurantId)).rejects.toThrow(mockError);

            expect(zeltyProviderWrapperMock.getZeltyClients).toHaveBeenCalledWith();
        });
    });
});
