import { container } from 'tsyringe';

import { MalouErrorCode, ProviderClientSource } from '@malou-io/package-utils';

import { ProviderClient, ProviderClientProps } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ComoClient } from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { CreateComoProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/create-provider-client/providers/como/create-como-provider-client.use-case';

describe('CreateComoProviderClientUseCase', () => {
    let comoProviderWrapperMock: jest.Mocked<ComoProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ComoProviderWrapper
        comoProviderWrapperMock = {
            createComoClient: jest.fn(),
            getComoClient: jest.fn(),
            submitEvent: jest.fn(),
        } as unknown as jest.Mocked<ComoProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ComoProviderWrapper, comoProviderWrapperMock);
    });

    const createMockComoClient = (overrides: Partial<ComoClient> = {}): ComoClient => ({
        comoMemberId: 'test-como-member-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+33123456789',
        status: 'Active',
        commonExtId: 'common-ext-id',
        createdOn: '2023-01-01T00:00:00Z',
        pointsBalance: {
            usedByPayment: false,
            balance: {
                monetary: 100,
                nonMonetary: 50,
            },
        },
        creditBalance: {
            usedByPayment: true,
            balance: {
                monetary: 200,
                nonMonetary: 0,
            },
        },
        genericWallet1Balance: undefined,
        genericWallet2Balance: undefined,
        genericWallet3Balance: undefined,
        consent: 'yes' as any,
        ...overrides,
    });

    const createMockClientData = (
        overrides: Partial<Omit<ProviderClientProps, 'id' | 'providerClientId' | 'source' | 'visits' | 'restaurantId'>> = {}
    ): Omit<ProviderClientProps, 'id' | 'providerClientId' | 'source' | 'visits' | 'restaurantId'> => {
        return {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: {
                prefix: 33,
                digits: 123456789,
            },
            ...overrides,
        };
    };

    describe('execute', () => {
        it('should successfully create and convert Como client to ProviderClient', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                phone: {
                    prefix: 33,
                    digits: 123456789,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'new-como-member-id',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                phoneNumber: '+33123456789',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                phoneNumber: '33123456789',
                allowEmail: true,
            });
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('new-como-member-id');
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should throw MalouError when Como client creation fails', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                email: '<EMAIL>',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(null);

            const useCase = container.resolve(CreateComoProviderClientUseCase);

            await expect(useCase.execute(restaurantId, clientData)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_CREATION_FAILED,
                    metadata: {
                        restaurantId,
                        client: clientData,
                    },
                })
            );

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                phoneNumber: '33123456789',
                allowEmail: true,
            });
        });

        it('should handle client data with null/undefined fields correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: undefined,
                lastName: undefined,
                email: '<EMAIL>',
                phone: undefined,
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'minimal-como-member-id',
                firstName: undefined,
                lastName: undefined,
                email: '<EMAIL>',
                phoneNumber: undefined,
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: undefined,
                lastName: undefined,
                phoneNumber: undefined,
                allowEmail: true,
            });
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('minimal-como-member-id');
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBe('<EMAIL>');
            expect(result.phone).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle client data with complete data correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phone: {
                    prefix: 1,
                    digits: **********,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'complete-como-member-id',
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phoneNumber: '+1**********',
                status: 'Active',
                pointsBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 150,
                        nonMonetary: 75,
                    },
                },
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Alice',
                lastName: 'Johnson',
                phoneNumber: '1**********',
                allowEmail: true,
            });
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('complete-como-member-id');
            expect(result.firstName).toBe('Alice');
            expect(result.lastName).toBe('Johnson');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle phone number formatting correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Pierre',
                lastName: 'Dupont',
                email: '<EMAIL>',
                phone: {
                    prefix: 33,
                    digits: 987654321,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'phone-format-como-member-id',
                firstName: 'Pierre',
                lastName: 'Dupont',
                email: '<EMAIL>',
                phoneNumber: '+33987654321',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Pierre',
                lastName: 'Dupont',
                phoneNumber: '33987654321',
                allowEmail: true,
            });
            expect(result.providerClientId).toBe('phone-format-como-member-id');
            expect(result.firstName).toBe('Pierre');
            expect(result.lastName).toBe('Dupont');
            expect(result.email).toBe('<EMAIL>');
        });

        it('should handle client with only email correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: undefined,
                lastName: undefined,
                email: '<EMAIL>',
                phone: undefined,
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'email-only-como-member-id',
                firstName: undefined,
                lastName: undefined,
                email: '<EMAIL>',
                phoneNumber: undefined,
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: undefined,
                lastName: undefined,
                phoneNumber: undefined,
                allowEmail: true,
            });
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('email-only-como-member-id');
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBe('<EMAIL>');
            expect(result.phone).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle special characters in email correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>',
                phone: undefined,
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'special-email-como-member-id',
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>',
                phoneNumber: undefined,
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                phoneNumber: undefined,
                allowEmail: true,
            });
            expect(result.email).toBe('<EMAIL>');
        });

        it('should handle long restaurant IDs correctly', async () => {
            const restaurantId = 'very-long-restaurant-id-with-many-characters-12345678901234567890';
            const clientData = createMockClientData({
                email: '<EMAIL>',
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'long-restaurant-como-member-id',
                email: '<EMAIL>',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                phoneNumber: '33123456789',
                allowEmail: true,
            });
            expect(result.restaurantId).toBe(restaurantId);
        });

        it('should propagate errors from ComoProviderWrapper', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                email: '<EMAIL>',
            });
            const mockError = new Error('Como service error');

            comoProviderWrapperMock.createComoClient.mockRejectedValue(mockError);

            const useCase = container.resolve(CreateComoProviderClientUseCase);

            await expect(useCase.execute(restaurantId, clientData)).rejects.toThrow(mockError);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                phoneNumber: '33123456789',
                allowEmail: true,
            });
        });

        it('should handle client with different phone prefix correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Maria',
                lastName: 'Garcia',
                email: '<EMAIL>',
                phone: {
                    prefix: 34,
                    digits: 612345678,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'spanish-como-member-id',
                firstName: 'Maria',
                lastName: 'Garcia',
                email: '<EMAIL>',
                phoneNumber: '+34612345678',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Maria',
                lastName: 'Garcia',
                phoneNumber: '34612345678',
                allowEmail: true,
            });
            expect(result.firstName).toBe('Maria');
            expect(result.lastName).toBe('Garcia');
            expect(result.email).toBe('<EMAIL>');
        });

        it('should handle client with zero phone digits correctly', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Zero',
                lastName: 'Phone',
                email: '<EMAIL>',
                phone: {
                    prefix: 33,
                    digits: 0,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'zero-phone-como-member-id',
                firstName: 'Zero',
                lastName: 'Phone',
                email: '<EMAIL>',
                phoneNumber: '+330',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Zero',
                lastName: 'Phone',
                phoneNumber: '330',
                allowEmail: true,
            });
            expect(result.firstName).toBe('Zero');
            expect(result.lastName).toBe('Phone');
            expect(result.email).toBe('<EMAIL>');
        });

        it('should handle client creation with Como client having all optional fields', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Complete',
                lastName: 'User',
                email: '<EMAIL>',
                phone: {
                    prefix: 44,
                    digits: **********,
                },
            });

            const mockComoClient = createMockComoClient({
                comoMemberId: 'complete-optional-como-member-id',
                firstName: 'Complete',
                lastName: 'User',
                email: '<EMAIL>',
                phoneNumber: '+44**********',
                status: 'Active',
                commonExtId: 'ext-id-123',
                createdOn: '2023-12-01T10:30:00Z',
                pointsBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 500,
                        nonMonetary: 250,
                    },
                },
                creditBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 1000,
                        nonMonetary: 100,
                    },
                },
                genericWallet1Balance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 50,
                        nonMonetary: 25,
                    },
                },
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Complete',
                lastName: 'User',
                phoneNumber: '44**********',
                allowEmail: true,
            });
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('complete-optional-como-member-id');
            expect(result.firstName).toBe('Complete');
            expect(result.lastName).toBe('User');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle client creation when Como returns client with different data', async () => {
            const restaurantId = 'test-restaurant-id';
            const clientData = createMockClientData({
                firstName: 'Input',
                lastName: 'Name',
                email: '<EMAIL>',
                phone: {
                    prefix: 1,
                    digits: **********,
                },
            });

            // Como service returns different data than input (simulating Como's processing)
            const mockComoClient = createMockComoClient({
                comoMemberId: 'different-data-como-member-id',
                firstName: 'Processed',
                lastName: 'Name',
                email: '<EMAIL>',
                phoneNumber: '+1**********',
            });

            comoProviderWrapperMock.createComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(CreateComoProviderClientUseCase);
            const result = await useCase.execute(restaurantId, clientData);

            expect(comoProviderWrapperMock.createComoClient).toHaveBeenCalledWith(restaurantId, {
                email: '<EMAIL>',
                firstName: 'Input',
                lastName: 'Name',
                phoneNumber: '1**********',
                allowEmail: true,
            });
            // Result should reflect what Como actually returned
            expect(result.firstName).toBe('Processed');
            expect(result.lastName).toBe('Name');
            expect(result.email).toBe('<EMAIL>');
        });
    });
});
