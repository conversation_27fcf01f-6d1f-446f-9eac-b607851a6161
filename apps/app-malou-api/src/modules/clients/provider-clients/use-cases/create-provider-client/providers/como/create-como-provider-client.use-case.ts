import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient, ProviderClientProps } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ComoClient } from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { ICreateProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/create-provider-client/providers/create-provider-client.use-case.interface';

@singleton()
export class CreateComoProviderClientUseCase implements ICreateProviderClientUseCase {
    constructor(private readonly _comoProviderWrapper: ComoProviderWrapper) {}

    async execute(
        restaurantId: string,
        client: Omit<ProviderClientProps, 'id' | 'providerClientId' | 'source' | 'visits' | 'restaurantId'>
    ): Promise<ProviderClient> {
        const partialComoClient: Partial<ComoClient> = {
            email: client.email,
            firstName: client.firstName,
            lastName: client.lastName,
            phoneNumber: client.phone ? `${client.phone.prefix}${client.phone.digits}` : undefined,
            allowEmail: true,
        };

        const comoClient = await this._comoProviderWrapper.createComoClient(restaurantId, partialComoClient);
        if (!comoClient) {
            throw new MalouError(MalouErrorCode.COMO_CLIENT_CREATION_FAILED, {
                metadata: {
                    restaurantId,
                    client,
                },
            });
        }
        return ProviderClient.fromComoClient(comoClient, restaurantId);
    }
}
