import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { IFetchProviderClientByEmailUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-by-email/providers/fetch-provider-client-by-email.use-case.interface';

@singleton()
export class FetchComoClientByEmailUseCase implements IFetchProviderClientByEmailUseCase {
    constructor(private readonly _comoProviderWrapper: ComoProviderWrapper) {}

    async execute(email: string, restaurantId: string): Promise<ProviderClient> {
        const comoClient = await this._comoProviderWrapper.getComoClient({ email }, restaurantId);
        if (!comoClient) {
            throw new MalouError(MalouErrorCode.COMO_CLIENT_NOT_FOUND, {
                metadata: {
                    email,
                    restaurantId,
                },
            });
        }
        return ProviderClient.fromComoClient(comoClient, restaurantId);
    }
}
