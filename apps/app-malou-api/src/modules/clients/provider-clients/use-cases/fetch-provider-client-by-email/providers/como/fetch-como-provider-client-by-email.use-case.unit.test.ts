import { container } from 'tsyringe';

import { MalouErrorCode, ProviderClientSource } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ComoClient } from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { FetchComoClientByEmailUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client-by-email/providers/como/fetch-como-provider-client-by-email.use-case';

describe('FetchComoClientByEmailUseCase', () => {
    let comoProviderWrapperMock: jest.Mocked<ComoProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ComoProviderWrapper
        comoProviderWrapperMock = {
            getComoClient: jest.fn(),
            submitEvent: jest.fn(),
        } as unknown as jest.Mocked<ComoProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ComoProviderWrapper, comoProviderWrapperMock);
    });

    const createMockComoClient = (overrides: Partial<ComoClient> = {}): ComoClient => ({
        comoMemberId: 'test-como-member-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+33123456789',
        status: 'Active',
        commonExtId: 'common-ext-id',
        createdOn: '2023-01-01T00:00:00Z',
        pointsBalance: {
            usedByPayment: false,
            balance: {
                monetary: 100,
                nonMonetary: 50,
            },
        },
        creditBalance: {
            usedByPayment: true,
            balance: {
                monetary: 200,
                nonMonetary: 0,
            },
        },
        genericWallet1Balance: undefined,
        genericWallet2Balance: undefined,
        genericWallet3Balance: undefined,
        consent: 'yes' as any,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert Como client to ProviderClient by email', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'test-member-id',
                firstName: 'John',
                lastName: 'Doe',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('test-member-id');
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
            expect(result.email).toBe(email);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should throw MalouError when Como client is not found by email', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            comoProviderWrapperMock.getComoClient.mockResolvedValue(null);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);

            await expect(useCase.execute(email, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: {
                        email,
                        restaurantId,
                    },
                })
            );

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
        });

        it('should handle Como client with null/undefined fields correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'minimal-member-id',
                email: email,
                firstName: undefined,
                lastName: undefined,
                phoneNumber: undefined,
                status: undefined,
                commonExtId: undefined,
                createdOn: undefined,
                pointsBalance: undefined,
                creditBalance: undefined,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('minimal-member-id');
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBe(email);
            expect(result.phone).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle Como client with complete data correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'complete-member-id',
                firstName: 'Alice',
                lastName: 'Johnson',
                email: email,
                phoneNumber: '+33987654321',
                status: 'Active',
                commonExtId: 'alice-ext-id',
                createdOn: '2023-05-15T10:30:00Z',
                pointsBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 500,
                        nonMonetary: 250,
                    },
                },
                creditBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 1000,
                        nonMonetary: 0,
                    },
                },
                consent: 'yes' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('complete-member-id');
            expect(result.firstName).toBe('Alice');
            expect(result.lastName).toBe('Johnson');
            expect(result.email).toBe(email);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle different email addresses correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'different-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'different-member-id',
                firstName: 'Bob',
                lastName: 'Wilson',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result.providerClientId).toBe('different-member-id');
            expect(result.firstName).toBe('Bob');
            expect(result.lastName).toBe('Wilson');
            expect(result.email).toBe(email);
            expect(result.restaurantId).toBe(restaurantId);
        });

        it('should propagate errors from Como service', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('Como service error');

            comoProviderWrapperMock.getComoClient.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);

            await expect(useCase.execute(email, restaurantId)).rejects.toThrow(mockError);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
        });

        it('should handle long email addresses correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'long-email-member-id',
                firstName: 'LongEmail',
                lastName: 'Client',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result.providerClientId).toBe('long-email-member-id');
            expect(result.firstName).toBe('LongEmail');
            expect(result.lastName).toBe('Client');
            expect(result.email).toBe(email);
        });

        it('should handle special characters in email addresses correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'special-chars-member-id',
                firstName: 'Special',
                lastName: 'Character',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result.providerClientId).toBe('special-chars-member-id');
            expect(result.firstName).toBe('Special');
            expect(result.lastName).toBe('Character');
            expect(result.email).toBe(email);
        });

        it('should handle uppercase email addresses correctly', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'uppercase-email-member-id',
                firstName: 'Uppercase',
                lastName: 'Email',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
            expect(result.providerClientId).toBe('uppercase-email-member-id');
            expect(result.firstName).toBe('Uppercase');
            expect(result.lastName).toBe('Email');
            expect(result.email).toBe(email);
        });

        it('should preserve all Como client data during conversion', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'detailed-member-id',
                firstName: 'Detailed',
                lastName: 'Client',
                email: email,
                phoneNumber: '+33123456789',
                status: 'Active',
                commonExtId: 'detailed-ext-id',
                createdOn: '2023-12-25T15:45:00Z',
                pointsBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 750,
                        nonMonetary: 300,
                    },
                },
                creditBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 1500,
                        nonMonetary: 100,
                    },
                },
                consent: 'yes' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('detailed-member-id');
            expect(result.firstName).toBe('Detailed');
            expect(result.lastName).toBe('Client');
            expect(result.email).toBe(email);
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle undefined Como client response', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            comoProviderWrapperMock.getComoClient.mockResolvedValue(undefined as any);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);

            await expect(useCase.execute(email, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: {
                        email,
                        restaurantId,
                    },
                })
            );

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ email }, restaurantId);
        });

        it('should handle Como client with different consent values', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'consent-member-id',
                firstName: 'Consent',
                lastName: 'Test',
                email: email,
                consent: 'no' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('consent-member-id');
            expect(result.firstName).toBe('Consent');
            expect(result.lastName).toBe('Test');
            expect(result.email).toBe(email);
        });

        it('should handle Como client with inactive status', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'inactive-member-id',
                firstName: 'Inactive',
                lastName: 'User',
                email: email,
                status: 'Inactive',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('inactive-member-id');
            expect(result.firstName).toBe('Inactive');
            expect(result.lastName).toBe('User');
            expect(result.email).toBe(email);
            expect(result.source).toBe(ProviderClientSource.COMO);
        });

        it('should handle Como client with wallet balances', async () => {
            const email = '<EMAIL>';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'wallet-member-id',
                firstName: 'Wallet',
                lastName: 'User',
                email: email,
                genericWallet1Balance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 50,
                        nonMonetary: 25,
                    },
                },
                genericWallet2Balance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 75,
                        nonMonetary: 0,
                    },
                },
                genericWallet3Balance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 100,
                        nonMonetary: 50,
                    },
                },
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('wallet-member-id');
            expect(result.firstName).toBe('Wallet');
            expect(result.lastName).toBe('User');
            expect(result.email).toBe(email);
            expect(result.source).toBe(ProviderClientSource.COMO);
        });

        it('should handle international email domains correctly', async () => {
            const email = 'international@münchen.de';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: 'international-member-id',
                firstName: 'International',
                lastName: 'User',
                email: email,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoClientByEmailUseCase);
            const result = await useCase.execute(email, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe('international-member-id');
            expect(result.firstName).toBe('International');
            expect(result.lastName).toBe('User');
            expect(result.email).toBe(email);
        });
    });
});
