import { container } from 'tsyringe';

import { ContactMode, MalouErrorCode, ProviderClientSource } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ZeltyClient } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.interfaces';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { FetchZeltyProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/zelty/fetch-zelty-provider-client.use-case';

describe('FetchZeltyProviderClientUseCase', () => {
    let zeltyProviderWrapperMock: jest.Mocked<ZeltyProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ZeltyProviderWrapper
        zeltyProviderWrapperMock = {
            getZeltyClient: jest.fn(),
        } as unknown as jest.Mocked<ZeltyProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ZeltyProviderWrapper, zeltyProviderWrapperMock);
    });

    const createMockZeltyClient = (overrides: Partial<ZeltyClient> = {}): ZeltyClient => ({
        id: 'test-zelty-id',
        uuid: 'test-uuid',
        remote_id: 'remote-id',
        nice_name: 'John Doe',
        updated_at: '2023-01-01T00:00:00Z',
        name: 'Doe',
        fname: 'John',
        company: null,
        card: 'card-123',
        phone: '+33123456789',
        phone2: null,
        mail: '<EMAIL>',
        birthday: '1990-01-01',
        balance: 100,
        personal_info: null,
        loyalty: 50,
        registration: '2023-01-01T00:00:00Z',
        default_address: 1,
        sms_optin: true,
        mail_optin: true,
        turnover: 500,
        nb_orders: 10,
        last_order_date: '2023-12-01T00:00:00Z',
        vip: false,
        other: null,
        metadata: null,
        addresses: [],
        country_code: 'FR',
        last_restaurant_id: 1,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert Zelty client to ProviderClient', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: 'John',
                name: 'Doe',
                mail: '<EMAIL>',
                phone: '+33123456789',
                birthday: '1990-01-01',
                mail_optin: true,
                sms_optin: false,
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.ZELTY);
            expect(result.visits).toEqual([]);
            expect(result.contactOptions).toEqual([ContactMode.EMAIL]); // only mail_optin is true
        });

        it('should throw MalouError when Zelty client is not found', async () => {
            const providerClientId = 'non-existent-client';
            const restaurantId = 'test-restaurant-id';

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(null);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.ZELTY_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
        });

        it('should handle Zelty client with null/undefined fields correctly', async () => {
            const providerClientId = 'client-with-nulls';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: null,
                name: null,
                mail: null,
                phone: null,
                birthday: null,
                mail_optin: null,
                sms_optin: null,
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBeUndefined();
            expect(result.phone).toBeUndefined();
            expect(result.birthday).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.ZELTY);
            expect(result.contactOptions).toEqual([]);
            expect(result.visits).toEqual([]);
        });

        it('should handle Zelty client with complete data correctly', async () => {
            const providerClientId = 'complete-client';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: 'Alice',
                name: 'Johnson',
                mail: '<EMAIL>',
                phone: '+33987654321',
                birthday: '1985-05-15',
                mail_optin: true,
                sms_optin: true,
                balance: 250,
                loyalty: 100,
                turnover: 1500,
                nb_orders: 25,
                vip: true,
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Alice');
            expect(result.lastName).toBe('Johnson');
            expect(result.email).toBe('<EMAIL>');
            expect(result.birthday).toEqual(new Date('1985-05-15'));
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.ZELTY);
            expect(result.contactOptions).toEqual([ContactMode.EMAIL, ContactMode.SMS]); // both optins are true
            expect(result.visits).toEqual([]);
        });

        it('should handle different contact option combinations correctly', async () => {
            const providerClientId = 'contact-options-client';
            const restaurantId = 'test-restaurant-id';

            // Test with only SMS optin
            const mockZeltyClientSmsOnly = createMockZeltyClient({
                id: providerClientId,
                fname: 'SMS',
                name: 'Only',
                mail_optin: false,
                sms_optin: true,
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClientSmsOnly);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result.contactOptions).toEqual([ContactMode.SMS]);
        });

        it('should propagate errors from Zelty service', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('Zelty service error');

            zeltyProviderWrapperMock.getZeltyClient.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(mockError);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
        });

        it('should handle undefined Zelty client response', async () => {
            const providerClientId = 'undefined-client';
            const restaurantId = 'test-restaurant-id';

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(undefined as any);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.ZELTY_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
        });

        it('should handle different provider client IDs correctly', async () => {
            const providerClientId = 'different-client-id';
            const restaurantId = 'different-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: 'Bob',
                name: 'Wilson',
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Bob');
            expect(result.lastName).toBe('Wilson');
            expect(result.restaurantId).toBe(restaurantId);
        });

        it('should handle long provider client IDs correctly', async () => {
            const providerClientId = 'very-long-provider-client-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: 'LongId',
                name: 'Client',
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('LongId');
            expect(result.lastName).toBe('Client');
        });

        it('should handle special characters in provider client ID correctly', async () => {
            const providerClientId = 'client-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';

            const mockZeltyClient = createMockZeltyClient({
                id: providerClientId,
                fname: 'Special',
                name: 'Character',
            });

            zeltyProviderWrapperMock.getZeltyClient.mockResolvedValue(mockZeltyClient);

            const useCase = container.resolve(FetchZeltyProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(zeltyProviderWrapperMock.getZeltyClient).toHaveBeenCalledWith(providerClientId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Special');
            expect(result.lastName).toBe('Character');
        });
    });
});
