import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { Como<PERSON>roviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { IFetchProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/fetch-provider-client.use-case.interface';

@singleton()
export class FetchComoProviderClientUseCase implements IFetchProviderClientUseCase {
    constructor(private readonly _comoProviderWrapper: ComoProviderWrapper) {}

    async execute(providerClientId: string, restaurantId: string): Promise<ProviderClient> {
        const comoClient = await this._comoProviderWrapper.getComoClient({ comoMemberId: providerClientId }, restaurantId);
        if (!comoClient) {
            throw new MalouError(MalouErrorCode.COMO_CLIENT_NOT_FOUND, {
                metadata: {
                    providerClientId,
                    restaurantId,
                },
            });
        }
        return ProviderClient.fromComoClient(comoClient, restaurantId);
    }
}
