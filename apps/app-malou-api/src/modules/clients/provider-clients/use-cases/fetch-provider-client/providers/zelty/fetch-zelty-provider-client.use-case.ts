import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ZeltyProviderWrapper } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.wrapper';
import { IFetchProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/fetch-provider-client.use-case.interface';

@singleton()
export class FetchZeltyProviderClientUseCase implements IFetchProviderClientUseCase {
    constructor(private readonly _zeltyProviderWrapper: ZeltyProviderWrapper) {}

    async execute(providerClientId: string, restaurantId: string): Promise<ProviderClient> {
        const zeltyClient = await this._zeltyProviderWrapper.getZeltyClient(providerClientId);
        if (!zeltyClient) {
            throw new MalouError(MalouErrorCode.ZELTY_CLIENT_NOT_FOUND, {
                metadata: {
                    providerClientId,
                    restaurantId,
                },
            });
        }
        return ProviderClient.fromZeltyClient(zeltyClient, restaurantId);
    }
}
