import { container } from 'tsyringe';

import { MalouErrorCode, ProviderClientSource } from '@malou-io/package-utils';

import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ComoClient } from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';
import { FetchComoProviderClientUseCase } from ':modules/clients/provider-clients/use-cases/fetch-provider-client/providers/como/fetch-como-provider-client.use-case';

describe('FetchComoClientUseCase', () => {
    let comoProviderWrapperMock: jest.Mocked<ComoProviderWrapper>;

    beforeEach(() => {
        container.clearInstances();

        // Mock ComoProviderWrapper
        comoProviderWrapperMock = {
            getComoClient: jest.fn(),
            submitEvent: jest.fn(),
        } as unknown as jest.Mocked<ComoProviderWrapper>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ComoProviderWrapper, comoProviderWrapperMock);
    });

    const createMockComoClient = (overrides: Partial<ComoClient> = {}): ComoClient => ({
        comoMemberId: 'test-como-member-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+33123456789',
        status: 'Active',
        commonExtId: 'common-ext-id',
        createdOn: '2023-01-01T00:00:00Z',
        pointsBalance: {
            usedByPayment: false,
            balance: {
                monetary: 100,
                nonMonetary: 50,
            },
        },
        creditBalance: {
            usedByPayment: true,
            balance: {
                monetary: 200,
                nonMonetary: 0,
            },
        },
        genericWallet1Balance: undefined,
        genericWallet2Balance: undefined,
        genericWallet3Balance: undefined,
        consent: 'yes' as any,
        ...overrides,
    });

    describe('execute', () => {
        it('should successfully fetch and convert Como client to ProviderClient', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('John');
            expect(result.lastName).toBe('Doe');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should throw MalouError when Como client is not found', async () => {
            const providerClientId = 'non-existent-client';
            const restaurantId = 'test-restaurant-id';

            comoProviderWrapperMock.getComoClient.mockResolvedValue(null);

            const useCase = container.resolve(FetchComoProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
        });

        it('should handle Como client with null/undefined fields correctly', async () => {
            const providerClientId = 'client-with-nulls';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: undefined,
                lastName: undefined,
                email: undefined,
                phoneNumber: undefined,
                status: undefined,
                commonExtId: undefined,
                createdOn: undefined,
                pointsBalance: undefined,
                creditBalance: undefined,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBeUndefined();
            expect(result.lastName).toBeUndefined();
            expect(result.email).toBeUndefined();
            expect(result.phone).toBeUndefined();
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle Como client with complete data correctly', async () => {
            const providerClientId = 'complete-client';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phoneNumber: '+33987654321',
                status: 'Active',
                commonExtId: 'alice-ext-id',
                createdOn: '2023-05-15T10:30:00Z',
                pointsBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 500,
                        nonMonetary: 250,
                    },
                },
                creditBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 1000,
                        nonMonetary: 0,
                    },
                },
                consent: 'yes' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Alice');
            expect(result.lastName).toBe('Johnson');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle different provider client IDs correctly', async () => {
            const providerClientId = 'different-client-id';
            const restaurantId = 'different-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Bob',
                lastName: 'Wilson',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Bob');
            expect(result.lastName).toBe('Wilson');
            expect(result.restaurantId).toBe(restaurantId);
        });

        it('should propagate errors from Como service', async () => {
            const providerClientId = 'test-provider-client-id';
            const restaurantId = 'test-restaurant-id';
            const mockError = new Error('Como service error');

            comoProviderWrapperMock.getComoClient.mockRejectedValue(mockError);

            const useCase = container.resolve(FetchComoProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(mockError);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
        });

        it('should handle long provider client IDs correctly', async () => {
            const providerClientId = 'very-long-provider-client-id-with-many-characters-12345678901234567890';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'LongId',
                lastName: 'Client',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('LongId');
            expect(result.lastName).toBe('Client');
        });

        it('should handle special characters in provider client ID correctly', async () => {
            const providerClientId = 'client-with-special-chars-@#$%^&*()';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Special',
                lastName: 'Character',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Special');
            expect(result.lastName).toBe('Character');
        });

        it('should preserve all Como client data during conversion', async () => {
            const providerClientId = 'detailed-client';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Detailed',
                lastName: 'Client',
                email: '<EMAIL>',
                phoneNumber: '+33123456789',
                status: 'Active',
                commonExtId: 'detailed-ext-id',
                createdOn: '2023-12-25T15:45:00Z',
                pointsBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 750,
                        nonMonetary: 300,
                    },
                },
                creditBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 1500,
                        nonMonetary: 100,
                    },
                },
                consent: 'yes' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Detailed');
            expect(result.lastName).toBe('Client');
            expect(result.email).toBe('<EMAIL>');
            expect(result.restaurantId).toBe(restaurantId);
            expect(result.source).toBe(ProviderClientSource.COMO);
            expect(result.visits).toEqual([]);
        });

        it('should handle undefined Como client response', async () => {
            const providerClientId = 'undefined-client';
            const restaurantId = 'test-restaurant-id';

            comoProviderWrapperMock.getComoClient.mockResolvedValue(undefined as any);

            const useCase = container.resolve(FetchComoProviderClientUseCase);

            await expect(useCase.execute(providerClientId, restaurantId)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.COMO_CLIENT_NOT_FOUND,
                    metadata: {
                        providerClientId,
                        restaurantId,
                    },
                })
            );

            expect(comoProviderWrapperMock.getComoClient).toHaveBeenCalledWith({ comoMemberId: providerClientId }, restaurantId);
        });

        it('should handle Como client with different consent values', async () => {
            const providerClientId = 'consent-client';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Consent',
                lastName: 'Test',
                consent: 'no' as any,
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Consent');
            expect(result.lastName).toBe('Test');
        });

        it('should handle Como client with inactive status', async () => {
            const providerClientId = 'inactive-client';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Inactive',
                lastName: 'User',
                status: 'Inactive',
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Inactive');
            expect(result.lastName).toBe('User');
            expect(result.source).toBe(ProviderClientSource.COMO);
        });

        it('should handle Como client with wallet balances', async () => {
            const providerClientId = 'wallet-client';
            const restaurantId = 'test-restaurant-id';

            const mockComoClient = createMockComoClient({
                comoMemberId: providerClientId,
                firstName: 'Wallet',
                lastName: 'User',
                genericWallet1Balance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 50,
                        nonMonetary: 25,
                    },
                },
                genericWallet2Balance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 75,
                        nonMonetary: 0,
                    },
                },
                genericWallet3Balance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 100,
                        nonMonetary: 50,
                    },
                },
            });

            comoProviderWrapperMock.getComoClient.mockResolvedValue(mockComoClient);

            const useCase = container.resolve(FetchComoProviderClientUseCase);
            const result = await useCase.execute(providerClientId, restaurantId);

            expect(result).toBeInstanceOf(ProviderClient);
            expect(result.providerClientId).toBe(providerClientId);
            expect(result.firstName).toBe('Wallet');
            expect(result.lastName).toBe('User');
            expect(result.source).toBe(ProviderClientSource.COMO);
        });
    });
});
