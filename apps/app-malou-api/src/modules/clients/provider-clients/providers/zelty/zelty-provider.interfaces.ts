import z from 'zod';

/**
 * Common types
 */

const zeltyAddressValidator = z.object({
    id: z.number(),
    remote_id: z.string().nullish(),
    name: z.string(),
    street: z.string(),
    street_num: z.string().nullish(),
    zip_code: z.string(),
    city: z.string(),
    formatted_address: z.string().nullish(),
    google_id: z.string().nullish(),
    address_more: z.string().nullish(),
    floor: z.string().nullish(),
    door: z.string().nullish(),
    building: z.string().nullish(),
    code: z.string().nullish(),
});

/**
 * Customers - https://docs.zelty.fr/get-customers-15325975e0
 */

export const zeltyCustomerValidator = z.object({
    id: z.string(),
    uuid: z.string().nullish(),
    remote_id: z.string().nullish(),
    nice_name: z.string().nullish(), // first name + last name
    updated_at: z.string().datetime().nullish(),
    name: z.string().nullable(), // lastname
    fname: z.string().nullable(),
    company: z.string().nullable(),
    card: z.string().nullish(),
    phone: z.string().nullish(),
    phone2: z.string().nullish(),
    mail: z.string().nullish(),
    birthday: z.string().date().nullish(),
    balance: z.number().nullish(),
    personal_info: z.string().nullish(),
    loyalty: z.number().nullish(),
    registration: z.string().datetime().nullish(),
    default_address: z.number().nullish(), // this is an id
    sms_optin: z.boolean().nullish(),
    mail_optin: z.boolean().nullish(),
    turnover: z.number().nullish(),
    nb_orders: z.number().nullish(),
    last_order_date: z.string().datetime().nullish(),
    vip: z.boolean().nullish(),
    other: z.string().nullish(),
    metadata: z.any().nullish(),
    addresses: z.array(zeltyAddressValidator),
    country_code: z.string().nullish(), // ISO code
    last_restaurant_id: z.number().nullish(), // this is an id
});

export type ZeltyClient = z.infer<typeof zeltyCustomerValidator>;

// https://docs.zelty.fr/get-customer-15344313e0
export const zeltyGetCustomerByZeltyIdResponseValidator = z.object({
    customer: zeltyCustomerValidator,
    errno: z.number(),
});

export type ZeltyGetCustomerByZeltyIdResponse = z.infer<typeof zeltyGetCustomerByZeltyIdResponseValidator>;

// https://docs.zelty.fr/get-customers-15325975e0
export const zeltyGetCustomersResponseValidator = z.object({
    customers: z.array(zeltyCustomerValidator),
    errno: z.number(),
});

export type ZeltyGetCustomersResponse = z.infer<typeof zeltyGetCustomersResponseValidator>;

/**
 * Orders - https://docs.zelty.fr/additional-parameters-856033m0
 */

const zeltyPurplePriceValidator = z.object({
    original_amount_inc_tax: z.number().nullish(),
});

const zeltyOrderEntryModifierValidator = z.object({
    id: z.number().nullish(),
    course: z.number().nullish(),
    type: z.string().nullish(),
    name: z.string().nullish(),
    quantity: z.number().nullish(),
    price: zeltyPurplePriceValidator.nullish(),
});

const zeltyFluffyPriceValidator = z.object({
    base_original_amount_inc_tax: z.number().nullish(),
    discounted_amount_inc_tax: z.number().nullish(),
    final_amount_inc_tax: z.number().nullish(),
    original_amount_inc_tax: z.number().nullish(),
});

const zeltyTaxValidator = z.object({
    tax_amount: z.number().nullish(),
    tax_rate: z.number().nullish(),
});

enum ZeltyOrderEntryType {
    MENU = 'menu',
    DISH = 'dish',
}

const zeltyOrderEntryValidator = z.object({
    course: z.number().nullish(),
    item_id: z.string().nullish(),
    modifiers: z.array(zeltyOrderEntryModifierValidator).optional(),
    name: z.string().nullish(),
    price: zeltyFluffyPriceValidator.nullish(),
    tax: zeltyTaxValidator.nullish(),
    type: z.nativeEnum(ZeltyOrderEntryType).nullish(),
});

const zeltyOrderTransactionValidator = z.object({
    id_transaction_method: z.number(),
    remote_id: z.string().nullish(),
    name: z.string(),
    price: z.string(),
});

enum ZeltyOrderFulfillmentType {
    DELIVER_BY_RESTAURANT = 'deliver_by_restaurant',
    DELIVER_BY_PARTNER = 'deliver_by_partner',
}

enum ZeltyOrderMode {
    EAT_IN = 'eat_in',
    TAKEAWAY = 'takeaway',
    DELIVERY = 'delivery',
}

enum ZeltyOrderSource {
    WEB = 'web',
    MOBILE = 'mobile',
    KIOSK = 'kiosk',
}

const zeltyOrderValidator = z.object({
    id: z.number(),
    remote_id: z.string().nullish(),
    id_delivery_address: z.number().nullish(),
    id_delivery_zone: z.number().nullish(),
    id_restaurant: z.number().nullish(),
    id_promotion: z.number().nullish(),
    address: zeltyAddressValidator.nullish(),
    buzzer_ref: z.string().nullish(),
    comment: z.string().nullish(),
    current_course: z.number().nullish(),
    customer: zeltyCustomerValidator,
    display_id: z.string().nullish(),
    due_date: z.date().nullish(),
    first_name: z.string().nullish(),
    fulfillment_type: z.nativeEnum(ZeltyOrderFulfillmentType).nullish(),
    items: z.array(zeltyOrderEntryValidator.nullable()).nullish(),
    mode: z.nativeEnum(ZeltyOrderMode).nullish(),
    needed_transactions: z.array(z.number()).nullish(),
    phone: z.string().nullish(),
    promotion_discount: z.number().nullish(),
    restaurant_remote_id: z.string().nullish(),
    seats: z.number().nullish(),
    source: z.nativeEnum(ZeltyOrderSource).nullish(),
    table: z.number().nullish(),
    total: z.number().nullish(),
    transactions: z.array(zeltyOrderTransactionValidator.nullable()).nullish(),
    virtual_brand_name: z.string().nullish(),
});

export type ZeltyOrder = z.infer<typeof zeltyOrderValidator>;

// https://docs.zelty.fr/get-orders-14185593e0
export const zeltyGetOrdersResponseValidator = z.object({
    orders: z.array(zeltyOrderValidator),
    errno: z.number(),
});

export type ZeltyGetOrdersResponse = z.infer<typeof zeltyGetOrdersResponseValidator>;

// https://docs.zelty.fr/get-order-14211260e0
export const zeltyGetOrderByZeltyIdResponseValidator = z.object({
    order: zeltyOrderValidator,
    errno: z.number(),
});

export type ZeltyGetOrderByZeltyIdResponse = z.infer<typeof zeltyGetOrderByZeltyIdResponseValidator>;

/**
 * Bookings - https://docs.zelty.fr/get-bookings-15359086e0
 */

const zeltyBookingValidator = z.object({
    id: z.number().optional(),
    id_command: z.number().nullish(),
    id_customer: z.number().nullish(),
    arrived_at: z.date().nullish(),
    booking_for: z.date().nullish(),
    cancel_reason: z.number().optional(),
    closed_at: z.date().nullish(),
    comment: z.string().nullish(),
    created_at: z.date(),
    customer: zeltyCustomerValidator.nullish(),
    final_price: z.number().nullish(),
    places: z.number().nullish(),
    remote_id: z.string().nullish(),
    src: z.string().nullish(),
    status: z.number().optional(),
    table: z.number().nullish(),
});

// https://docs.zelty.fr/get-bookings-15359086e0
export const zeltyGetBookingsResponseValidator = z.object({
    bookings: z.array(zeltyBookingValidator),
    errno: z.number(),
});

export type ZeltyGetBookingsResponse = z.infer<typeof zeltyGetBookingsResponseValidator>;

// https://docs.zelty.fr/get-booking-15363586e0
export const zeltyGetBookingByZeltyIdResponseValidator = z.object({
    booking: zeltyBookingValidator,
    errno: z.number(),
});

export type ZeltyGetBookingByZeltyIdResponse = z.infer<typeof zeltyGetBookingByZeltyIdResponseValidator>;

/**
 * Restaurants - https://docs.zelty.fr/get-restaurants-14211259e0
 */

const zeltyClosingPeriodValidator = z.object({
    end: z.number(),
    reason: z.string().optional(),
    start: z.number(),
});

const zeltyTimetableValidator = z.object({
    '1': z.array(z.array(z.number()).nullable()),
    '2': z.array(z.array(z.number())),
    '3': z.array(z.array(z.number())),
    '4': z.array(z.array(z.number())),
    '5': z.array(z.array(z.number())),
    '6': z.array(z.array(z.number())),
    '7': z.array(z.array(z.number())),
});

const zeltyLocationValidator = z.object({
    lat: z.number().optional(),
    lng: z.number().optional(),
});

const zeltyDeliveryZoneValidator = z.object({
    allstreets: z.boolean().optional(),
    center: z.null().optional(),
    city: z.string().optional(),
    closures: z.array(zeltyClosingPeriodValidator),
    delivery_charge: z.number().optional(),
    delivery_time: z.number().optional(),
    disabled: z.boolean().optional(),
    force_web_payment: z.boolean().optional(),
    id: z.number().optional(),
    id_restaurant: z.number().optional(),
    min_price: z.number().optional(),
    name: z.string().optional(),
    polygon: z.array(zeltyLocationValidator).optional(),
    type: z.number().optional(),
    zip_code: z.string().optional(),
});

export const zeltyRestaurantValidator = z.object({
    address: z.string().nullish(),
    closures: z.array(zeltyClosingPeriodValidator).optional(),
    country_code: z.string().optional(),
    currency: z.string().optional(),
    default_lang: z.string().optional(),
    delay: z.number().optional(),
    delivery_charge: z.number().optional(),
    delivery_charge_tva: z.number().optional(),
    delivery_hours: zeltyTimetableValidator.nullish(),
    delivery_minimum: z.number().optional(),
    delivery_no_charge_min: z.number().optional(),
    delivery_time: z.number().optional(),
    delivery_zones: z.array(zeltyDeliveryZoneValidator).optional(),
    description: z.string().nullish(),
    disable: z.boolean().optional(),
    happy_hours: zeltyTimetableValidator.nullish(),
    id: z.number().optional(),
    image: z.string().optional(),
    loc: zeltyLocationValidator.nullish(),
    name: z.string().optional(),
    online_ordering_hidden: z.boolean().optional(),
    opening_hours: zeltyTimetableValidator.nullish(),
    opening_hours_txt: z.string().nullish(),
    phone: z.string().nullish(),
    production_delay: z.number().optional(),
    public_name: z.string().optional(),
    remote_id: z.string().nullish(),
    takeaway_delay: z.number().optional(),
});

export type ZeltyRestaurant = z.infer<typeof zeltyRestaurantValidator>;

// https://docs.zelty.fr/get-restaurants-14211259e0
export const zeltyGetRestaurantsResponseValidator = z.object({
    restaurants: z.array(zeltyRestaurantValidator),
    errno: z.number(),
});

export type ZeltyGetRestaurantsResponse = z.infer<typeof zeltyGetRestaurantsResponseValidator>;
