export enum ZeltyApiErrorCode {
    UNAUTHORIZED = '4030000',
    NOT_FOUND = '4001012',
}

export const ZeltyApiProviderErrorCode = {
    CANNOT_VALIDATE_RESPONSE: 'CANNOT_VALIDATE_RESPONSE',
    DATE_RANGE_TOO_LARGE: 'DATE_RANGE_TOO_LARGE',
    UNAUTHORIZED: 'UNAUTHORIZED',
    NOT_FOUND: 'NOT_FOUND',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export type IZeltyApiProviderErrorCode = (typeof ZeltyApiProviderErrorCode)[keyof typeof ZeltyApiProviderErrorCode];

export interface ZeltyApiProviderErrorObject {
    code: I<PERSON>eltyA<PERSON>ProviderErrorCode;
    stringifiedRawError?: string;
}

export interface ZeltyApiRequestOptions {
    method: 'GET' | 'POST';

    /** The path of the HTTP request without the domain name */
    endpoint: string;

    queryParams: Record<string, string | number>;

    headers?: Record<string, string>;

    body?: any;
}
