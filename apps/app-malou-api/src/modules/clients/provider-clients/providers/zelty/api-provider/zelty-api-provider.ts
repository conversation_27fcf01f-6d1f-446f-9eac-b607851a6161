import axios, { AxiosResponse } from 'axios';
import { DateTime } from 'luxon';
import { Err, err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { ZodType } from 'zod';

import { errorReplacer } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import {
    ZeltyApiProviderErrorCode,
    ZeltyApiProviderErrorObject,
    ZeltyApiRequestOptions,
} from ':modules/clients/provider-clients/providers/zelty/api-provider/zelty-api-provider.definitions';
import {
    ZeltyGetBookingByZeltyIdResponse,
    zeltyGetBookingByZeltyIdResponseValidator,
    ZeltyGetBookingsResponse,
    zeltyGetBookingsResponseValidator,
    ZeltyGetCustomerByZeltyIdResponse,
    zeltyGetCustomerByZeltyIdResponseValidator,
    ZeltyGetCustomersResponse,
    zeltyGetCustomersResponseValidator,
    ZeltyGetOrderByZeltyIdResponse,
    zeltyGetOrderByZeltyIdResponseValidator,
    ZeltyGetOrdersResponse,
    zeltyGetOrdersResponseValidator,
    ZeltyGetRestaurantsResponse,
    zeltyGetRestaurantsResponseValidator,
} from ':modules/clients/provider-clients/providers/zelty/zelty-provider.interfaces';

@singleton()
export class ZeltyApiProvider {
    // https://docs.zelty.fr/get-customers-15325975e0
    async getCustomers({
        offset,
        limit,
    }: {
        offset: number;
        limit: number;
    }): Promise<Result<ZeltyGetCustomersResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';

        return this._callApi({
            responseValidator: zeltyGetCustomersResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/customers`,
                queryParams: { offset, limit },
            },
            token,
        });
    }

    // https://docs.zelty.fr/get-customer-15344313e0
    async getCustomerByZeltyId(zeltyId: string): Promise<Result<ZeltyGetCustomerByZeltyIdResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';
        return this._callApi({
            responseValidator: zeltyGetCustomerByZeltyIdResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/customers/${zeltyId}`,
                queryParams: {},
            },
            token,
        });
    }

    // https://docs.zelty.fr/get-orders-14185593e0
    async getOrders(fromDate: Date, toDate: Date): Promise<Result<ZeltyGetOrdersResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';

        const fromDateTime = DateTime.fromJSDate(fromDate).startOf('day');
        const toDateTime = DateTime.fromJSDate(toDate).endOf('day');
        const diffInDays = toDateTime.diff(fromDateTime, 'days').days;
        if (diffInDays > 31) {
            logger.error('[ZeltyApiProvider.getOrders] Error DATE_RANGE_TOO_LARGE', { fromDate, toDate });
            return err({
                code: ZeltyApiProviderErrorCode.DATE_RANGE_TOO_LARGE,
                stringifiedRawError: `Date range too large: ${diffInDays} days`,
            });
        }

        return this._callApi({
            responseValidator: zeltyGetOrdersResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/orders`,
                queryParams: { from: fromDate.toISOString(), to: toDate.toISOString() },
            },
            token,
        });
    }

    // https://docs.zelty.fr/get-order-14211260e0
    async getOrderByZeltyId(orderId: string): Promise<Result<ZeltyGetOrderByZeltyIdResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';
        return this._callApi({
            responseValidator: zeltyGetOrderByZeltyIdResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/orders/${orderId}`,
                queryParams: {},
            },
            token,
        });
    }

    // https://docs.zelty.fr/get-bookings-15359086e0
    async getBookings(date?: Date): Promise<Result<ZeltyGetBookingsResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';
        return this._callApi({
            responseValidator: zeltyGetBookingsResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/bookings`,
                queryParams: date ? { date: date.toISOString() } : {}, // API default value is today
            },
            token,
        });
    }

    // https://docs.zelty.fr/get-booking-15363586e0
    getBookingByZeltyId(bookingId: string): Promise<Result<ZeltyGetBookingByZeltyIdResponse, ZeltyApiProviderErrorObject>> {
        const token = 'MTU0NjU648i5A+vO8ob+qczDDNnMSha1DL0=';
        return this._callApi({
            responseValidator: zeltyGetBookingByZeltyIdResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/bookings/${bookingId}`,
                queryParams: {},
            },
            token,
        });
    }

    getRestaurants(token: string): Promise<Result<ZeltyGetRestaurantsResponse, ZeltyApiProviderErrorObject>> {
        return this._callApi({
            responseValidator: zeltyGetRestaurantsResponseValidator,
            requestOptions: {
                method: 'GET',
                endpoint: `/restaurants`,
                queryParams: {},
            },
            token,
        });
    }

    // https://docs.zelty.fr/getting-started-855365m0
    private async _callApi<T>(params: {
        responseValidator: ZodType<T>;
        token: string;
        requestOptions: ZeltyApiRequestOptions;
    }): Promise<Result<T, ZeltyApiProviderErrorObject>> {
        logger.info('[ZeltyApiProvider.callApi] Start', {
            requestOptions: params.requestOptions,
            responseValidatorDescription: params.responseValidator.description,
        });
        const requiredHeaders = {
            Authorization: `Bearer ${params.token}`,
        };

        const apiVersion = Config.platforms.zelty.api.version;

        let res: AxiosResponse;
        try {
            res = await axios({
                method: params.requestOptions.method,
                baseURL: `https://api.zelty.fr/${apiVersion}`,
                url: params.requestOptions.endpoint,
                params: { ...params.requestOptions.queryParams },
                headers: params.requestOptions.headers ? { ...params.requestOptions.headers, ...requiredHeaders } : requiredHeaders,
                data: params.requestOptions.body,
            });
        } catch (error: unknown) {
            logger.error('[ZeltyApiProvider.callApi] Error', { error });
            return this._handleError(error);
        }

        logger.info('[ZeltyApiProvider.callApi] OK', { res: this._stringifyResultWithLimit(res.data) });
        const validatedResData = params.responseValidator.safeParse(res.data);
        if (!validatedResData.success) {
            logger.error('[ZeltyApiProvider.callApi] Error CANNOT_VALIDATE_RESPONSE', {
                error: validatedResData.error,
                response: res.data,
            });
            return err({
                code: ZeltyApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE,
                stringifiedRawError: JSON.stringify(validatedResData.error), // Do not use errorReplacer for ZodError
            });
        }
        return ok(validatedResData.data);
    }

    private _handleError(error: unknown): Err<never, ZeltyApiProviderErrorObject> {
        let stringifiedRawError = JSON.stringify(error, errorReplacer);
        if (axios.isAxiosError(error)) {
            stringifiedRawError = JSON.stringify(
                { data: error.response?.data, status: error.response?.status, statusText: error.response?.statusText },
                errorReplacer
            );
            if (error.response?.status === 401) {
                logger.error('[ZeltyApiProvider.callApi] Error UNAUTHORIZED');
                return err({ code: ZeltyApiProviderErrorCode.UNAUTHORIZED, stringifiedRawError });
            }
            if (error.response?.status === 404) {
                logger.error('[ZeltyApiProvider.callApi] Error NOT_FOUND');
                return err({
                    code: ZeltyApiProviderErrorCode.NOT_FOUND,
                    stringifiedRawError,
                });
            }
        }
        logger.error('[ZeltyApiProvider.callApi] Error UNKNOWN_ERROR');
        return err({ code: ZeltyApiProviderErrorCode.UNKNOWN_ERROR, stringifiedRawError });
    }

    private _stringifyResultWithLimit(res: any): string {
        try {
            return JSON.stringify(res).substring(0, 250);
        } catch (error) {
            logger.warn('_stringifyResultWithLimit', error);
            return 'error stringify result';
        }
    }
}
