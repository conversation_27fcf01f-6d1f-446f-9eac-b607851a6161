import { singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { ZeltyApiProvider } from ':modules/clients/provider-clients/providers/zelty/api-provider/zelty-api-provider';
import { ZeltyClient, ZeltyOrder, ZeltyRestaurant } from ':modules/clients/provider-clients/providers/zelty/zelty-provider.interfaces';
import PlatformsRepository from ':modules/platforms/platforms.repository';

@singleton()
export class ZeltyProviderWrapper {
    constructor(
        private readonly _zeltyApiProvider: ZeltyApiProvider,
        private readonly _platformsRespository: PlatformsRepository
    ) {}

    async getZeltyClient(zeltyId: string): Promise<ZeltyClient | null> {
        const result = await this._zeltyApiProvider.getCustomerByZeltyId(zeltyId);
        if (result.isErr()) {
            return null;
        }
        const zeltyClient = result.value.customer;
        return zeltyClient;
    }

    async getZeltyClients(): Promise<ZeltyClient[]> {
        let offset = 0;
        const limit = 100;

        const zeltyClients: ZeltyClient[] = [];
        let hasNextPage = true;
        const maxIterations = 100; // to prevent infinite loop
        let iterations = 0;

        while (hasNextPage && iterations < maxIterations) {
            iterations++;
            const result = await this._zeltyApiProvider.getCustomers({ offset, limit });
            offset += limit;
            if (result.isErr()) {
                return zeltyClients;
            }
            zeltyClients.push(...result.value.customers);
            hasNextPage = result.value.customers.length === limit;
        }
        return zeltyClients;
    }

    async getZeltyOrder(zeltyId: string): Promise<ZeltyOrder | null> {
        const result = await this._zeltyApiProvider.getOrderByZeltyId(zeltyId);
        if (result.isErr()) {
            return null;
        }
        const zeltyOrder = result.value.order;
        return zeltyOrder;
    }

    async getZeltyRestaurants(token: string): Promise<ZeltyRestaurant[]> {
        const result = await this._zeltyApiProvider.getRestaurants(token);
        if (result.isErr()) {
            return [];
        }
        return result.value.restaurants;
    }

    async getRestaurantIdFromZeltyRestaurantId(zeltyRestaurantId: string): Promise<string | null> {
        const platform = await this._platformsRespository.findOne({
            filter: { key: PlatformKey.ZELTY, socialId: zeltyRestaurantId },
            projection: { restaurantId: 1 },
            options: { lean: true },
        });
        return platform ? platform.restaurantId.toString() : null;
    }
}
