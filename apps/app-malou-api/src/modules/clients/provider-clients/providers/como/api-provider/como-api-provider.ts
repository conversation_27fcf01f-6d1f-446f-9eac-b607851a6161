import axios, { AxiosResponse } from 'axios';
import { Err, err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';
import { ZodType } from 'zod';

import { errorReplacer } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import {
    ComoApiErrorCode,
    comoApiErrorResponseValidator,
    ComoApiProviderErrorCode,
    ComoApiProviderErrorObject,
    ComoApiRequestOptions,
} from ':modules/clients/provider-clients/providers/como/api-provider/como-api-provider.definitions';
import { mapRestaurantIdToComoToken } from ':modules/clients/provider-clients/providers/como/como-api-token-mapper';
import {
    ComoClient,
    ComoCreateClientResponse,
    comoCreateClientResponseValidator,
    ComoGetClientResponse,
    comoGetClientResponseValidator,
    ComoSendClientWonGiftEventResponse,
    comoSendClientWonGiftEventResponseValidator,
} from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';

@singleton()
export class ComoApiProvider {
    // https://docs.comosense.com/d/docs/advanced-apis/registration/registration-quick-call
    async createClient(
        restaurantId: string,
        client: Partial<ComoClient>
    ): Promise<Result<ComoCreateClientResponse, ComoApiProviderErrorObject>> {
        const tokenResult = this._getToken(restaurantId);
        if (tokenResult.isErr()) {
            return err(tokenResult.error);
        }

        return this._callApi({
            responseValidator: comoCreateClientResponseValidator,
            token: tokenResult.value,
            requestOptions: {
                method: 'POST',
                endpoint: '/v4/advanced/registration/quick',
                queryParams: {},
                body: { customer: client },
            },
        });
    }

    // https://docs.comosense.com/d/docs/api-calls-specification/getmemberdetails-call
    async getClient(
        restaurantId: string,
        data: { comoMemberId?: string; email?: string; phoneNumber?: string }
    ): Promise<Result<ComoGetClientResponse, ComoApiProviderErrorObject>> {
        const tokenResult = this._getToken(restaurantId);
        if (tokenResult.isErr()) {
            return err(tokenResult.error);
        }

        return this._callApi({
            responseValidator: comoGetClientResponseValidator,
            token: tokenResult.value,
            requestOptions: {
                method: 'POST',
                endpoint: '/v4/getMemberDetails',
                queryParams: { returnAssets: 'all' },
                body: { customer: data },
            },
        });
    }

    // https://docs.comosense.com/d/docs/advanced-apis/external-events/submit-event-call
    async submitEvent(data: {
        restaurantId: string;
        comoMemberId: string;
        eventName: string;
        date: Date;
    }): Promise<Result<ComoSendClientWonGiftEventResponse, ComoApiProviderErrorObject>> {
        const tokenResult = this._getToken(data.restaurantId);
        if (tokenResult.isErr()) {
            return err(tokenResult.error);
        }

        return this._callApi({
            responseValidator: comoSendClientWonGiftEventResponseValidator,
            token: tokenResult.value,
            requestOptions: {
                method: 'POST',
                endpoint: '/v4/advanced/submitEvent',
                queryParams: {},
                body: {
                    customers: [{ comoMemberId: data.comoMemberId }],
                    event: {
                        type: data.eventName,
                        time: data.date.toISOString(),
                    },
                },
            },
        });
    }

    private _getToken(restaurantId: string): Result<string, ComoApiProviderErrorObject> {
        const token = mapRestaurantIdToComoToken(restaurantId);
        if (token) {
            return ok(token);
        }
        logger.error('[ComoApiProvider._getToken] No token found for restaurant', { restaurantId });
        return err({ code: ComoApiProviderErrorCode.UNAUTHORIZED });
    }

    private async _callApi<T>(params: {
        responseValidator: ZodType<T>;
        token: string;
        requestOptions: ComoApiRequestOptions;
    }): Promise<Result<T, ComoApiProviderErrorObject>> {
        logger.info('[ComoApiProvider.callApi] Start', {
            requestOptions: params.requestOptions,
            responseValidatorDescription: params.responseValidator.description,
        });
        const requiredHeaders = {
            'X-Api-Key': params.token,
            'X-Branch-Id': '1',
            'X-Pos-Id': '3',
            'X-Source-Type': 'Website',
            'X-Source-Name': 'malou-inte',
            'X-Source-Version': '1.2.3',
        };

        let res: AxiosResponse;
        try {
            res = await axios({
                method: params.requestOptions.method,
                baseURL: 'https://api.prod.bcomo.com/api',
                url: params.requestOptions.endpoint,
                params: { ...params.requestOptions.queryParams },
                headers: params.requestOptions.headers ? { ...params.requestOptions.headers, ...requiredHeaders } : requiredHeaders,
                data: params.requestOptions.body,
            });
        } catch (error: unknown) {
            logger.error('[ComoApiProvider.callApi] Error', { error });
            return this._handleError(error);
        }

        logger.info('[ComoApiProvider.callApi] OK', { res: this._stringifyResultWithLimit(res.data) });
        const validatedResData = params.responseValidator.safeParse(res.data);
        if (!validatedResData.success) {
            logger.error('[ComoApiProvider.callApi] Error CANNOT_VALIDATE_RESPONSE', {
                error: validatedResData.error,
                response: res.data,
            });
            return err({
                code: ComoApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE,
                stringifiedRawError: JSON.stringify(validatedResData.error), // Do not use errorReplacer for ZodError
            });
        }
        return ok(validatedResData.data);
    }

    private _handleError(error: unknown): Err<never, ComoApiProviderErrorObject> {
        let stringifiedRawError = JSON.stringify(error, errorReplacer);
        if (axios.isAxiosError(error)) {
            stringifiedRawError = JSON.stringify(
                { data: error.response?.data, status: error.response?.status, statusText: error.response?.statusText },
                errorReplacer
            );
            const errorResponse = comoApiErrorResponseValidator.safeParse(error.response?.data);
            if (!errorResponse.success) {
                logger.error('[ComoApiProvider.callApi] Error CANNOT_VALIDATE_ERROR_RESPONSE', {
                    stringifiedRawError,
                    error: errorResponse.error,
                });
                return err({
                    code: ComoApiProviderErrorCode.CANNOT_VALIDATE_ERROR_RESPONSE,
                    stringifiedRawError: JSON.stringify(errorResponse.error), // Do not use errorReplacer for ZodError,
                });
            }
            const code = errorResponse.data.errors[0].code;
            if (code === ComoApiErrorCode.UNAUTHORIZED) {
                logger.error('[ComoApiProvider.callApi] Error UNAUTHORIZED');
                return err({ code: ComoApiProviderErrorCode.UNAUTHORIZED, stringifiedRawError });
            }
            if (code === ComoApiErrorCode.NOT_FOUND) {
                logger.error('[ComoApiProvider.callApi] Error CUSTOMER_NOT_FOUND');
                return err({
                    code: ComoApiProviderErrorCode.CUSTOMER_NOT_FOUND,
                    stringifiedRawError,
                });
            }
        }
        logger.error('[ComoApiProvider.callApi] Error UNKNOWN_ERROR');
        return err({ code: ComoApiProviderErrorCode.UNKNOWN_ERROR, stringifiedRawError });
    }

    private _stringifyResultWithLimit(res: any): string {
        try {
            return JSON.stringify(res).substring(0, 250);
        } catch (error) {
            logger.warn('_stringifyResultWithLimit', error);
            return 'error stringify result';
        }
    }
}
