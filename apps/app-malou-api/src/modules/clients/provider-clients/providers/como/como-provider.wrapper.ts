import { singleton } from 'tsyringe';

import { ComoApiProvider } from ':modules/clients/provider-clients/providers/como/api-provider/como-api-provider';
import { ComoClient } from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';

@singleton()
export class ComoProviderWrapper {
    constructor(private readonly _comoApiProvider: ComoApiProvider) {}

    async getComoClient(
        data: { comoMemberId?: string; email?: string; phoneNumber?: string },
        restaurantId: string
    ): Promise<ComoClient | null> {
        const result = await this._comoApiProvider.getClient(restaurantId, data);
        if (result.isErr()) {
            return null;
        }
        const comoClient = result.value.membership;
        return comoClient;
    }

    async submitEvent(data: { restaurantId: string; comoMemberId: string; eventName: string; date: Date }): Promise<void> {
        const result = await this._comoApiProvider.submitEvent(data);
        if (result.isErr()) {
            throw new Error(result.error.code);
        }
    }

    async createComoClient(restaurantId: string, client: Partial<ComoClient>): Promise<ComoClient | null> {
        const result = await this._comoApiProvider.createClient(restaurantId, client);
        if (result.isErr()) {
            return null;
        }
        const comoClient = result.value.membership;
        return comoClient;
    }
}
