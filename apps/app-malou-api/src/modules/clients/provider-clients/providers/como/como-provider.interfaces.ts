import z from 'zod';

const comoBalanceValidator = z.object({
    usedByPayment: z.boolean(),
    balance: z.object({
        monetary: z.number(),
        nonMonetary: z.number(),
    }),
});

export enum ComoConsent {
    NO = 'no',
    YES = 'yes',
    TEMPORARY = 'temporary',
}

const comoClientValidator = z.object({
    comoMemberId: z.string(),
    email: z.string().email().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    phoneNumber: z.string().optional(),
    status: z.string().optional(), // received "Inactive" for example
    commonExtId: z.string().optional(),
    createdOn: z.string().datetime().optional(),
    pointsBalance: comoBalanceValidator.optional(),
    creditBalance: comoBalanceValidator.optional(),
    genericWallet1Balance: comoBalanceValidator.optional(),
    genericWallet2Balance: comoBalanceValidator.optional(),
    genericWallet3Balance: comoBalanceValidator.optional(),
    consent: z.nativeEnum(ComoConsent),
    allowEmail: z.boolean().optional(),
    allowSms: z.boolean().optional(),
});

export type ComoClient = z.infer<typeof comoClientValidator>;

export const comoCreateClientResponseValidator = z.object({
    status: z.literal('ok'),
    membership: comoClientValidator,
});

export type ComoCreateClientResponse = z.infer<typeof comoCreateClientResponseValidator>;

export const comoGetClientResponseValidator = z.object({
    status: z.literal('ok'),
    memberNotes: z
        .array(
            z.object({
                content: z.any(),
                type: z.string(),
            })
        )
        .optional(),
    membership: comoClientValidator,
});

export type ComoGetClientResponse = z.infer<typeof comoGetClientResponseValidator>;

export const comoSendClientWonGiftEventResponseValidator = z.object({
    status: z.literal('ok'),
});

export type ComoSendClientWonGiftEventResponse = z.infer<typeof comoSendClientWonGiftEventResponseValidator>;
