import { z } from 'zod';

export enum ComoApiErrorCode {
    UNAUTHORIZED = '4030000',
    NOT_FOUND = '4001012',
}

export const ComoApiProviderErrorCode = {
    CANNOT_VALIDATE_ERROR_RESPONSE: 'CANNOT_VALIDATE_ERROR_RESPONSE',
    CANNOT_VALIDATE_RESPONSE: 'CANNOT_VALIDATE_RESPONSE',
    UNAUTHORIZED: 'UNAUTHORIZED',
    CUSTOMER_NOT_FOUND: 'CUSTOMER_NOT_FOUND',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export type IComoApiProviderErrorCode = (typeof ComoApiProviderErrorCode)[keyof typeof ComoApiProviderErrorCode];

export interface ComoApiProviderErrorObject {
    code: IComoApiProviderErrorCode;
    stringifiedRawError?: string;
}

export interface ComoApiRequestOptions {
    method: 'GET' | 'POST';

    /** The path of the HTTP request without the domain name */
    endpoint: string;

    queryParams: Record<string, string | number>;

    headers?: Record<string, string>;

    body?: any;
}

const comoApiUnauthorizedErrorValidator = z.object({
    code: z.union([z.literal(ComoApiErrorCode.UNAUTHORIZED), z.string()]), // received "4010000", not sure if this is the only value for this error
    message: z.string(), // "Forbidden"
});

const comoApiCustomerNotFoundErrorValidator = z.object({
    code: z.union([z.literal(ComoApiErrorCode.NOT_FOUND), z.string()]), // received "4001012", not sure if this is the only value for this error
    message: z.string(),
    cause: z.array(
        z.object({
            code: z.union([z.literal('4220000'), z.string()]), // received "4220000", not sure if this is the only value for this error
            message: z.string(),
            customerIndex: z.number(),
        })
    ),
});

export const comoApiErrorResponseValidator = z.object({
    status: z.literal('error'),
    errors: z.array(z.union([comoApiUnauthorizedErrorValidator, comoApiCustomerNotFoundErrorValidator])),
});
