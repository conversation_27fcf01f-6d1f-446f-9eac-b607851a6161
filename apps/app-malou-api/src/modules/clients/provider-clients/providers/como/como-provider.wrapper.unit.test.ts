import { err, ok } from 'neverthrow';
import { container } from 'tsyringe';

import { ComoApiProvider } from ':modules/clients/provider-clients/providers/como/api-provider/como-api-provider';
import {
    ComoApiProviderErrorCode,
    ComoApiProviderErrorObject,
} from ':modules/clients/provider-clients/providers/como/api-provider/como-api-provider.definitions';
import {
    ComoClient,
    ComoCreateClientResponse,
    ComoGetClientResponse,
    ComoSendClientWonGiftEventResponse,
} from ':modules/clients/provider-clients/providers/como/como-provider.interfaces';
import { ComoProviderWrapper } from ':modules/clients/provider-clients/providers/como/como-provider.wrapper';

describe('ComoProviderWrapper', () => {
    let comoApiProviderMock: jest.Mocked<ComoApiProvider>;

    beforeEach(() => {
        container.clearInstances();

        // Mock <PERSON>pi<PERSON>rovider
        comoApiProviderMock = {
            createClient: jest.fn(),
            getClient: jest.fn(),
            submitEvent: jest.fn(),
        } as unknown as jest.Mocked<ComoApiProvider>;

        // Clear all mock calls
        jest.clearAllMocks();

        container.registerInstance(ComoApiProvider, comoApiProviderMock);
    });

    const createMockComoClient = (overrides: Partial<ComoClient> = {}): ComoClient => ({
        comoMemberId: 'test-como-member-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+33123456789',
        status: 'Active',
        commonExtId: 'common-ext-id',
        createdOn: '2023-01-01T00:00:00Z',
        pointsBalance: {
            usedByPayment: false,
            balance: {
                monetary: 100,
                nonMonetary: 50,
            },
        },
        creditBalance: {
            usedByPayment: true,
            balance: {
                monetary: 200,
                nonMonetary: 0,
            },
        },
        genericWallet1Balance: undefined,
        genericWallet2Balance: undefined,
        genericWallet3Balance: undefined,
        consent: 'yes' as any,
        ...overrides,
    });

    const createMockGetClientResponse = (comoClient: ComoClient): ComoGetClientResponse => ({
        status: 'ok',
        memberNotes: [],
        membership: comoClient,
    });

    const createMockCreateClientResponse = (comoClient: ComoClient): ComoCreateClientResponse => ({
        status: 'ok',
        membership: comoClient,
    });

    const createMockSendGiftEventResponse = (): ComoSendClientWonGiftEventResponse => ({
        status: 'ok',
    });

    const createMockError = (code: string = ComoApiProviderErrorCode.UNKNOWN_ERROR): ComoApiProviderErrorObject => ({
        code: code as any,
        stringifiedRawError: 'Mock error details',
    });

    describe('getComoClient', () => {
        it('should return client when API call succeeds with comoMemberId', async () => {
            const mockClient = createMockComoClient();
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'test-client-id' }, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { comoMemberId: 'test-client-id' });
        });

        it('should return client when API call succeeds with email', async () => {
            const mockClient = createMockComoClient({ email: '<EMAIL>' });
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ email: '<EMAIL>' }, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { email: '<EMAIL>' });
        });

        it('should return client when API call succeeds with phoneNumber', async () => {
            const mockClient = createMockComoClient({ phoneNumber: '+33123456789' });
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ phoneNumber: '+33123456789' }, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { phoneNumber: '+33123456789' });
        });

        it('should return client when API call succeeds with multiple search criteria', async () => {
            const mockClient = createMockComoClient({
                email: '<EMAIL>',
                phoneNumber: '+33123456789',
            });
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient(
                {
                    comoMemberId: 'test-client-id',
                    email: '<EMAIL>',
                    phoneNumber: '+33123456789',
                },
                'test-restaurant-id'
            );

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', {
                comoMemberId: 'test-client-id',
                email: '<EMAIL>',
                phoneNumber: '+33123456789',
            });
        });

        it('should return null when API call fails with CUSTOMER_NOT_FOUND', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.CUSTOMER_NOT_FOUND);
            comoApiProviderMock.getClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'non-existent-client' }, 'test-restaurant-id');

            expect(result).toBeNull();
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { comoMemberId: 'non-existent-client' });
        });

        it('should return null when API call fails with UNAUTHORIZED', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.UNAUTHORIZED);
            comoApiProviderMock.getClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ email: '<EMAIL>' }, 'test-restaurant-id');

            expect(result).toBeNull();
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { email: '<EMAIL>' });
        });

        it('should return null when API call fails with UNKNOWN_ERROR', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.UNKNOWN_ERROR);
            comoApiProviderMock.getClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ phoneNumber: '+33123456789' }, 'test-restaurant-id');

            expect(result).toBeNull();
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { phoneNumber: '+33123456789' });
        });

        it('should return null when API call fails with CANNOT_VALIDATE_RESPONSE', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE);
            comoApiProviderMock.getClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'test-client-id' }, 'test-restaurant-id');

            expect(result).toBeNull();
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { comoMemberId: 'test-client-id' });
        });

        it('should return null when API call fails with CANNOT_VALIDATE_ERROR_RESPONSE', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.CANNOT_VALIDATE_ERROR_RESPONSE);
            comoApiProviderMock.getClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ email: '<EMAIL>' }, 'test-restaurant-id');

            expect(result).toBeNull();
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', { email: '<EMAIL>' });
        });

        it('should handle client with null/undefined fields correctly', async () => {
            const mockClient = createMockComoClient({
                firstName: undefined,
                lastName: undefined,
                email: undefined,
                phoneNumber: undefined,
                status: undefined,
                commonExtId: undefined,
                createdOn: undefined,
                pointsBalance: undefined,
                creditBalance: undefined,
            });
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'test-client-id' }, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(result?.firstName).toBeUndefined();
            expect(result?.lastName).toBeUndefined();
            expect(result?.email).toBeUndefined();
            expect(result?.phoneNumber).toBeUndefined();
        });

        it('should handle client with complete data correctly', async () => {
            const mockClient = createMockComoClient({
                comoMemberId: 'complete-member-id',
                firstName: 'Alice',
                lastName: 'Johnson',
                email: '<EMAIL>',
                phoneNumber: '+33987654321',
                status: 'Active',
                commonExtId: 'alice-ext-id',
                createdOn: '2023-05-15T10:30:00Z',
                pointsBalance: {
                    usedByPayment: true,
                    balance: {
                        monetary: 500,
                        nonMonetary: 250,
                    },
                },
                creditBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 1000,
                        nonMonetary: 0,
                    },
                },
                consent: 'yes' as any,
            });
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'complete-member-id' }, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(result?.firstName).toBe('Alice');
            expect(result?.lastName).toBe('Johnson');
            expect(result?.email).toBe('<EMAIL>');
            expect(result?.phoneNumber).toBe('+33987654321');
            expect(result?.status).toBe('Active');
        });

        it('should handle different restaurant IDs correctly', async () => {
            const mockClient = createMockComoClient();
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({ comoMemberId: 'test-client-id' }, 'different-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('different-restaurant-id', { comoMemberId: 'test-client-id' });
        });

        it('should handle empty search criteria correctly', async () => {
            const mockClient = createMockComoClient();
            const mockResponse = createMockGetClientResponse(mockClient);
            comoApiProviderMock.getClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.getComoClient({}, 'test-restaurant-id');

            expect(result).toEqual(mockClient);
            expect(comoApiProviderMock.getClient).toHaveBeenCalledWith('test-restaurant-id', {});
        });
    });

    describe('submitEvent', () => {
        it('should successfully send gift event when API call succeeds', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Free Dessert',
                date: new Date('2023-12-25T19:00:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should throw error when API call fails with UNAUTHORIZED', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.UNAUTHORIZED);
            comoApiProviderMock.submitEvent.mockResolvedValue(err(mockError));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Free Dessert',
                date: new Date('2023-12-25T19:00:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);

            await expect(comoProviderWrapper.submitEvent(eventData)).rejects.toThrow('UNAUTHORIZED');
            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should throw error when API call fails with UNKNOWN_ERROR', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.UNKNOWN_ERROR);
            comoApiProviderMock.submitEvent.mockResolvedValue(err(mockError));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Free Appetizer',
                date: new Date('2023-11-15T18:30:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);

            await expect(comoProviderWrapper.submitEvent(eventData)).rejects.toThrow('UNKNOWN_ERROR');
            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should throw error when API call fails with CANNOT_VALIDATE_RESPONSE', async () => {
            const mockError = createMockError(ComoApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE);
            comoApiProviderMock.submitEvent.mockResolvedValue(err(mockError));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Free Drink',
                date: new Date('2023-10-01T12:00:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);

            await expect(comoProviderWrapper.submitEvent(eventData)).rejects.toThrow('CANNOT_VALIDATE_RESPONSE');
            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should handle different gift names correctly', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Special Birthday Discount',
                date: new Date('2023-08-20T14:30:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should handle different client IDs correctly', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'different-restaurant-id',
                comoMemberId: 'different-client-id',
                eventName: 'Loyalty Reward',
                date: new Date('2023-07-10T16:45:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should handle past dates correctly', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Retroactive Gift',
                date: new Date('2022-12-31T23:59:59Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should handle future dates correctly', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Future Gift',
                date: new Date('2024-06-15T10:00:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });

        it('should handle long gift names correctly', async () => {
            const mockResponse = createMockSendGiftEventResponse();
            comoApiProviderMock.submitEvent.mockResolvedValue(ok(mockResponse));

            const eventData = {
                restaurantId: 'test-restaurant-id',
                comoMemberId: 'test-client-id',
                eventName: 'Very Long Gift Name With Many Characters That Describes A Special Promotional Offer For Loyal Customers',
                date: new Date('2023-09-05T13:15:00Z'),
            };

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            await comoProviderWrapper.submitEvent(eventData);

            expect(comoApiProviderMock.submitEvent).toHaveBeenCalledWith(eventData);
        });
    });

    describe('createComoClient', () => {
        it('should return client when API call succeeds with complete client data', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'New',
                lastName: 'User',
                phoneNumber: '+33123456789',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'new-como-member-id',
                email: '<EMAIL>',
                firstName: 'New',
                lastName: 'User',
                phoneNumber: '+33123456789',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return client when API call succeeds with minimal client data', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'minimal-como-member-id',
                email: '<EMAIL>',
                firstName: undefined,
                lastName: undefined,
                phoneNumber: undefined,
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return client when API call succeeds with only name data', async () => {
            const clientData: Partial<ComoClient> = {
                firstName: 'John',
                lastName: 'Doe',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'name-only-como-member-id',
                firstName: 'John',
                lastName: 'Doe',
                email: undefined,
                phoneNumber: undefined,
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return client when API call succeeds with only phone data', async () => {
            const clientData: Partial<ComoClient> = {
                phoneNumber: '+33987654321',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'phone-only-como-member-id',
                phoneNumber: '+33987654321',
                email: undefined,
                firstName: undefined,
                lastName: undefined,
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return null when API call fails with UNAUTHORIZED', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
            };

            const mockError = createMockError(ComoApiProviderErrorCode.UNAUTHORIZED);
            comoApiProviderMock.createClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toBeNull();
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return null when API call fails with UNKNOWN_ERROR', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                phoneNumber: '+33123456789',
            };

            const mockError = createMockError(ComoApiProviderErrorCode.UNKNOWN_ERROR);
            comoApiProviderMock.createClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toBeNull();
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return null when API call fails with CANNOT_VALIDATE_RESPONSE', async () => {
            const clientData: Partial<ComoClient> = {
                firstName: 'Invalid',
                lastName: 'Response',
                email: '<EMAIL>',
            };

            const mockError = createMockError(ComoApiProviderErrorCode.CANNOT_VALIDATE_RESPONSE);
            comoApiProviderMock.createClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toBeNull();
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should return null when API call fails with CANNOT_VALIDATE_ERROR_RESPONSE', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
            };

            const mockError = createMockError(ComoApiProviderErrorCode.CANNOT_VALIDATE_ERROR_RESPONSE);
            comoApiProviderMock.createClient.mockResolvedValue(err(mockError));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toBeNull();
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle client creation with special characters in email correctly', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'Special',
                lastName: 'Email',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'special-email-como-member-id',
                email: '<EMAIL>',
                firstName: 'Special',
                lastName: 'Email',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(result?.email).toBe('<EMAIL>');
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle client creation with international phone numbers correctly', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'International',
                lastName: 'User',
                phoneNumber: '+447123456789',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'international-como-member-id',
                email: '<EMAIL>',
                firstName: 'International',
                lastName: 'User',
                phoneNumber: '+447123456789',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(result?.phoneNumber).toBe('+447123456789');
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle different restaurant IDs correctly', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'Different',
                lastName: 'Restaurant',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'different-restaurant-como-member-id',
                email: '<EMAIL>',
                firstName: 'Different',
                lastName: 'Restaurant',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('different-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('different-restaurant-id', clientData);
        });

        it('should handle empty client data correctly', async () => {
            const clientData: Partial<ComoClient> = {};

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'empty-data-como-member-id',
                email: undefined,
                firstName: undefined,
                lastName: undefined,
                phoneNumber: undefined,
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle client creation with long names correctly', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'VeryLongFirstNameWithManyCharactersThatExceedsNormalLength',
                lastName: 'VeryLongLastNameWithManyCharactersThatExceedsNormalLength',
                phoneNumber: '+33123456789',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'long-names-como-member-id',
                email: '<EMAIL>',
                firstName: 'VeryLongFirstNameWithManyCharactersThatExceedsNormalLength',
                lastName: 'VeryLongLastNameWithManyCharactersThatExceedsNormalLength',
                phoneNumber: '+33123456789',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(result?.firstName).toBe('VeryLongFirstNameWithManyCharactersThatExceedsNormalLength');
            expect(result?.lastName).toBe('VeryLongLastNameWithManyCharactersThatExceedsNormalLength');
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle client creation when Como returns different data than input', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'Input',
                lastName: 'Name',
                phoneNumber: '+33111111111',
            };

            // Como service returns different data than input (simulating Como's processing)
            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'processed-como-member-id',
                email: '<EMAIL>',
                firstName: 'Processed',
                lastName: 'Name',
                phoneNumber: '+33111111111',
                status: 'Active',
                commonExtId: 'processed-ext-id',
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            // Result should reflect what Como actually returned
            expect(result?.email).toBe('<EMAIL>');
            expect(result?.firstName).toBe('Processed');
            expect(result?.status).toBe('Active');
            expect(result?.commonExtId).toBe('processed-ext-id');
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });

        it('should handle client creation with complete Como response data', async () => {
            const clientData: Partial<ComoClient> = {
                email: '<EMAIL>',
                firstName: 'Complete',
                lastName: 'User',
                phoneNumber: '+33555666777',
            };

            const mockCreatedClient = createMockComoClient({
                comoMemberId: 'complete-response-como-member-id',
                email: '<EMAIL>',
                firstName: 'Complete',
                lastName: 'User',
                phoneNumber: '+33555666777',
                status: 'Active',
                commonExtId: 'complete-ext-id',
                createdOn: '2023-12-01T10:30:00Z',
                pointsBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 0,
                        nonMonetary: 0,
                    },
                },
                creditBalance: {
                    usedByPayment: false,
                    balance: {
                        monetary: 0,
                        nonMonetary: 0,
                    },
                },
                consent: 'yes' as any,
            });

            const mockResponse = createMockCreateClientResponse(mockCreatedClient);
            comoApiProviderMock.createClient.mockResolvedValue(ok(mockResponse));

            const comoProviderWrapper = container.resolve(ComoProviderWrapper);
            const result = await comoProviderWrapper.createComoClient('test-restaurant-id', clientData);

            expect(result).toEqual(mockCreatedClient);
            expect(result?.comoMemberId).toBe('complete-response-como-member-id');
            expect(result?.status).toBe('Active');
            expect(result?.commonExtId).toBe('complete-ext-id');
            expect(result?.createdOn).toBe('2023-12-01T10:30:00Z');
            expect(result?.pointsBalance?.balance.monetary).toBe(0);
            expect(result?.creditBalance?.balance.monetary).toBe(0);
            expect(result?.consent).toBe('yes');
            expect(comoApiProviderMock.createClient).toHaveBeenCalledWith('test-restaurant-id', clientData);
        });
    });
});
