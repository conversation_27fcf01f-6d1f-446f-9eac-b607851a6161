import { container } from 'tsyringe';

import { DbId, IRestaurant, IReview, newDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    Civility,
    DEFAULT_LANG_UNKNOWN,
    KEYWORD_SCORE_METHOD,
    KeywordScoreTextType,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { KeywordsScoreUseCases } from ':modules/keywords/modules/score/keywords-score.use-cases';
import { getDefaultRestaurantAiSettings } from ':modules/restaurant-ai-settings/tests/restaurant-ai-settings.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';

let generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService;
let keywordsScoreUseCases: KeywordsScoreUseCases;
const defaultKeywordAnalysis = {
    keywords: [],
    score: 1.5,
    count: 0,
};
describe('GenerateKeywordAnalysisForCommentService', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository', 'RestaurantAiSettingsRepository']);

        keywordsScoreUseCases = {} as KeywordsScoreUseCases;
        keywordsScoreUseCases.buildBricksFromKeywords = jest.fn().mockReturnValue([]);
        keywordsScoreUseCases.generateKeywordAnalysis = jest.fn().mockResolvedValue(defaultKeywordAnalysis);
        container.registerInstance(KeywordsScoreUseCases, keywordsScoreUseCases);

        generateKeywordAnalysisForCommentService = container.resolve(GenerateKeywordAnalysisForCommentService);
    });

    describe('execute', () => {
        it('should throw if no restaurant found', async () => {
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(restaurantId).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.RESTAURANT_NOT_FOUND,
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                generateKeywordAnalysisForCommentService.execute({
                    reviewId,
                    rating: 5,
                    restaurantId,
                    text: 'Comment text',
                    reviewLang: ApplicationLanguage.FR,
                    reviewSocialCreatedAt: new Date(),
                    reviewerName: 'Reviewer',
                    reviewerNameValidation: DEFAULT_REVIEWER_NAME_VALIDATION,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should generate correct payload for keyword analysis with bricks & restaurant name', async () => {
            const keywordsScoreUsesCasesSpy = jest.spyOn(keywordsScoreUseCases, 'generateKeywordAnalysis');
            const settingsRestaurantName = 'Custom Restaurant Name';
            const myReviewText = 'My review text';
            const myReviewerName = 'My reviewer name';
            const myCommentText = 'Comment text';

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'restaurantAiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantAiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .restaurantName(settingsRestaurantName)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text(myReviewText)
                                    .reviewer({
                                        displayName: myReviewerName,
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        bricks: [settingsRestaurantName],
                        keywordScoreMethod: KEYWORD_SCORE_METHOD,
                        language: dependencies.reviews[0].lang,
                        reviewerName: dependencies.reviews[0].reviewer!.displayName,
                        reviewerNameValidation: {
                            gender: Civility.OTHER,
                            firstName: 'UNKNOWN',
                            isFirstNameValid: false,
                            lastName: 'UNKNOWN',
                            isLastNameValid: false,
                        },
                        text: myCommentText,
                        textType: KeywordScoreTextType.HIGH_RATE_REVIEW,
                        venueName: settingsRestaurantName,
                        responseTime: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurant = seededObjects.restaurants[0] as IRestaurant;
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();
            const review = seededObjects.reviews[0] as IReview;
            const expectedResult = testCase.getExpectedResult();

            const result = await generateKeywordAnalysisForCommentService.execute({
                reviewId,
                rating: 5,
                restaurant,
                text: myCommentText,
                reviewLang: review.lang ?? undefined,
                reviewSocialCreatedAt: new Date(),
                reviewerName: review.reviewer!.displayName,
                reviewerNameValidation: DEFAULT_REVIEWER_NAME_VALIDATION,
            });

            expect(keywordsScoreUsesCasesSpy).toHaveBeenCalledWith(expectedResult);
            expect(result).toEqual(defaultKeywordAnalysis);
        });

        it('should use default lang if no lang is set for review', async () => {
            const keywordsScoreUsesCasesSpy = jest.spyOn(keywordsScoreUseCases, 'generateKeywordAnalysis');
            const settingsRestaurantName = 'Custom Restaurant Name';
            const myReviewText = 'My review text';
            const myReviewerName = 'My reviewer name';
            const myCommentText = 'Comment text';

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'restaurantAiSettings'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    restaurantAiSettings: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAiSettings()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .restaurantName(settingsRestaurantName)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .text(myReviewText)
                                    .lang(null)
                                    .reviewer({
                                        displayName: myReviewerName,
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        bricks: [settingsRestaurantName],
                        keywordScoreMethod: KEYWORD_SCORE_METHOD,
                        language: DEFAULT_LANG_UNKNOWN,
                        reviewerName: dependencies.reviews[0].reviewer!.displayName,
                        reviewerNameValidation: {
                            gender: Civility.OTHER,
                            firstName: 'UNKNOWN',
                            isFirstNameValid: false,
                            lastName: 'UNKNOWN',
                            isLastNameValid: false,
                        },
                        text: myCommentText,
                        textType: KeywordScoreTextType.HIGH_RATE_REVIEW,
                        venueName: settingsRestaurantName,
                        responseTime: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurant = seededObjects.restaurants[0] as IRestaurant;
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();
            const review = seededObjects.reviews[0] as IReview;
            const expectedResult = testCase.getExpectedResult();

            await generateKeywordAnalysisForCommentService.execute({
                reviewId,
                rating: 5,
                restaurant,
                text: myCommentText,
                reviewLang: review.lang ?? undefined,
                reviewSocialCreatedAt: new Date(),
                reviewerName: review.reviewer!.displayName,
                reviewerNameValidation: DEFAULT_REVIEWER_NAME_VALIDATION,
            });

            expect(keywordsScoreUsesCasesSpy).toHaveBeenCalledWith(expectedResult);
        });
    });
});
