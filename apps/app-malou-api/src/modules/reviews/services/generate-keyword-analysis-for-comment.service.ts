import { singleton } from 'tsyringe';

import { DbId, IKeywordAnalysis, IRestaurant } from '@malou-io/package-models';
import {
    DEFAULT_LANG_UNKNOWN,
    getTimeDifferenceInHours,
    KEYWORD_SCORE_METHOD,
    KeywordScoreTextType,
    MalouErrorCode,
    mapLanguageStringToApplicationLanguage,
    MIN_POSITIVE_REVIEW_RATING,
    removeDiacritics,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { AiReviewerNameValidation, DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { Breakdown } from ':modules/keywords/entities/breakdown.entity';
import { KeywordsScoreUseCases } from ':modules/keywords/modules/score/keywords-score.use-cases';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { RestaurantKeyword } from ':modules/restaurant-keywords/entities/restaurant-keywords.entity';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

export type BuildKeywordAnalysisForCommentServiceProps = {
    reviewId: string;
    restaurant?: Pick<IRestaurant, '_id' | 'name'>;
    restaurantId?: DbId;
    rating: number | null;
    text: string;
    reviewLang?: string;
    reviewSocialCreatedAt: Date;
    reviewerName: string;
    commentSocialUpdatedAt?: Date;
    reviewerNameValidation?: AiReviewerNameValidation;
};

@singleton()
export class GenerateKeywordAnalysisForCommentService {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _keywordsScoreUseCases: KeywordsScoreUseCases
    ) {}

    async execute({
        restaurant,
        rating,
        restaurantId,
        text,
        reviewLang,
        reviewSocialCreatedAt,
        reviewerName,
        commentSocialUpdatedAt,
        reviewerNameValidation,
    }: BuildKeywordAnalysisForCommentServiceProps): Promise<IKeywordAnalysis> {
        const type =
            (rating ?? 0) < MIN_POSITIVE_REVIEW_RATING ? KeywordScoreTextType.LOW_RATE_REVIEW : KeywordScoreTextType.HIGH_RATE_REVIEW; // rating ?? 0, we consider that if no rating, it's a negative review. (It's not the case for foursquare though)
        let foundRestaurant: any = restaurant;
        if (!foundRestaurant) {
            foundRestaurant = await this._restaurantsRepository.findOne({
                filter: { _id: restaurantId },
                projection: {
                    name: 1,
                },
                options: {
                    lean: true,
                },
            });
        }

        if (!foundRestaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'Restaurant not found',
                metadata: { restaurantId },
            });
        }

        const { restaurantName, restaurantKeywords } = await this._getRestaurantNameAndKeywords(foundRestaurant);

        const bricks = this._keywordsScoreUseCases.buildBricksFromKeywords(restaurantKeywords);

        if (restaurantName) {
            bricks.unshift(
                new Breakdown({
                    text: restaurantName,
                    category: 'restaurantName',
                })
            );
        }

        const applicationLanguage = mapLanguageStringToApplicationLanguage(reviewLang);

        return this._keywordsScoreUseCases.generateKeywordAnalysis({
            bricks: bricks.map((brick) => brick.getTranslatedText(applicationLanguage)),
            keywordScoreMethod: KEYWORD_SCORE_METHOD,
            language: reviewLang ?? DEFAULT_LANG_UNKNOWN,
            responseTime: Math.floor(getTimeDifferenceInHours(commentSocialUpdatedAt ?? new Date(), reviewSocialCreatedAt)),
            reviewerName: this._cleanText(reviewerName),
            reviewerNameValidation: reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
            text: this._cleanText(text),
            textType: type,
            venueName: this._cleanText(restaurantName),
        });
    }

    private async _getRestaurantNameAndKeywords(
        restaurant: Pick<IRestaurant, '_id' | 'name'>
    ): Promise<{ restaurantName: string; restaurantKeywords: RestaurantKeyword[] }> {
        const restaurantKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywords(restaurant._id.toString());
        const restaurantName = restaurant.name;

        const restaurantAiSettings = await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantId(
            restaurant._id?.toString()
        );

        if (!restaurantAiSettings?.reviewSettings) {
            return { restaurantName, restaurantKeywords };
        }

        return {
            restaurantName: restaurantAiSettings.restaurantName,
            restaurantKeywords: restaurantKeywords.filter((restaurantKeyword) =>
                restaurantAiSettings.reviewSettings.restaurantKeywordIds?.includes(restaurantKeyword.id)
            ),
        };
    }

    private _cleanText(text: string): string {
        return removeDiacritics(text).trim();
    }
}
