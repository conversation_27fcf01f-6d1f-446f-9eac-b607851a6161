import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ICreatePrivateReview, IPrivateReview } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { AutoReplyUseCases } from ':modules/automations/auto-reply.use-cases';
import CampaignsRepository from ':modules/campaigns/campaigns.repository';
import ClientsRepository from ':modules/clients/clients.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { GeneratePublicBusinessIdService } from ':modules/reviews/services/generate-public-business-id/generate-public-business-id.service';
import { StartPreviousReviewsAnalysisService } from ':modules/reviews/services/start-previous-reviews-analysis/start-previous-reviews-analysis.service';
import { StartReviewSemanticAnalysisService } from ':modules/segment-analyses/services/start-review-semantic-analysis.service';

@singleton()
export class CreatePrivateReviewUseCase {
    constructor(
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _campaignsRepository: CampaignsRepository,
        private readonly _clientsRepository: ClientsRepository,
        private readonly _generatePublicBusinessIdService: GeneratePublicBusinessIdService,
        private readonly _startReviewSemanticAnalysisService: StartReviewSemanticAnalysisService,
        private readonly _startPreviousReviewsAnalysisService: StartPreviousReviewsAnalysisService,
        private readonly _autoReplyUseCases: AutoReplyUseCases
    ) {}

    async execute(privateReview: ICreatePrivateReview): Promise<IPrivateReview> {
        const { clientId, restaurantId } = privateReview;
        assert(restaurantId, 'restaurantId is required');
        const review = await this._privateReviewsRepository.createPrivateReview({
            ...privateReview,
            socialSortDate: privateReview.socialCreatedAt,
        });
        assert(review, 'Private review not created');

        await this._generatePublicBusinessIdService.startGeneratePublicBusinessId({ privateReviewIds: [review._id.toString()] });

        if (clientId) {
            await this._clientsRepository.updateOne({
                filter: {
                    _id: clientId,
                    restaurantId,
                },
                update: {
                    $addToSet: {
                        reviewsLeft: {
                            platformKey: 'privateNegativeReview',
                            hasLeftReview: true,
                        },
                    },
                },
            });
        }

        if (privateReview.campaignId && privateReview.clientId) {
            const setValues = {
                'contactInteractions.$.lastStarRatingDate': new Date(),
                'contactInteractions.$.lastStarRating': review.rating,
            };
            await this._campaignsRepository.updateOne({
                filter: { _id: privateReview.campaignId, 'contactInteractions.clientId': privateReview.clientId },
                update: { ...setValues },
            });
        }

        await this._startReviewSemanticAnalysisService.execute({ review, isPrivateReview: true });
        await this._startPreviousReviewsAnalysisService.execute({ review, isPrivateReview: true });
        this._autoReplyUseCases.handleReviewAutoReply(review).catch((error) => {
            logger.error('[CreatePrivateReviewUseCase] - Error with the auto reply use case', error);
        });
        return review;
    }
}
