import { render } from '@react-email/render';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { v4 } from 'uuid';

import { DownloadReviewsTemplateProps, ReviewWithTranslationsResponseDto, SimpleRestaurant } from '@malou-io/package-dto';
import { DownloadReviewsTemplate } from '@malou-io/package-emails';
import { DbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    createDate,
    filterByRequiredKeys,
    hasRatingOutOfTen,
    LANGUAGES,
    MalouErrorCode,
    PlatformKey,
    TranslationSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { ReviewPagination } from ':helpers/pagination';
import { TranslateTextService } from ':modules/ai/services/translate-text.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { AddTranslationToReviewService } from ':modules/reviews/services/add-translation-to-review.service';
import { HtmlToPdfService } from ':services/html-to-pdf-service/html-to-pdf.service';
import { ReviewsSemanticAnalysisService } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.service';

import { LoggerEvent } from './download-reviews-as-pdf.interface';
import { RestaurantReviewsToDownloadReviewsTemplateDataMapper } from './download-reviews-as-pdf.mapper';

@singleton()
export class DownloadReviewsAsPdfUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsMapper: ReviewsDtoMapper,
        private readonly _restaurantReviewsToDownloadReviewsTemplateDataMapper: RestaurantReviewsToDownloadReviewsTemplateDataMapper,
        private readonly _reviewsSemanticAnalysisService: ReviewsSemanticAnalysisService,
        private readonly _htmlToPdfService: HtmlToPdfService,
        private readonly _translateTextService: TranslateTextService,
        private readonly _addTranslationToReviewService: AddTranslationToReviewService
    ) {}

    async execute(
        reviewFilters: ReviewFiltersInput,
        pagination: ReviewPagination,
        user: { _id: DbId; defaultLanguage: ApplicationLanguage },
        timeZone: string
    ): Promise<any> {
        try {
            const { startDate, endDate, restaurantIds } = reviewFilters;

            logger.info(LoggerEvent.GET_RAW_REVIEWS, {
                startDate,
                endDate,
                userId: user._id,
            });

            const results = await this._reviewsRepository.getRestaurantReviewsPaginatedV2({
                pagination,
                filters: reviewFilters,
            });

            const { reviews } = this._reviewsMapper.toGetRestaurantsReviewsV2ResponseDto(results, pagination);

            logger.info(LoggerEvent.GET_SIMPLE_RESTAURANTS_BY_IDS, { restaurantIds });

            const restaurants = await this._restaurantsRepository.getRestaurantsForReviewsReportByIds(restaurantIds ?? []);

            logger.info(LoggerEvent.TRANSLATING_REVIEW_TEXT);

            const translatedReviews: ReviewWithTranslationsResponseDto[] = [];
            for (const review of reviews) {
                if (review.text && review.lang !== user.defaultLanguage) {
                    try {
                        const translatedReview = await this._translateReviewText(review, user);
                        if (translatedReview) {
                            translatedReviews.push(translatedReview);
                            continue;
                        }
                    } catch (error) {
                        logger.error(LoggerEvent.TRANSLATING_REVIEW_TEXT_FAILED, {
                            reviewId: review._id,
                            userId: user._id,
                        });
                    }
                }
                translatedReviews.push(review);
            }

            logger.info(LoggerEvent.COMPUTE_AVERAGE_REVIEWS_RATING);

            const averageReviewsRating =
                reviews.length === 0
                    ? 0
                    : filterByRequiredKeys(reviews, ['rating'])
                          .map((review) => (hasRatingOutOfTen(review.key) ? review.rating / 2 : review.rating))
                          .reduce((a, b) => a + b, 0) / reviews.length;

            logger.info(LoggerEvent.MAP_REVIEWS_TO_DOWNLOAD_REVIEWS_TEMPLATE, {
                reviewCount: reviews.length,
                restaurantCount: restaurants.length,
            });

            assert(reviewFilters.startDate, 'Start date is required');
            assert(reviewFilters.endDate, 'End date is required');

            const reviewsToDownload =
                await this._restaurantReviewsToDownloadReviewsTemplateDataMapper.mapToDownloadRestaurantReviewsDataSection({
                    restaurantsReviews: translatedReviews,
                    averageReviewsRating,
                    restaurants,
                    startDate: createDate(reviewFilters.startDate) as Date,
                    endDate: createDate(reviewFilters.endDate) as Date,
                    timeZone,
                    lang: user.defaultLanguage,
                });

            logger.info(LoggerEvent.GET_AI_SEMANTIC_ANALYSIS);

            const aiSemanticAnalysisResponse = await this._getAiSemanticAnalysis(reviewsToDownload, user.defaultLanguage);

            const reviewsToDownloadAsPdf = this._restaurantReviewsToDownloadReviewsTemplateDataMapper.addAiGlobalAnalysisCta(
                reviewsToDownload,
                aiSemanticAnalysisResponse
            );

            logger.info(LoggerEvent.RENDER_HTML);

            const html = render(
                DownloadReviewsTemplate({
                    ...reviewsToDownloadAsPdf,
                })
            );

            logger.info(LoggerEvent.GENERATE_PDF, { html: html.length });

            return this._generatePdf(html, restaurants, user._id.toString());
        } catch (error: any) {
            throw new MalouError(MalouErrorCode.DOWNLOAD_REVIEWS_AS_PDF_FAILED, {
                metadata: {
                    user: user._id,
                    error,
                    isMaximumLimitExceeded: error?.code === MalouErrorCode.LAMBDA_MAXIMUM_CALL_STACK_SIZE_EXCEEDED,
                    restaurantIds: reviewFilters.restaurantIds,
                },
            });
        }
    }

    private async _getAiSemanticAnalysis(
        reviewsToDownload: DownloadReviewsTemplateProps,
        defaultLanguage: string
    ): Promise<string | undefined> {
        try {
            const restaurantsPayloadData = reviewsToDownload.restaurantsReviews.map((restaurantReview) => ({
                restaurantName: restaurantReview.header.name,
                reviewsSemanticAnalysis: Object.values(restaurantReview.reviewsGroupedByPlatform)
                    .flat()
                    ?.flatMap((review) =>
                        review?.segmentAnalysis?.map(({ category, sentiment, segment }) => ({
                            tag: category,
                            sentiment,
                            originalSegment: segment,
                        }))
                    )
                    .filter((segment) => segment !== undefined),
            }));

            const { semanticAnalysisResult: aiSemanticAnalysis } = await this._reviewsSemanticAnalysisService.getSemanticAnalysisOverview({
                collection: AiInteractionRelatedEntityCollection.REVIEWS,
                language: LANGUAGES[defaultLanguage],
                restaurantsData: restaurantsPayloadData,
            });

            return aiSemanticAnalysis;
        } catch (e) {
            logger.error(LoggerEvent.GET_AI_SEMANTIC_ANALYSIS_FAILED, {
                error: e,
            });
            return undefined;
        }
    }

    private async _generatePdf(html: string, restaurants: SimpleRestaurant[], userId: string): Promise<string | null> {
        let folderName = 'download-reviews';
        const pdfFileName = `download-reviews-${DateTime.now().toISODate()}-${v4()}.pdf`;
        if (restaurants.length === 1) {
            folderName += `/${restaurants[0]._id}`;
        } else {
            folderName += `/aggregated/${userId}`;
        }

        return this._htmlToPdfService.generatePdf(html, pdfFileName, {
            folderName,
            margin: { top: 15, right: 0, bottom: 0, left: 0 },
        });
    }

    private async _translateReviewText(
        review: ReviewWithTranslationsResponseDto,
        user: { _id: DbId; defaultLanguage: string }
    ): Promise<ReviewWithTranslationsResponseDto> {
        if (review.translations) {
            const translation = review.translations[user.defaultLanguage];
            if (translation) {
                return {
                    ...review,
                    text: translation,
                };
            }
        }
        const translationResult: string | undefined = await this._translateTextService.execute({
            relatedEntityId: review._id,
            aiInteractionRelatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.REVIEW_TRANSLATION,
            text: review.text ?? '',
            lang: user.defaultLanguage,
            restaurantId: review.restaurantId,
            userId: user._id?.toString(),
        });
        if (translationResult) {
            await this._addTranslationToReviewService.saveReviewTextTranslations({
                reviewId: review._id.toString(),
                translation: translationResult,
                language: user.defaultLanguage as ApplicationLanguage,
                source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
                isPrivateReview: review.key === PlatformKey.PRIVATE,
            });
            return {
                ...review,
                text: translationResult,
            };
        }
        return review;
    }
}
