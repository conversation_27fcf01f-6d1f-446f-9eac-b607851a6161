import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IReview, toDbId } from '@malou-io/package-models';
import { AiInteractionRelatedEntityCollection } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PrivateReviewWithTranslations } from ':modules/private-reviews/private-reviews.interface';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { IPlatformReviewMapper, ReviewMapper, ReviewMapperFactory } from ':modules/reviews/reviews.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { GetPlatformReviewsService } from ':modules/reviews/services/get-review-platform-use-cases';
import { GenerateLanguageDetectionService } from ':services/text-translator/generate-language-detection.service';

@singleton()
export class AutoReplyToReviewUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _credentialsRepository: CredentialsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _getPlatformReviewsService: GetPlatformReviewsService,
        private readonly _generateLanguageDetectionService: GenerateLanguageDetectionService,
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService
    ) {}

    async execute({
        reviewId,
        replyText,
        templateId,
        interactionId,
        replyLang,
        isPrivateReview,
    }: {
        reviewId: string;
        replyText: string;
        templateId?: string;
        interactionId?: string;
        replyLang?: string | null;
        isPrivateReview?: boolean;
    }): Promise<IReview | undefined> {
        return isPrivateReview
            ? this._autoReplyToPrivateReview({
                  reviewId,
                  replyText,
                  templateId,
                  interactionId,
                  ...(replyLang && { replyLang }),
              })
            : this._autoReplyToReview({
                  reviewId,
                  replyText,
                  templateId,
                  interactionId,
                  ...(replyLang && { replyLang }),
              });
    }

    private async _autoReplyToPrivateReview({
        reviewId,
        replyText,
        templateId,
        interactionId,
        replyLang,
    }: {
        reviewId: string;
        replyText: string;
        templateId?: string;
        interactionId?: string;
        replyLang?: string;
    }) {
        try {
            const review = await this._privateReviewsRepository.getPrivateReviewByIdWithTranslation(reviewId);
            if (!review || review?.comments?.length) {
                const reason = !review ? 'no review found' : 'already answered';
                logger.info(`[AutoReplyToReviewUseCase] [_autoReplyToPrivateReview] Auto reply skipped, ${reason}`, {
                    reviewId,
                    replyText,
                    templateId,
                    interactionId,
                });
                return;
            }
            const platformReviewsUseCases = this._getPlatformReviewsService.execute(review.key);
            const PlatformReviewMapper: IPlatformReviewMapper<PrivateReviewWithTranslations> = ReviewMapperFactory.getPlatformReviewMapper(
                review.key
            ) as IPlatformReviewMapper<PrivateReviewWithTranslations>;
            const [platformReply, detectedLang] = await Promise.all([
                platformReviewsUseCases.reply({
                    review,
                    restaurantId: review.restaurantId.toString(),
                    comment: PlatformReviewMapper.mapToPlatformReply(replyText, review),
                }),
                !replyLang
                    ? this._generateLanguageDetectionService.execute({
                          relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                          relatedEntityId: review._id.toString(),
                          restaurantId: review.restaurantId.toString(),
                          text: replyText,
                      })
                    : Promise.resolve(replyLang),
            ]);

            replyLang = detectedLang;
            const mappedReply = ReviewMapper.mapToMalouReply(review.key, platformReply);
            mappedReply.isMalou = true;
            mappedReply.templateIdUsed = templateId ? toDbId(templateId) : null;
            mappedReply.aiInteractionIdUsed = interactionId ? toDbId(interactionId) : null;
            mappedReply.keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                reviewId: review._id.toString(),
                rating: review.rating ?? null,
                restaurantId: review.restaurantId,
                text: replyText,
                reviewLang: replyLang,
                reviewSocialCreatedAt: new Date(review.socialCreatedAt),
                reviewerName: [review.client?.firstName, review.client?.lastName].filter(Boolean).join(' ') ?? '',
                reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
            });

            await platformReviewsUseCases.pushReviewComment({
                socialId: review._id.toString(),
                key: review.key,
                comment: mappedReply,
            });
            logger.info('[AutoReplyToReviewUseCase] [_autoReplyToPrivateReview] Auto reply finished successfully', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                mappedReply,
            });
            return await this._reviewsRepository.findOneAndUpdateOrFail({
                filter: { _id: review._id },
                update: { wasAnsweredAutomatically: true },
                options: { lean: true },
            });
        } catch (error) {
            logger.error('[AutoReplyToReviewUseCase] [_autoReplyToPrivateReview] Error while auto replying', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                error,
            });
            return;
        }
    }

    private async _autoReplyToReview({
        reviewId,
        replyText,
        templateId,
        interactionId,
        replyLang,
    }: {
        reviewId: string;
        replyText: string;
        templateId?: string;
        interactionId?: string;
        replyLang?: string;
    }) {
        try {
            const review = await this._reviewsRepository.getReviewById(reviewId);
            if (!review || review?.comments?.length) {
                const reason = !review ? 'no review found' : 'already answered';
                logger.info(`[AutoReplyToReviewUseCase] [_autoReplyToPrivateReview] Auto reply skipped, ${reason}`, {
                    reviewId,
                    replyText,
                    templateId,
                    interactionId,
                });
                return;
            }
            const platform = await this._platformsRepository.getPlatformById(review.platformId.toString());
            assert(platform, 'Platform not found');

            const platformReviewsUseCases = this._getPlatformReviewsService.execute(review.key);
            const PlatformReviewMapper: IPlatformReviewMapper<IReview> = ReviewMapperFactory.getPlatformReviewMapper(
                review.key
            ) as IPlatformReviewMapper<IReview>;
            const credentialId = platform.credentials?.[0];
            const credential = credentialId
                ? ((await this._credentialsRepository.getCredentialById(credentialId)) ?? undefined)
                : undefined;
            const [platformReply, detectedLang] = await Promise.all([
                platformReviewsUseCases.reply({
                    review,
                    restaurantId: review.restaurantId.toString(),
                    credential,
                    platform,
                    comment: PlatformReviewMapper.mapToPlatformReply(replyText, review, platform.socialId),
                }),
                !replyLang
                    ? this._generateLanguageDetectionService.execute({
                          relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                          relatedEntityId: review._id.toString(),
                          restaurantId: review.restaurantId.toString(),
                          text: replyText,
                      })
                    : Promise.resolve(replyLang),
            ]);

            replyLang = detectedLang;
            const mappedReply = ReviewMapper.mapToMalouReply(review.key, platformReply);
            mappedReply.isMalou = true;
            mappedReply.templateIdUsed = templateId ? toDbId(templateId) : null;
            mappedReply.aiInteractionIdUsed = interactionId ? toDbId(interactionId) : null;
            mappedReply.keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                reviewId: review._id.toString(),
                rating: review.rating ?? null,
                restaurantId: review.restaurantId,
                text: replyText,
                reviewLang: replyLang,
                reviewSocialCreatedAt: new Date(review.socialCreatedAt),
                reviewerName: review.reviewer?.displayName ?? '',
                reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
            });

            await platformReviewsUseCases.pushReviewComment({
                socialId: review.socialId,
                key: review.key,
                comment: mappedReply,
            });
            logger.info('[AutoReplyToReviewUseCase] [_autoReplyToReview] Auto reply finished successfully', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                mappedReply,
            });
            return await this._reviewsRepository.findOneAndUpdateOrFail({
                filter: { _id: review._id },
                update: { wasAnsweredAutomatically: true },
                options: { lean: true },
            });
        } catch (error) {
            logger.error('[AutoReplyToReviewUseCase] [_autoReplyToReview] Error while auto replying', {
                reviewId,
                replyText,
                templateId,
                interactionId,
                error,
            });
            return;
        }
    }
}
