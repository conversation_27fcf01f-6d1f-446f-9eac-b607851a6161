import { singleton } from 'tsyringe';

import { ReviewPagination } from ':helpers/pagination';
import { PrivateReviewWithScanWithNfc } from ':modules/private-reviews/private-reviews.interface';
import { ReviewFiltersInput, ReviewWithSemanticAnalysis } from ':modules/reviews/reviews.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class GetReviewsPaginatedUseCase {
    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute(
        pagination: ReviewPagination,
        filters: ReviewFiltersInput
    ): Promise<(ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc)[]> {
        const reviews = await this._reviewsRepository.getRestaurantReviewsPaginatedV2({
            pagination,
            filters,
        });
        return reviews.map(
            (review) =>
                ({
                    ...review,
                    semanticAnalysisSegments: review.semanticAnalysisSegments,
                }) as any
        );
    }
}
