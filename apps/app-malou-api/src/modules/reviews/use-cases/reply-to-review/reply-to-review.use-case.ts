import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { DbId, IReview, IR<PERSON>iewComment, IUser, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';
import {
    COUNTRIES,
    countryCodeToUbereatsOfferTiersMap,
    DEFAULT_LANG_UNKNOWN,
    MalouErrorCode,
    PlatformKey,
    PostedStatus,
    UbereatsPromotionValue,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformHeaderConfigOptions } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { IPlatformReviewMapper, ReviewMapper, ReviewMapperFactory } from ':modules/reviews/reviews.mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { GetPlatformReviewsService } from ':modules/reviews/services/get-review-platform-use-cases';

@singleton()
export class ReplyToReviewUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _credentialsRepository: CredentialsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _getPlatformReviewsService: GetPlatformReviewsService,
        private readonly _generateKeywordAnalysisForCommentService: GenerateKeywordAnalysisForCommentService
    ) {}

    async execute({
        user,
        reviewId,
        comment,
        restaurantId,
        headerConfig,
        isFromJob,
    }: {
        user: PopulateBuilderHelper<IUser, [{ path: 'profilePicture' }]>;
        reviewId: string;
        comment: Partial<IReviewComment>;
        restaurantId: string;
        headerConfig: PlatformHeaderConfigOptions;
        isFromJob: boolean;
    }): Promise<IReview | null> {
        try {
            const review = await this._reviewsRepository.getReviewById(reviewId);
            if (!review) {
                throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, {
                    metadata: { reviewId },
                });
            }

            const platform = await this._platformsRepository.getPlatformById(review.platformId.toString());
            assert(platform, 'Platform not found');
            const credential = platform.credentials?.[0]
                ? ((await this._credentialsRepository.getCredentialById(platform.credentials?.[0])) ?? undefined)
                : undefined;
            const PlatformReviewMapper: IPlatformReviewMapper<IReview> = ReviewMapperFactory.getPlatformReviewMapper(
                platform.key
            ) as IPlatformReviewMapper<IReview>;

            let platformReply;
            try {
                platformReply = await this._getPlatformReviewsService.execute(review.key).reply({
                    review,
                    restaurantId,
                    credential,
                    platform,
                    comment: PlatformReviewMapper.mapToPlatformReply(comment.text ?? '', review, platform.socialId, comment),
                    headerConfig,
                });
            } catch (error) {
                logger.error('[REPLY_REVIEW_ERROR]', { error, platformKey: platform.key });
                const retryPlatforms = [PlatformKey.UBEREATS, PlatformKey.DELIVEROO];
                if (isFromJob || !retryPlatforms.includes(review.key)) {
                    throw error;
                }
                const reviewUpdated = await this._reviewsRepository.pushComment(reviewId, {
                    ...comment,
                    posted: PostedStatus.RETRY,
                } as IReviewComment);
                const commentId = reviewUpdated.comments[reviewUpdated.comments.length - 1]._id;
                await this._scheduleRetryReplyToReview({ user, reviewId, comment, restaurantId, headerConfig }, commentId);
                return reviewUpdated;
            }

            const mappedReply = ReviewMapper.mapToMalouReply(review.key, platformReply, user, comment);
            mappedReply.isMalou = true;
            mappedReply.templateIdUsed = comment.templateIdUsed;
            mappedReply.isRepliedFromAggregatedView = comment.isRepliedFromAggregatedView;
            mappedReply.ubereatsPromotionValue = comment.ubereatsPromotionValue;
            mappedReply.ubereatsPromotionAmountInHundredths = await this._computeUbereatsPromotionAmountInHundredths(
                restaurantId,
                comment.ubereatsPromotionValue
            );
            const hasEmptyKeywordAnalysis =
                !comment.keywordAnalysis ||
                (!comment.keywordAnalysis?.keywords?.length && !comment.keywordAnalysis?.score && !comment.keywordAnalysis?.count);

            if (mappedReply.text && hasEmptyKeywordAnalysis) {
                mappedReply.keywordAnalysis = await this._generateKeywordAnalysisForCommentService.execute({
                    reviewId: review._id.toString(),
                    rating: review.rating ?? null,
                    restaurantId: review.restaurantId,
                    text: mappedReply.text,
                    reviewLang: review.lang ?? DEFAULT_LANG_UNKNOWN,
                    reviewSocialCreatedAt: new Date(review.socialCreatedAt),
                    reviewerName: review.reviewer?.displayName ?? '',
                    reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
                });
            }

            const associatedAiInteraction = await this._aiInteractionsRepository.findOne({
                filter: { relatedEntityId: review._id },
            });
            if (associatedAiInteraction) {
                mappedReply.aiInteractionIdUsed = associatedAiInteraction._id;
            }
            await this._getPlatformReviewsService.execute(review.key).pushReviewComment({
                socialId: review.socialId,
                key: review.key,
                comment: mappedReply,
            });

            return this._reviewsRepository.findOne({
                filter: {
                    _id: toDbId(reviewId),
                },
                options: {
                    lean: true,
                    populate: [
                        {
                            path: 'semanticAnalysis',
                        },
                    ],
                },
            });
        } catch (error) {
            logger.error('[REPLY_REVIEW_UNEXPECTED_ERROR]', { error }); // Remove if it does not happen (it shouldn't)
            throw error;
        }
    }

    private async _computeUbereatsPromotionAmountInHundredths(
        restaurantId: string,
        ubereatsPromotionValue?: UbereatsPromotionValue
    ): Promise<number | undefined> {
        const restaurant = await this._restaurantsRepository.findOneOrFail({ filter: { _id: restaurantId }, options: { lean: true } });
        const restaurantCountry = restaurant.address?.country;
        const countryCode = COUNTRIES.find((country) => country.name === restaurantCountry || country.name_fr === restaurantCountry)?.code;
        if (!countryCode) {
            return undefined;
        }
        const amount = countryCodeToUbereatsOfferTiersMap[countryCode]?.[ubereatsPromotionValue];
        return amount ? amount * 100 : undefined;
    }

    private async _scheduleRetryReplyToReview(
        args: {
            user: PopulateBuilderHelper<IUser, [{ path: 'profilePicture' }]>;
            reviewId: string;
            comment: Partial<IReviewComment>;
            restaurantId: string;
            headerConfig: PlatformHeaderConfigOptions;
        },
        commentId: DbId
    ): Promise<void> {
        const in5Minutes = DateTime.now().plus({ minutes: 5 }).toJSDate();
        await this._agendaSingleton.schedule(in5Minutes, AgendaJobName.RETRY_REPLY_TO_REVIEW, {
            commentId,
            args,
        });
    }
}
