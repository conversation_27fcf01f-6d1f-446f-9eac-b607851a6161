import {
    I<PERSON><PERSON>urant<PERSON>dd<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    IReviewAnalysis,
    ISegmentAnalysisWithParentTopics,
    ITranslations,
    OverwriteOrAssign,
    PopulateBuilderHelper,
} from '@malou-io/package-models';
import { PrivatePlatforms, ReviewAnalysisSentiment } from '@malou-io/package-utils';

export interface RestaurantReviewsRatingStats {
    restaurant: {
        _id: string;
        name: string;
        address: IRestaurantAddress;
        internalName?: string;
    };
    averageRating?: number;
    total?: number;
    reviews: {
        key: string;
        socialId: string;
        rating: number;
    }[];
}

export interface ReviewFiltersInput {
    answerable?: boolean | null;
    answered?: boolean | null;
    archived?: boolean | null;
    endDate?: Date | string | null;
    notAnswered?: boolean | null;
    pending?: boolean | null;
    platforms?: any[] | null;
    previousPeriod?: any | null;
    privatePlatforms?: PrivatePlatforms[] | null;
    /** Zero is a special value and matches reviews without rating. */
    ratings?: (0 | 1 | 2 | 3 | 4 | 5)[] | null;
    restaurantIds?: string[] | null;
    searchText?: string | null;
    showPrivate?: boolean | null;
    startDate?: Date | string | null;
    text?: string | null;
    unarchived?: boolean | null;
    withoutText?: boolean | null;
    withText?: boolean | null;
}

export type ReviewWithTranslations = PopulateBuilderHelper<IReview, [{ path: 'translations' }]>;

export type ReviewWithSemanticAnalysis = OverwriteOrAssign<
    ReviewWithTranslations,
    {
        semanticAnalysis?: IReviewAnalysis;
        semanticAnalysisSegments?: ISegmentAnalysisWithParentTopics[];
        aiRelevantBricks?: Array<OverwriteOrAssign<NonNullable<IReview['aiRelevantBricks']>[number], { translations?: ITranslations }>>;
        ratingTags?: OverwriteOrAssign<NonNullable<IReview['ratingTags']>[number], { translations?: ITranslations }>[];
    }
>;

export type ReviewWithSemanticAnalysisAndSentiment = OverwriteOrAssign<
    ReviewWithSemanticAnalysis,
    {
        sentiment: ReviewAnalysisSentiment;
    }
>;

export type ReviewWithAnalysis = Pick<
    ReviewWithSemanticAnalysis,
    | 'socialId'
    | 'text'
    | 'key'
    | 'semanticAnalysis'
    | 'semanticAnalysisSegments'
    | 'reviewer'
    | 'socialCreatedAt'
    | 'rating'
    | 'restaurantId'
>;
