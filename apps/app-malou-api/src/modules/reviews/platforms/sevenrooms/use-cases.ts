import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPlatform, IReview, IReviewComment } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { SevenroomsCredentialRepository } from ':modules/credentials/platforms/sevenrooms/sevenrooms.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SevenroomsReplyPayload } from ':modules/reviews/platforms/sevenrooms/interface';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { SevenroomsProvider } from ':providers/sevenrooms/sevenrooms.provider';
import { SevenroomsReview } from ':providers/sevenrooms/sevenrooms.provider.interface';
import { GetSevenroomsVenueService } from ':services/platforms/sevenrooms/get-sevenrooms-venue.service';

const MAX_PAGE = 4;
const MAX_PAGE_RECENT = 2;
const SEVENROOMS_FETCH_REVIEWS_INTERVAL_IN_MS = 3 * TimeInMilliseconds.SECOND;

@singleton()
export class SevenroomsReviewsUseCases implements PlatformReviewsUseCases<IReview> {
    constructor(
        private readonly _sevenroomsProvider: SevenroomsProvider,
        private readonly _sevenroomsCredentialRepository: SevenroomsCredentialRepository,
        private readonly _getSevenroomsVenueService: GetSevenroomsVenueService,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async getReviewsData(
        { socialId, restaurantId }: { socialId?: string; restaurantId?: string },
        recentOnly: boolean
    ): Promise<SevenroomsReview[]> {
        if (!socialId) {
            return [];
        }
        const sevenroomsCredential = await this._sevenroomsCredentialRepository.getSuperCredential();
        assert(sevenroomsCredential, 'Missing Sevenrooms credential');
        const { sessionId, csrfToken, authId } = sevenroomsCredential;
        const doesUserHaveMarketingAccess = await this._sevenroomsProvider.doesUserHaveMarketingAccess({
            sessionId,
            socialId,
            email: authId,
        });
        if (!doesUserHaveMarketingAccess) {
            throw new MalouError(MalouErrorCode.SEVENROOMS_USER_NO_MARKETING_ACCESS, {
                metadata: {
                    email: authId,
                    sessionId,
                    socialId,
                },
            });
        }
        await this._sevenroomsCredentialRepository.updateSuperCredentialExpiration();
        const maxPage = recentOnly ? MAX_PAGE_RECENT : MAX_PAGE;
        const venueId = await this._getSevenroomsVenueService.execute({ socialId, restaurantId, sessionId, csrfToken });
        assert(venueId, 'Venue ID not found for the given socialId or restaurantId');
        return this._fetchReviews({ venueId, csrfToken, sessionId, maxPage });
    }

    mapReviewsDataToMalou(platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        const filteredReviews = reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review)).filter((r) => r.socialId);
        return filteredReviews;
    }

    reply = async ({ review, comment }: { review: IReview; comment: PlatformReplyPayload }): Promise<any> => {
        try {
            const sevenroomsComment = comment as SevenroomsReplyPayload;
            const platform = await this._platformsRepository.findOne({
                filter: { _id: review.platformId },
                projection: { socialId: 1, venueId: 1 },
                options: { lean: true },
            });

            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: {
                        platformId: review.platformId,
                    },
                });
            }
            assert(platform.socialId, 'Platform socialId is required to reply to a Sevenrooms review');
            const reviewerSocialId = review.reviewer?.socialId;
            if (!reviewerSocialId) {
                throw new MalouError(MalouErrorCode.NOT_FOUND, {
                    metadata: {
                        socialId: platform.socialId,
                        review,
                        message: 'Reviewer social ID not found',
                    },
                });
            }

            const sevenroomsCredential = await this._sevenroomsCredentialRepository.getSuperCredential();
            assert(sevenroomsCredential, 'Missing Sevenrooms credential');
            const { sessionId, csrfToken } = sevenroomsCredential;

            const venueId = await this._getSevenroomsVenueService.execute({
                socialId: platform.socialId,
                csrfToken,
                sessionId,
            });
            assert(venueId, 'Venue ID not found for the given platform socialId');

            const conversationId = await this._getConversationId({
                sessionId,
                csrfToken,
                socialId: platform.socialId!,
                userId: reviewerSocialId,
            });
            if (!conversationId) {
                throw new MalouError(MalouErrorCode.NOT_FOUND, {
                    metadata: {
                        socialId: platform.socialId,
                        message: 'Conversation ID not found in user information',
                    },
                });
            }

            await this._sevenroomsProvider.postReviewReply({
                csrfToken,
                sessionId,
                venueId,
                conversationId,
                userId: reviewerSocialId,
                replyPayload: {
                    comment: sevenroomsComment.comment,
                },
            });

            reviewsReplyCounter.add(1, {
                source: PlatformKey.SEVENROOMS,
                status: 'success',
            });
            return sevenroomsComment;
        } catch (error) {
            logger.error('[SEVENROOMS_REPLY_ERROR] ', { error, platformId: review.platformId, reviewId: review._id });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.SEVENROOMS,
                status: 'failure',
            });
            throw error;
        }
    };

    pushReviewComment = ({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }) =>
        this._reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    updateComment() {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'SevenroomsReviewsUseCases does not implement updateComment',
        });
    }

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'SevenroomsReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    private async _fetchReviews({
        venueId,
        csrfToken,
        sessionId,
        maxPage,
    }: {
        venueId: string;
        csrfToken: string;
        sessionId: string;
        maxPage: number;
    }): Promise<SevenroomsReview[]> {
        const reviews: SevenroomsReview[] = [];
        try {
            let page = 0;
            do {
                const sevenroomsReviews = await this._fetchReviewsPage({ sessionId, csrfToken, venueId, page });
                reviews.push(...sevenroomsReviews);
                page += 1;
                await waitFor(SEVENROOMS_FETCH_REVIEWS_INTERVAL_IN_MS); // To avoid 429 Too Many Requests
            } while (page < maxPage);
            reviewsFetchCounter.add(1, {
                source: PlatformKey.SEVENROOMS,
                status: 'success',
            });
            return reviews;
        } catch (error) {
            reviewsFetchCounter.add(1, {
                source: PlatformKey.SEVENROOMS,
                status: 'failure',
            });
            throw error;
        }
    }

    private async _fetchReviewsPage({
        sessionId,
        csrfToken,
        venueId,
        page,
    }: {
        sessionId: string;
        csrfToken: string;
        venueId: string;
        page: number;
    }): Promise<SevenroomsReview[]> {
        const sevenroomsReviews = await this._sevenroomsProvider.getPaginatedReviews({
            sessionId,
            csrfToken,
            venueId,
            page,
        });
        await this._sevenroomsCredentialRepository.updateSuperCredentialExpiration();
        return sevenroomsReviews.data.results;
    }

    private async _getConversationId({
        sessionId,
        csrfToken,
        socialId,
        userId,
    }: {
        sessionId: string;
        csrfToken: string;
        socialId: string;
        userId: string;
    }): Promise<string> {
        const userInfo = await this._sevenroomsProvider.getUserInformation({
            csrfToken,
            sessionId,
            socialId,
            userId,
        });

        if (!userInfo) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                metadata: {
                    socialId,
                    userId,
                    message: 'User information not found',
                },
            });
        }

        return userInfo.data.actual.conversation;
    }
}
