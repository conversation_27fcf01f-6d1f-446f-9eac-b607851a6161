import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { PlatformKey, PostedStatus, ReviewAnalysisSentiment, ReviewAnalysisTag, RoiTip } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultReviewReplyAutomation } from ':modules/automations/tests/review-reply-automation.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReviewAnalysis } from ':modules/review-analyses/tests/review-analysis.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { GetReviewsTips } from ':modules/roi/use-cases/compute-tips/get-tips-by-category/get-reviews-tips';
import { getTipDataFromId } from ':modules/roi/use-cases/get-roi-tips/utils';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';

describe('GetReviewsTips', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'ReviewAnalysesRepository',
            'ReviewsRepository',
            'AutomationsRepository',
            'SegmentAnalysesRepository',
        ]);
    });

    describe('execute', () => {
        describe('given reviews that are before new semantic analysis', () => {
            const now = DateTime.fromISO('2024-01-01T12:00:00.000Z');
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const oneMonthAgo = now.minus({ months: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const oneMonthDateFilter = { $gte: oneMonthAgo, $lte: yesterday };
            const threeMonthsDateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            it('should return REVIEWS_NOT_ENOUGH tip because of no activity', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);

                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(3).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(oneMonthAgo)
                                        .socialCreatedAt(oneMonthAgo)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return Array.from(Array(3).keys()).map((i) =>
                                    getDefaultReviewAnalysis()
                                        .platformKey(dependencies.reviews()[i].key)
                                        .segmentAnalyses(
                                            Object.values(ReviewAnalysisTag).map((tag) => ({
                                                sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                originalSegment: 'Test Original Segment',
                                                probability: 0.5,
                                                segment: 'Test Segment',
                                                tag,
                                            }))
                                        )
                                        .reviewSocialId(dependencies.reviews()[i].socialId)
                                        .build()
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [getTipDataFromId(RoiTip.REVIEWS_NOT_ENOUGH)];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should return all reviews tips', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);

                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(oneMonthAgo)
                                        .socialCreatedAt(oneMonthAgo)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReviewAnalysis()
                                        .platformKey(dependencies.reviews()[i].key)
                                        .segmentAnalyses(
                                            Object.values(ReviewAnalysisTag).map((tag) => ({
                                                sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                originalSegment: 'Test Original Segment',
                                                probability: 0.5,
                                                segment: 'Test Segment',
                                                tag,
                                            }))
                                        )
                                        .reviewSocialId(dependencies.reviews()[i].socialId)
                                        .build()
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            getTipDataFromId(RoiTip.REVIEWS_AVERAGE_RESPONSE_TIME_TOO_HIGH),
                            getTipDataFromId(RoiTip.REVIEWS_AUTOMATION_NOT_ACTIVATED),
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return  REVIEWS_AVERAGE_RESPONSE_TIME_TOO_HIGH tip because response time < 4 days', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReviewAnalysis()
                                        .platformKey(dependencies.reviews()[i].key)
                                        .segmentAnalyses(
                                            Object.values(ReviewAnalysisTag).map((tag) => ({
                                                sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                originalSegment: 'Test Original Segment',
                                                probability: 0.5,
                                                segment: 'Test Segment',
                                                tag,
                                            }))
                                        )
                                        .reviewSocialId(dependencies.reviews()[i].socialId)
                                        .build()
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            getTipDataFromId(RoiTip.REVIEWS_AUTOMATION_NOT_ACTIVATED),
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return REVIEWS_AUTOMATION_NOT_ACTIVATED tip because if there is 1 active automation for the restaurant', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReviewAnalysis()
                                        .platformKey(dependencies.reviews()[i].key)
                                        .segmentAnalyses(
                                            Object.values(ReviewAnalysisTag).map((tag) => ({
                                                sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                originalSegment: 'Test Original Segment',
                                                probability: 0.5,
                                                segment: 'Test Segment',
                                                tag,
                                            }))
                                        )
                                        .reviewSocialId(dependencies.reviews()[i].socialId)
                                        .build()
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it("should not return  REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS and REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS tips because they don't appear in more than 30% of negative reviews", async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReviewAnalysis()
                                        .platformKey(dependencies.reviews()[i].key)
                                        .segmentAnalyses(
                                            Object.values(ReviewAnalysisTag)
                                                .filter((tag) => tag !== ReviewAnalysisTag.PRICE && tag !== ReviewAnalysisTag.FOOD)
                                                .map((tag) => ({
                                                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                    originalSegment: 'Test Original Segment',
                                                    probability: 0.5,
                                                    segment: 'Test Segment',
                                                    tag,
                                                }))
                                        )
                                        .reviewSocialId(dependencies.reviews()[i].socialId)
                                        .build()
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return tips', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(9).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        reviewAnalysis: {
                            data(dependencies) {
                                return [
                                    ...Array.from(Array(7).keys()).map((i) =>
                                        getDefaultReviewAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .segmentAnalyses([
                                                {
                                                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                    originalSegment: 'Test Original Segment',
                                                    probability: 0.5,
                                                    segment: 'Test Segment',
                                                    tag: ReviewAnalysisTag.SERVICE,
                                                },
                                            ])
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    ),
                                    ...Array.from(Array(2).keys()).map((i) =>
                                        getDefaultReviewAnalysis()
                                            .platformKey(dependencies.reviews()[i + 7].key)
                                            .segmentAnalyses([
                                                {
                                                    sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                                    originalSegment: 'Test Original Segment',
                                                    probability: 0.5,
                                                    segment: 'Test Segment',
                                                    tag: ReviewAnalysisTag.SERVICE,
                                                },
                                            ])
                                            .reviewSocialId(dependencies.reviews()[i + 7].socialId)
                                            .build()
                                    ),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return [{ ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 }];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });
        });

        describe('given reviews that are after new semantic analysis', () => {
            const now = DateTime.fromISO('2026-01-01T12:00:00.000Z');
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const oneMonthAgo = now.minus({ months: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const oneMonthDateFilter = { $gte: oneMonthAgo, $lte: yesterday };
            const threeMonthsDateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            it('should return REVIEWS_NOT_ENOUGH tip because of no activity', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);

                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(3).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(oneMonthAgo)
                                        .socialCreatedAt(oneMonthAgo)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return Array.from(Array(3).keys()).flatMap((i) =>
                                    Object.values(ReviewAnalysisTag).map((tag) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .category(tag)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment')
                                            .aiFoundSegment('Test Segment')
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    )
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [getTipDataFromId(RoiTip.REVIEWS_NOT_ENOUGH)];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should return all reviews tips', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);

                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(oneMonthAgo)
                                        .socialCreatedAt(oneMonthAgo)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).flatMap((i) =>
                                    Object.values(ReviewAnalysisTag).map((tag) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .category(tag)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment')
                                            .aiFoundSegment('Test Segment')
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    )
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            getTipDataFromId(RoiTip.REVIEWS_AVERAGE_RESPONSE_TIME_TOO_HIGH),
                            getTipDataFromId(RoiTip.REVIEWS_AUTOMATION_NOT_ACTIVATED),
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return  REVIEWS_AVERAGE_RESPONSE_TIME_TOO_HIGH tip because response time < 4 days', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).flatMap((i) =>
                                    Object.values(ReviewAnalysisTag).map((tag) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .category(tag)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment')
                                            .aiFoundSegment('Test Segment')
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    )
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            getTipDataFromId(RoiTip.REVIEWS_AUTOMATION_NOT_ACTIVATED),
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return REVIEWS_AUTOMATION_NOT_ACTIVATED tip because if there is 1 active automation for the restaurant', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).flatMap((i) =>
                                    Object.values(ReviewAnalysisTag).map((tag) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .category(tag)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment')
                                            .aiFoundSegment('Test Segment')
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    )
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            { ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it("should not return  REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS and REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS tips because they don't appear in more than 30% of negative reviews", async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return Array.from(Array(5).keys()).flatMap((i) =>
                                    Object.values(ReviewAnalysisTag)
                                        .filter((tag) => tag !== ReviewAnalysisTag.PRICE && tag !== ReviewAnalysisTag.FOOD)
                                        .map((tag) =>
                                            getDefaultSegmentAnalysis()
                                                .platformKey(dependencies.reviews()[i].key)
                                                .category(tag)
                                                .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                                .segment('Test Original Segment')
                                                .aiFoundSegment('Test Segment')
                                                .reviewSocialId(dependencies.reviews()[i].socialId)
                                                .build()
                                        )
                                );
                            },
                        },
                    },
                    expectedResult() {
                        return [
                            { ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                            { ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 },
                        ];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });

            it('should not return tips', async () => {
                const getReviewsTips = container.resolve(GetReviewsTips);
                const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'automations'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                            },
                        },
                        automations: {
                            data(dependencies) {
                                return [
                                    getDefaultReviewReplyAutomation().restaurantId(dependencies.restaurants()[0]._id).active(true).build(),
                                ];
                            },
                        },
                        reviews: {
                            data(dependencies) {
                                return Array.from(Array(9).keys()).map((i) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .key(PlatformKey.GMB)
                                        .createdAt(yesterday)
                                        .socialCreatedAt(yesterday)
                                        .comments([
                                            {
                                                text: 'Test Comment',
                                                posted: PostedStatus.POSTED,
                                                _id: newDbId(),
                                                socialUpdatedAt: yesterday,
                                            },
                                        ])
                                        .socialId(`social_id_${i}`)
                                        .rating(2)
                                        .build()
                                );
                            },
                        },
                        segmentAnalyses: {
                            data(dependencies) {
                                return [
                                    ...Array.from(Array(7).keys()).flatMap((i) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i].key)
                                            .category(ReviewAnalysisTag.SERVICE)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment' + i)
                                            .aiFoundSegment('Test Segment' + i)
                                            .reviewSocialId(dependencies.reviews()[i].socialId)
                                            .build()
                                    ),
                                    ...Array.from(Array(2).keys()).flatMap((i) =>
                                        getDefaultSegmentAnalysis()
                                            .platformKey(dependencies.reviews()[i + 7].key)
                                            .category(ReviewAnalysisTag.SERVICE)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segment('Test Original Segment' + (i + 7))
                                            .aiFoundSegment('Test Segment' + (i + 7))
                                            .reviewSocialId(dependencies.reviews()[i + 7].socialId)
                                            .build()
                                    ),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return [{ ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS), value: 100 }];
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getReviewsTips.execute(restaurantId, { oneMonthDateFilter, threeMonthsDateFilter });

                expect(result).toEqual(expectedResult);
            });
        });
    });
});
