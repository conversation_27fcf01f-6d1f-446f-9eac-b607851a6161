import { singleton } from 'tsyringe';

import { IReviewWithSemanticAnalysis, toDbId } from '@malou-io/package-models';
import {
    DateFilter,
    getNumberOfDaysFromMilliseconds,
    isDateAfterNewSemanticAnalysisMinDate,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisTag,
    RoiTip,
    RoiTipData,
    roiTipsConfig,
} from '@malou-io/package-utils';

import AutomationsRepository from ':modules/automations/automations.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { getTipDataFromId } from ':modules/roi/use-cases/get-roi-tips/utils';
import { RoiUtils } from ':modules/roi/use-cases/utils';

@singleton()
export class GetReviewsTips {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _automationsRepository: AutomationsRepository,
        private readonly _roiUtils: RoiUtils
    ) {}

    async execute(
        restaurantId: string,
        { oneMonthDateFilter, threeMonthsDateFilter }: { oneMonthDateFilter: DateFilter; threeMonthsDateFilter: DateFilter }
    ): Promise<RoiTipData[]> {
        const reviewsTips: RoiTipData[] = [];

        const reviews = await this._reviewsRepository.getReviewsWithAnalysis({
            restaurantId: toDbId(restaurantId),
            socialCreatedAt: threeMonthsDateFilter,
            key: PlatformKey.GMB,
        });

        if (reviews.length < roiTipsConfig.MIN_REVIEW_COUNT) {
            return [getTipDataFromId(RoiTip.REVIEWS_NOT_ENOUGH)];
        }

        // TODO: remove check when socialCreatedAt is required in reviews
        const lastMonthReviews = reviews.filter((review) =>
            review.socialCreatedAt ? review.socialCreatedAt.getTime() >= oneMonthDateFilter.$gte.getTime() : false
        );

        const negativeReviews = lastMonthReviews.filter((review) => (review.rating ?? 0) < roiTipsConfig.MIN_POSITIVE_REVIEW_RATING);
        const tipsForSemanticAnalysisTags = this._getTipsForSemanticAnalysisTags(negativeReviews);

        const averageResponseTimeInMilliseconds = await this._reviewsRepository.getAverageResponseTimeInMillisecondsByRestaurantId({
            restaurantId,
            startDate: oneMonthDateFilter.$gte,
            endDate: oneMonthDateFilter.$lte,
        });
        if (getNumberOfDaysFromMilliseconds(averageResponseTimeInMilliseconds) > roiTipsConfig.MAX_RESPONSE_TIME_IN_DAYS) {
            reviewsTips.push(getTipDataFromId(RoiTip.REVIEWS_AVERAGE_RESPONSE_TIME_TOO_HIGH));
        }

        const activatedAutomations = await this._automationsRepository.find({
            filter: {
                active: true,
                restaurantId,
            },
            options: { lean: true },
        });
        if (activatedAutomations.length === 0) {
            reviewsTips.push(getTipDataFromId(RoiTip.REVIEWS_AUTOMATION_NOT_ACTIVATED));
        }

        return [...reviewsTips, ...tipsForSemanticAnalysisTags];
    }

    private _getTipsForSemanticAnalysisTags(negativeReviews: Partial<IReviewWithSemanticAnalysis>[]): RoiTipData[] {
        const tipsForSemanticAnalysisTags: RoiTipData[] = [];

        const totalNegativeReviewTagCount = this._getTotalNegativeReviewCount(negativeReviews);

        Object.entries(totalNegativeReviewTagCount).forEach(([tag, count]) => {
            switch (tag) {
                case ReviewAnalysisTag.PRICE:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_PRICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                case ReviewAnalysisTag.OVERALL_EXPERIENCE:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_QUALITY_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                case ReviewAnalysisTag.SERVICE:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_SERVICE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                case ReviewAnalysisTag.HYGIENE:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_HYGIENE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                case ReviewAnalysisTag.FOOD:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_KITCHEN_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                case ReviewAnalysisTag.ATMOSPHERE:
                    if (this._isTagRegularlyMentionedInNegativeReviews(count, negativeReviews.length)) {
                        tipsForSemanticAnalysisTags.push({
                            ...getTipDataFromId(RoiTip.REVIEWS_ATMOSPHERE_REGULARLY_MENTIONED_IN_NEGATIVE_ANALYSIS),
                            value: this._getPercentage(count, negativeReviews.length),
                        });
                    }
                    break;
                default:
                    break;
            }
        });

        return tipsForSemanticAnalysisTags;
    }

    private _getTotalNegativeReviewCount(negativeReviews: Partial<IReviewWithSemanticAnalysis>[]): Record<ReviewAnalysisTag, number> {
        const totalNegativeReviewTagCount = this._roiUtils.createEmptyNegativeReviewTagCount();

        negativeReviews.forEach((review) => {
            const negativeReviewTagCount = this._roiUtils.createNegativeReviewTagsFlags();
            if (isDateAfterNewSemanticAnalysisMinDate(review.socialCreatedAt!)) {
                review.semanticAnalysisSegments?.forEach((segmentAnalysis) => {
                    if (
                        segmentAnalysis.sentiment === ReviewAnalysisSentiment.NEGATIVE &&
                        !negativeReviewTagCount[segmentAnalysis.category]
                    ) {
                        negativeReviewTagCount[segmentAnalysis.category] = true;
                    }
                });
            } else {
                review.semanticAnalysis?.segmentAnalyses.forEach((segmentAnalysis) => {
                    if (segmentAnalysis.sentiment === ReviewAnalysisSentiment.NEGATIVE && !negativeReviewTagCount[segmentAnalysis.tag]) {
                        negativeReviewTagCount[segmentAnalysis.tag] = true;
                    }
                });
            }
            this._mergeNegativeReviewsTagsCount(totalNegativeReviewTagCount, negativeReviewTagCount);
        });

        return totalNegativeReviewTagCount;
    }

    private _getPercentage(tagCount: number, total: number): number {
        return (tagCount / total) * 100;
    }

    private _isTagRegularlyMentionedInNegativeReviews(tagCount: number, total: number): boolean {
        if (this._getPercentage(tagCount, total) >= roiTipsConfig.MAX_TAG_APPEARANCE_PERCENTAGE_IN_NEGATIVE_REVIEWS) {
            return true;
        }
        return false;
    }

    private _mergeNegativeReviewsTagsCount(
        totalNegativeReviewsTagCount: Record<ReviewAnalysisTag, number>,
        negativeReviewTagCount: Record<ReviewAnalysisTag, boolean>
    ): void {
        Object.entries(negativeReviewTagCount).forEach(([tag, hasNegativeFlag]) => {
            if (hasNegativeFlag) {
                totalNegativeReviewsTagCount[tag] += 1;
            }
        });
    }
}
