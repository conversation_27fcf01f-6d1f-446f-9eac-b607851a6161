import { singleton } from 'tsyringe';

import { IPrivateReview, IReview } from '@malou-io/package-models';
import { isDateAfterNewSemanticAnalysisMinDate, SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { ReviewSemanticAnalysisProducer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';

@singleton()
export class StartReviewSemanticAnalysisService {
    constructor(
        private readonly _reviewSemanticAnalysisProducer: ReviewSemanticAnalysisProducer,
        private readonly _reviewsRepository: ReviewsRepository
    ) {}

    async execute({ review, isPrivateReview = false }: { review: IReview | IPrivateReview; isPrivateReview?: boolean }): Promise<void> {
        try {
            if (!review.text?.length) {
                logger.info('[StartReviewSemanticAnalysisService] Review has no text', {
                    reviewId: review._id,
                });
                return;
            }

            if (review.semanticAnalysisFetchStatus && review.semanticAnalysisFetchStatus !== SemanticAnalysisFetchStatus.FAILED) {
                logger.info('[StartReviewSemanticAnalysisService] Review already has semantic analysis', {
                    reviewId: review._id,
                    semanticAnalysisFetchStatus: review.semanticAnalysisFetchStatus,
                });
                return;
            }

            const socialDate = (review as IReview).socialUpdatedAt || review.socialCreatedAt;
            const isAfterNewSemanticAnalysisStartDate = isDateAfterNewSemanticAnalysisMinDate(socialDate);
            if (!isAfterNewSemanticAnalysisStartDate) {
                logger.info('[StartReviewSemanticAnalysisService] Review is before new semantic analysis start date', {
                    reviewId: review._id,
                    socialDate,
                });
                return;
            }

            await this._reviewSemanticAnalysisProducer.execute({
                reviewId: review._id.toString(),
                restaurantId: review.restaurantId.toString(),
                isPrivateReview,
            });
            await this._reviewsRepository.updateOne({
                filter: { _id: review._id },
                update: { semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus.PENDING },
            });
        } catch (err) {
            logger.error('[StartReviewSemanticAnalysisService] Error while starting review semantic analysis', {
                reviewId: review._id,
                error: err,
            });
        }
    }
}
