import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { DailyReportProps, DailyReviewsStatsProps, RestaurantProps, SegmentAnalysisProps, SimpleRestaurant } from '@malou-io/package-dto';
import { IReviewWithSemanticAnalysisAndTranslations } from '@malou-io/package-models';
import {
    GrowthVariation,
    isDateAfterNewSemanticAnalysisMinDate,
    isNotNil,
    Locale,
    mapApplicationLanguageToLocale,
    numberToFixed,
} from '@malou-io/package-utils';

import { ConcernedRestaurantsProps, DailyReviewsReportsData } from ':modules/reports/use-cases/review-reports/review-reports.types';
import { User } from ':modules/users/entities/user.entity';

@singleton()
export class DailyReviewReportsMapper {
    async mapToEmailProps(
        user: User,
        aggregationResult: DailyReviewsReportsData,
        restaurants: SimpleRestaurant[]
    ): Promise<DailyReportProps> {
        const locale = mapApplicationLanguageToLocale(user.defaultLanguage);

        return {
            locale,
            period: this._getPeriod(),
            concernedRestaurants: this._getConcernedRestaurants(restaurants),
            reviewsStats: this._getReviewsStats(aggregationResult, restaurants),
            restaurantsReviews: await this._getRestaurantsReviews(aggregationResult, restaurants, locale),
        };
    }

    private readonly _getPeriod = (): { startDate: Date; endDate: Date } => {
        const periodStartDate = DateTime.now().minus({ days: 1 }).set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        const periodEndDate = DateTime.now().set({ hour: 23, minute: 59, second: 59, millisecond: 999 });
        return { startDate: periodStartDate.toJSDate(), endDate: periodEndDate.toJSDate() };
    };

    private _getConcernedRestaurants(restaurants: SimpleRestaurant[]): ConcernedRestaurantsProps[] {
        return restaurants.map((restaurant) => ({
            name: restaurant.name,
            address: restaurant.formattedAddress ?? '',
            image: restaurant.logo,
        }));
    }

    private _getReviewsStats(aggregationResult: DailyReviewsReportsData, restaurants: SimpleRestaurant[]): DailyReviewsStatsProps {
        const allRestaurantsReviewsCountGrowthRate =
            isNotNil(aggregationResult.metadata?.count) && isNotNil(aggregationResult.previousMetadata?.count)
                ? numberToFixed(aggregationResult.metadata.count - aggregationResult.previousMetadata.count)
                : 0;
        const allRestaurantsReviewsRatingGrowthRate =
            isNotNil(aggregationResult.metadata?.ratingAvg) && isNotNil(aggregationResult.previousMetadata?.ratingAvg)
                ? numberToFixed(aggregationResult.metadata?.ratingAvg - aggregationResult.previousMetadata?.ratingAvg)
                : 0;
        const allRestaurantsReviewsNotAnsweredGrowthRate =
            isNotNil(aggregationResult.metadata?.notAnsweredCount) && isNotNil(aggregationResult.previousMetadata?.notAnsweredCount)
                ? numberToFixed(aggregationResult.metadata?.notAnsweredCount - aggregationResult.previousMetadata?.notAnsweredCount)
                : 0;
        return {
            restaurantCount: restaurants.length,
            stats: {
                rating: {
                    growth: {
                        rate: allRestaurantsReviewsCountGrowthRate,
                        variation: allRestaurantsReviewsCountGrowthRate > 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                        flipped: false,
                        isPercentage: false,
                        isSemantic: false,
                    },
                    totalRated: numberToFixed(aggregationResult.metadata?.count),
                },
                averageRating: {
                    value: numberToFixed(aggregationResult.metadata?.ratingAvg),
                    growth: {
                        rate: numberToFixed(allRestaurantsReviewsRatingGrowthRate),
                        variation: numberToFixed(allRestaurantsReviewsRatingGrowthRate) >= 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                        flipped: false,
                        isPercentage: false,
                        isSemantic: false,
                    },
                },
                notAnsweredReviews: {
                    value: numberToFixed(aggregationResult.metadata?.notAnsweredCount),
                    growth: {
                        rate: allRestaurantsReviewsNotAnsweredGrowthRate,
                        variation: allRestaurantsReviewsNotAnsweredGrowthRate >= 0 ? GrowthVariation.DOWN : GrowthVariation.UP,
                        flipped: true,
                        isPercentage: false,
                        isSemantic: false,
                    },
                },
                cta: {
                    link: '',
                },
                noReviewsCta: {
                    link: this._getNoReviewsCtaLink(restaurants),
                },
                aiGlobalAnalysisCta: {
                    noticeText: aggregationResult.aiSemanticGlobalAnalysis,
                    link: `${this._getReviewsBaseUrl(restaurants)}?from_email=daily_reviews&clicked_on=semantic_global_analysis_cta`,
                },
            },
        };
    }

    private async _getRestaurantsReviews(
        aggregationResult: DailyReviewsReportsData,
        restaurants: SimpleRestaurant[],
        locale: Locale
    ): Promise<RestaurantProps[]> {
        const restaurantsReviews: RestaurantProps[] = await Promise.all(
            aggregationResult.data.map(async (restaurantData) => {
                const growthRate =
                    isNotNil(restaurantData.metadata?.ratingAvg) && isNotNil(restaurantData.previousMetadata?.ratingAvg)
                        ? numberToFixed(restaurantData.metadata?.ratingAvg - restaurantData.previousMetadata?.ratingAvg)
                        : 0;
                return {
                    header: {
                        name: restaurantData.name,
                        address: restaurantData.address ?? '',
                        image: restaurants.find((restaurant) => restaurant.name === restaurantData.name)?.logo ?? '',
                        rating: {
                            totalRated: restaurantData.metadata?.ratingAvg ?? 0,
                            growth: {
                                rate: growthRate,
                                variation: growthRate > 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                                flipped: false,
                                isPercentage: false,
                                isSemantic: false,
                            },
                        },
                        reviewsCount: {
                            totalReviewsCount: restaurantData.metadata?.count ?? 0,
                            nonAnsweredReviewsCount: restaurantData.metadata?.notAnsweredCount ?? 0,
                        },
                    },
                    reviews: await Promise.all(
                        restaurantData.reviews.map(async (review) => ({
                            reviewerProfilePhoto: review.reviewer?.profilePhotoUrl ?? '',
                            platformImage: review.key,
                            reviewerName: review.reviewer?.displayName ?? '',
                            rating: review.rating ?? 0,
                            text: review.text ?? undefined,
                            segmentAnalysis: await this._getSegmentAnalysis(review),
                            translatedFrom: review.lang && review.lang !== locale ? review.lang : undefined,
                            socialCreatedAt: review.socialCreatedAt,
                            socialUpdatedAt: review.socialUpdatedAt ?? undefined,
                            locale,
                        }))
                    ),
                    stats: {},
                    cta: {
                        link:
                            restaurantData.reviews.length === 0
                                ? `${process.env.BASE_URL}/restaurants/${restaurantData.restaurantId}/resources/totems?from_email=daily_reviews&clicked_on=daily_reviews_no_reviews_cta`
                                : `${process.env.BASE_URL}/restaurants/${restaurantData.restaurantId}/reputation/reviews?from_email=daily_reviews&clicked_on=daily_reviews_cta`,
                    },
                };
            })
        );
        return restaurantsReviews.sort((a, b) => {
            const aTotalRated = a?.header?.rating?.totalRated ?? 0;
            const bTotalRated = b?.header?.rating?.totalRated ?? 0;
            if (aTotalRated >= 0 && bTotalRated >= 0) {
                return bTotalRated - aTotalRated;
            }
            if (aTotalRated >= 0) {
                return -1;
            }
            if (bTotalRated >= 0) {
                return 1;
            }
            return 0;
        });
    }

    private _getReviewsBaseUrl(restaurants: SimpleRestaurant[]): string {
        const definedRestaurants = restaurants.filter((restaurant) => isNotNil(restaurant?._id));
        return definedRestaurants.length === 1
            ? `${process.env.BASE_URL}/restaurants/${definedRestaurants[0]._id}/reputation/reviews`
            : `${process.env.BASE_URL}/groups/reputation/reviews`;
    }

    private _getNoReviewsCtaLink(rests: SimpleRestaurant[]): string {
        const firstExistingRestaurantId = rests.find((restaurant) => isNotNil(restaurant?._id))?._id;
        const baseUrl = process.env.BASE_URL;
        const emailParams = 'from_email=daily_reviews&clicked_on=no_reviews_cta';

        if (firstExistingRestaurantId) {
            return `${baseUrl}/restaurants/${firstExistingRestaurantId}/resources/totems?${emailParams}`;
        }

        return `${baseUrl}/groups/reputation/reviews?${emailParams}`;
    }

    private async _getSegmentAnalysis(review: IReviewWithSemanticAnalysisAndTranslations): Promise<SegmentAnalysisProps[]> {
        const isCreatedAfterSemanticAnalysisRelease = isDateAfterNewSemanticAnalysisMinDate(review.socialCreatedAt);
        if (isCreatedAfterSemanticAnalysisRelease) {
            return (
                review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                    category,
                    sentiment,
                    segment,
                })) ?? []
            );
        }
        return review.semanticAnalysis?.segmentAnalyses?.map(({ tag, sentiment, segment }) => ({
            category: tag,
            sentiment,
            segment,
        }));
    }
}
