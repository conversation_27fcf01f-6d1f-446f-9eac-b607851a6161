import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    AverageRatingProps,
    NotAnsweredReviewsProps,
    RatingProps,
    RestaurantHeaderProps,
    ReviewProps,
    SegmentAnalysisProps,
    SimpleRestaurant,
    WeeklyRestaurantReviewsProps,
    WeeklyReviewsProps,
    WeeklyReviewsReportProps,
} from '@malou-io/package-dto';
import { IReviewWithSemanticAnalysisAndTranslations } from '@malou-io/package-models';
import {
    getPlatformDefinition,
    GrowthVariation,
    isDateAfterNewSemanticAnalysisMinDate,
    isNotNil,
    Locale,
    mapApplicationLanguageToLocale,
    numberToFixed,
} from '@malou-io/package-utils';

import { ConcernedRestaurantsProps } from ':modules/reports/use-cases/review-reports/review-reports.types';
import { WeeklyReviewsReportData } from ':modules/reports/use-cases/review-reports/weekly-reports/reviews-report-weekly.entity';
import { User } from ':modules/users/entities/user.entity';

@singleton()
export class WeeklyReviewReportsMapper {
    async mapToEmailProps(
        user: User,
        aggregationResult: WeeklyReviewsReportData,
        restaurants: SimpleRestaurant[]
    ): Promise<WeeklyReviewsReportProps> {
        const locale = mapApplicationLanguageToLocale(user.defaultLanguage);

        return {
            locale,
            period: this._getPeriod(),
            concernedRestaurants: this._getConcernedRestaurants(restaurants),
            reviewsStats: this._getReviewsStats(aggregationResult, restaurants),
            restaurantsReviews: await this._getRestaurantsReviews(aggregationResult, restaurants, locale),
        };
    }

    private readonly _getPeriod = (): { startDate: Date; endDate: Date } => {
        const periodStartDate = DateTime.now().minus({ days: 7 }).set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        const periodEndDate = DateTime.now().set({ hour: 23, minute: 59, second: 59, millisecond: 999 });
        return { startDate: periodStartDate.toJSDate(), endDate: periodEndDate.toJSDate() };
    };

    private _getConcernedRestaurants(restaurants: SimpleRestaurant[]): ConcernedRestaurantsProps[] {
        return restaurants.map((restaurant) => ({
            name: restaurant.name,
            address: restaurant.formattedAddress ?? '',
            image: restaurant.logo,
        }));
    }

    private _getReviewsStats(aggregationResult: WeeklyReviewsReportData, restaurants: SimpleRestaurant[]): WeeklyReviewsProps {
        const allRestaurantsReviewsCountGrowthRate =
            isNotNil(aggregationResult.metadata?.count) && isNotNil(aggregationResult.previousMetadata?.count)
                ? numberToFixed(aggregationResult.metadata?.count - aggregationResult.previousMetadata?.count)
                : 0;
        const allRestaurantsReviewsRatingGrowthRate =
            isNotNil(aggregationResult.metadata?.ratingAvg) && isNotNil(aggregationResult.previousMetadata?.ratingAvg)
                ? numberToFixed(aggregationResult.metadata?.ratingAvg - aggregationResult.previousMetadata?.ratingAvg)
                : 0;
        const allRestaurantsReviewsNotAnsweredGrowthRate =
            isNotNil(aggregationResult.metadata?.notAnsweredCount) && isNotNil(aggregationResult.previousMetadata?.notAnsweredCount)
                ? numberToFixed(aggregationResult.metadata?.notAnsweredCount - aggregationResult.previousMetadata?.notAnsweredCount)
                : 0;

        return {
            restaurantCount: restaurants.length,
            stats: {
                rating: {
                    totalRated: aggregationResult.metadata?.count,
                    growth: {
                        rate: allRestaurantsReviewsCountGrowthRate,
                        variation: allRestaurantsReviewsCountGrowthRate > 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                        flipped: false,
                        isPercentage: false,
                        isSemantic: false,
                    },
                },
                averageRating: {
                    value: aggregationResult.metadata?.ratingAvg,
                    growth: {
                        rate: allRestaurantsReviewsRatingGrowthRate,
                        variation: allRestaurantsReviewsRatingGrowthRate > 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                        flipped: false,
                        isPercentage: false,
                        isSemantic: false,
                    },
                } as AverageRatingProps,
                notAnsweredReviews: {
                    value: aggregationResult.metadata?.notAnsweredCount,
                    growth: {
                        rate: allRestaurantsReviewsNotAnsweredGrowthRate,
                        variation: allRestaurantsReviewsNotAnsweredGrowthRate > 0 ? GrowthVariation.UP : GrowthVariation.DOWN,
                        flipped: true,
                        isPercentage: false,
                        isSemantic: false,
                    },
                } as NotAnsweredReviewsProps,
                cta: {
                    link: `${this._getReviewsBaseUrl(restaurants)}?from_email=weekly_reviews&clicked_on=reviews_cta`,
                    noticeText: aggregationResult.reportGlobalReviewsSemanticAnalysisOverview,
                },
                noReviewsCta: {
                    link: this._getNoReviewsCtaLink(restaurants),
                },
            },
        } as WeeklyReviewsProps;
    }

    private _getReviewsBaseUrl(restaurants: SimpleRestaurant[]): string {
        const definedRestaurants = restaurants.filter((restaurant) => isNotNil(restaurant?._id));
        return definedRestaurants.length === 1
            ? `${process.env.BASE_URL}/restaurants/${definedRestaurants[0]._id}/reputation/reviews`
            : `${process.env.BASE_URL}/groups/reputation/reviews`;
    }

    private _getNoReviewsCtaLink(rests: SimpleRestaurant[]): string {
        const firstExistingRestaurantId = rests.find((restaurant) => isNotNil(restaurant?._id))?._id;
        const baseUrl = process.env.BASE_URL;
        const emailParams = 'from_email=weekly_reviews&clicked_on=no_reviews_cta';

        if (firstExistingRestaurantId) {
            return `${baseUrl}/restaurants/${firstExistingRestaurantId}/resources/totems?${emailParams}`;
        }

        return `${baseUrl}/groups/reputation/reviews?${emailParams}`;
    }

    private async _getRestaurantsReviews(
        aggregationResult: WeeklyReviewsReportData,
        restaurants: SimpleRestaurant[],
        locale: Locale
    ): Promise<WeeklyRestaurantReviewsProps[]> {
        const restaurantsReviews: WeeklyRestaurantReviewsProps[] = await Promise.all(
            aggregationResult.data.map(async (restaurantData) => {
                const restaurantBaseUrl = `${process.env.BASE_URL}/restaurants/${restaurantData.restaurantId}`;
                return {
                    noReviewsCta: {
                        link: `${restaurantBaseUrl}/boosters/totems?from_email=weekly_reviews&clicked_on=no_reviews_cta`,
                    },
                    platformsCta: {
                        link: `${restaurantBaseUrl}/reputation/reviews?from_email=weekly_reviews&clicked_on=platforms_cta`,
                    },
                    semanticAnalysis: {
                        cta: {
                            noticeText: restaurantData.reviewsSemanticAnalysisOverview,
                            link: `${restaurantBaseUrl}/statistics/e-reputation?from_email=weekly_reviews&clicked_on=semantic_analysis_cta`,
                        },
                    },
                    restaurantHeader: {
                        address: restaurantData.address ?? '',
                        name: restaurantData.name,
                        image: restaurantData.image,
                        rating: {
                            totalRated: numberToFixed(restaurantData.metadata?.ratingAvg),
                            growth: {
                                rate:
                                    isNotNil(restaurantData.metadata?.ratingAvg) && isNotNil(restaurantData.previousMetadata?.ratingAvg)
                                        ? numberToFixed(restaurantData.metadata?.ratingAvg - restaurantData.previousMetadata?.ratingAvg)
                                        : 0,
                                variation: this._getVariation(
                                    restaurantData.metadata?.ratingAvg,
                                    restaurantData.previousMetadata?.ratingAvg
                                ),
                                flipped: false,
                                isPercentage: false,
                                isSemantic: false,
                            },
                        },
                        reviewsCount: {
                            nonAnsweredReviewsCount: numberToFixed(restaurantData.metadata?.notAnsweredCount),
                            totalReviewsCount: numberToFixed(restaurantData.metadata?.count),
                        },
                    } as RestaurantHeaderProps,
                    noReviewsLink: {
                        link: `${process.env.BASE_URL}/restaurants/${restaurantData.restaurantId}/boosters/totems?from_email=weekly_reviews&clicked_on=no_reviews_cta`,
                    },
                    platforms: await Promise.all(
                        restaurantData.platforms.map(async (platform) => {
                            return {
                                rating: {
                                    totalRated: numberToFixed(platform.metadata.platformRating),
                                    growth: {
                                        rate: numberToFixed(platform.metadata.platformRating - platform.previousMetadata.platformRating),
                                        variation: this._getVariation(
                                            platform.metadata.platformRating,
                                            platform.previousMetadata.platformRating
                                        ),
                                        flipped: false,
                                        isPercentage: false,
                                        isSemantic: false,
                                    },
                                } as RatingProps,

                                averageRating: {
                                    value: numberToFixed(platform.metadata.ratingAvg),
                                    growth: {
                                        rate:
                                            isNotNil(platform.metadata.ratingAvg) && isNotNil(platform.previousMetadata.ratingAvg)
                                                ? numberToFixed(platform.metadata.ratingAvg - platform.previousMetadata.ratingAvg)
                                                : 0,
                                        variation: this._getVariation(platform.metadata.ratingAvg, platform.previousMetadata.ratingAvg),
                                        flipped: false,
                                        isPercentage: false,
                                        isSemantic: false,
                                    },
                                } as AverageRatingProps,

                                topReviews: (await Promise.all(
                                    platform.topReviews.map(async (review) => ({
                                        reviewerName: review.reviewer?.displayName,
                                        reviewerProfilePhoto: review.reviewer?.profilePhotoUrl,
                                        rating: review.rating,
                                        segmentAnalysis: await this._getSegmentAnalysis(review),
                                        text: review.text,
                                        translatedFrom: review.lang !== locale ? review.lang : undefined,
                                        socialCreatedAt: review.socialCreatedAt,
                                    }))
                                )) as ReviewProps[],

                                flopReviews: (await Promise.all(
                                    platform.flopReviews.map(async (review) => ({
                                        reviewerName: review.reviewer?.displayName,
                                        reviewerProfilePhoto: review.reviewer?.profilePhotoUrl,
                                        rating: review.rating,
                                        segmentAnalysis: await this._getSegmentAnalysis(review),
                                        text: review.text,
                                        translatedFrom: review.lang !== locale ? review.lang : undefined,
                                        socialCreatedAt: review.socialCreatedAt,
                                    }))
                                )) as ReviewProps[],
                                cta: {
                                    link: `${process.env.BASE_URL}/restaurants/${restaurantData.restaurantId}/reputation/reviews?from_email=weekly_reviews&clicked_on=platforms_cta&platform=${platform.platformKey}`,
                                },
                                metaData: {
                                    fullName: getPlatformDefinition(platform.platformKey)?.fullName ?? '',
                                    key: platform.platformKey,
                                },
                            };
                        })
                    ),
                };
            })
        );
        return (
            restaurantsReviews
                // sort from the highest rated platform to the lowest
                .sort((a, b) => {
                    const aTotalRated = a?.restaurantHeader?.rating?.totalRated ?? 0;
                    const bTotalRated = b?.restaurantHeader?.rating?.totalRated ?? 0;
                    if (aTotalRated >= 0 && bTotalRated >= 0) {
                        return bTotalRated - aTotalRated;
                    }
                    if (aTotalRated >= 0) {
                        return -1;
                    }
                    if (bTotalRated >= 0) {
                        return 1;
                    }
                    return 0;
                })
        );
    }

    private _getVariation(currentValue: number | undefined, previousValue: number | undefined): GrowthVariation {
        if (isNotNil(currentValue) && isNotNil(previousValue)) {
            const difference = numberToFixed(currentValue - previousValue);
            return difference > 0 ? GrowthVariation.UP : GrowthVariation.DOWN;
        }

        return GrowthVariation.NONE;
    }

    private async _getSegmentAnalysis(review: IReviewWithSemanticAnalysisAndTranslations): Promise<SegmentAnalysisProps[]> {
        const isCreatedAfterSemanticAnalysisRelease = isDateAfterNewSemanticAnalysisMinDate(review.socialCreatedAt);

        if (isCreatedAfterSemanticAnalysisRelease) {
            return (
                review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                    category,
                    sentiment,
                    segment,
                })) ?? []
            );
        }
        return review.semanticAnalysis?.segmentAnalyses?.map(({ tag, sentiment, segment }) => ({
            category: tag,
            sentiment,
            segment,
        }));
    }
}
