import { isEmpty } from 'lodash';
import { singleton } from 'tsyringe';

import { PreviousSegmentAnalysisDto, ReviewAnalysisDto, SegmentAnalysisDto } from '@malou-io/package-dto';
import { isNotNil } from '@malou-io/package-utils';

import { ReviewWithSemanticAnalysis } from ':modules/reviews/reviews.interfaces';

@singleton()
export class ReviewAnalysesDtoMapper {
    fromSemanticAnalysisToReviewAnalysisDto(semanticAnalysis: ReviewWithSemanticAnalysis['semanticAnalysis']): ReviewAnalysisDto | null {
        if (!semanticAnalysis) {
            return null;
        }
        const {
            _id,
            platformKey,
            reviewSocialId,
            createdAt,
            failCount,
            providerKey,
            segmentAnalyses,
            status,
            updatedAt,
            openaiProcessingTimeMs,
            providerId,
            rawProviderResponse,
            socialCreatedAt,
            failedReason,
        } = semanticAnalysis;

        return {
            _id: _id.toString(),
            platformKey,
            reviewSocialId,
            createdAt: createdAt.toISOString(),
            failCount,
            providerKey,
            segmentAnalyses: segmentAnalyses
                .filter(isNotNil)
                .map((segmentAnalysis) => this._fromSegmentAnalysisToSegmentAnalysisDto(segmentAnalysis)),
            status,
            updatedAt: updatedAt.toISOString(),
            openaiProcessingTimeMs,
            providerId,
            rawProviderResponse,
            socialCreatedAt: socialCreatedAt?.toISOString(),
            failedReason,
        };
    }

    fromSemanticAnalysisToReviewAnalysisDtoV2(
        semanticAnalysisSegments: ReviewWithSemanticAnalysis['semanticAnalysisSegments'],
        restaurantId: string
    ): SegmentAnalysisDto[] {
        return (
            semanticAnalysisSegments?.map((segmentAnalysis) => {
                // Find the parent topic for the current restaurant
                const parentTopic = segmentAnalysis.segmentAnalysisParentTopics?.length
                    ? segmentAnalysis.segmentAnalysisParentTopics.find((topic) => topic.restaurantId.toString() === restaurantId)
                    : undefined;

                return {
                    id: segmentAnalysis._id.toString(),
                    platformKey: segmentAnalysis.platformKey,
                    reviewSocialId: segmentAnalysis.reviewSocialId,
                    reviewSocialCreatedAt: segmentAnalysis.reviewSocialCreatedAt,
                    platformSocialId: segmentAnalysis.platformSocialId,
                    createdAt: segmentAnalysis.createdAt.toISOString(),
                    updatedAt: segmentAnalysis.updatedAt.toISOString(),
                    topic: segmentAnalysis.topic,
                    category: segmentAnalysis.category,
                    segment: segmentAnalysis.segment,
                    aiFoundSegment: segmentAnalysis.aiFoundSegment,
                    isRatingTagOrMenuItem: segmentAnalysis.isRatingTagOrMenuItem,
                    sentiment: segmentAnalysis.sentiment,
                    segmentAnalysisParentTopic:
                        parentTopic && !isEmpty(parentTopic)
                            ? {
                                  ...parentTopic,
                                  id: parentTopic._id?.toString(),
                                  subcategory: parentTopic.subcategory ?? undefined,
                                  restaurantId: parentTopic.restaurantId?.toString(),
                                  translationsId: parentTopic.translationsId?.toString(),
                                  translations: parentTopic.translations
                                      ? {
                                            ...parentTopic.translations,
                                            id: parentTopic.translations._id?.toString(),
                                        }
                                      : undefined,
                              }
                            : undefined,
                };
            }) ?? []
        );
    }

    private _fromSegmentAnalysisToSegmentAnalysisDto(
        segmentAnalysis: NonNullable<ReviewWithSemanticAnalysis['semanticAnalysis']>['segmentAnalyses'][0]
    ): PreviousSegmentAnalysisDto {
        const { tag, segment, sentiment, probability, originalSegment } = segmentAnalysis;
        return { tag, segment, sentiment, probability, originalSegment };
    }
}
