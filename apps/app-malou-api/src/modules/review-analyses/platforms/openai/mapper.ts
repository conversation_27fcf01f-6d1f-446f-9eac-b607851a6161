import levenshtein from 'fast-levenshtein';

import { ReviewAnalysisSentiment, ReviewAnalysisSubCategory, ReviewAnalysisTag } from '@malou-io/package-utils';

import { appendCharToString, findCharacterIndices, getCharAfterSubstring } from ':helpers/utils';
import { AiReviewAnalysisCategory, AiReviewAnalysisSubCategory } from ':microservices/ai-semantic-analysis.service';

const MAX_LOOP_COUNT = 8;
const MIN_SEGMENT_LENGTH = 1;

export namespace ReviewSemanticAnalysisMapper {
    export const mapToMalouSegmentCategory = (category: AiReviewAnalysisCategory): ReviewAnalysisTag => {
        const mappedCategory = {
            [AiReviewAnalysisCategory.CUISINE]: ReviewAnalysisTag.FOOD,
            [AiReviewAnalysisCategory.SERVICE]: ReviewAnalysisTag.SERVICE,
            [AiReviewAnalysisCategory.PRICE]: ReviewAnalysisTag.PRICE,
            [AiReviewAnalysisCategory.AMBIANCE]: ReviewAnalysisTag.ATMOSPHERE,
            [AiReviewAnalysisCategory.HYGIENE]: ReviewAnalysisTag.HYGIENE,
            [AiReviewAnalysisCategory.OVERALL_EXPERIENCE]: ReviewAnalysisTag.OVERALL_EXPERIENCE,
        }[category];

        return mappedCategory || ReviewAnalysisTag.OVERALL_EXPERIENCE;
    };

    export const mapToLambdaSegmentCategory = (category: ReviewAnalysisTag): AiReviewAnalysisCategory => {
        const mappedCategory = {
            [ReviewAnalysisTag.FOOD]: AiReviewAnalysisCategory.CUISINE,
            [ReviewAnalysisTag.SERVICE]: AiReviewAnalysisCategory.SERVICE,
            [ReviewAnalysisTag.PRICE]: AiReviewAnalysisCategory.PRICE,
            [ReviewAnalysisTag.ATMOSPHERE]: AiReviewAnalysisCategory.AMBIANCE,
            [ReviewAnalysisTag.HYGIENE]: AiReviewAnalysisCategory.HYGIENE,
            [ReviewAnalysisTag.OVERALL_EXPERIENCE]: AiReviewAnalysisCategory.OVERALL_EXPERIENCE,
        }[category];

        return mappedCategory || AiReviewAnalysisCategory.OVERALL_EXPERIENCE;
    };

    export const mapToMalouSegmentSubcategory = (subCategory?: AiReviewAnalysisSubCategory): ReviewAnalysisSubCategory | undefined => {
        if (!subCategory) {
            return undefined;
        }
        const mappedSubCategory = {
            [AiReviewAnalysisSubCategory.MENU_ITEMS]: ReviewAnalysisSubCategory.MENU_ITEMS,
            [AiReviewAnalysisSubCategory.STAFF_MEMBERS]: ReviewAnalysisSubCategory.STAFF_MEMBERS,
        }[subCategory];

        return mappedSubCategory || undefined;
    };

    export const mapToLambdaSegmentSubCategory = (subCategory: ReviewAnalysisSubCategory | null): AiReviewAnalysisSubCategory | null => {
        if (!subCategory) {
            return null;
        }
        const mappedSubCategory = {
            [ReviewAnalysisSubCategory.MENU_ITEMS]: AiReviewAnalysisSubCategory.MENU_ITEMS,
            [ReviewAnalysisSubCategory.STAFF_MEMBERS]: AiReviewAnalysisSubCategory.STAFF_MEMBERS,
        }[subCategory];

        return mappedSubCategory || null;
    };

    export const mapToMalouSentiment = (sentiment: string): ReviewAnalysisSentiment => {
        const sentimentMapping: Record<string, ReviewAnalysisSentiment> = {
            positive: ReviewAnalysisSentiment.POSITIVE,
            negative: ReviewAnalysisSentiment.NEGATIVE,
            neutral: ReviewAnalysisSentiment.NEUTRAL,
        };
        return sentimentMapping[sentiment.toLowerCase()] || ReviewAnalysisSentiment.NEUTRAL;
    };

    export const cleanSegment = (reviewText: string, segment: string): string | null => {
        if (!segment) {
            return null;
        }
        if (reviewText.includes(segment)) {
            return segment;
        }
        const originalSegment = findOriginalSegment(reviewText, segment);
        if (!(originalSegment.length > MIN_SEGMENT_LENGTH)) {
            return null;
        }
        return finalizeOriginalSegmentSearch(reviewText, originalSegment, segment);
    };

    export const findOriginalSegment = (reviewText: string, segment: string): string => {
        const firstChar = segment[0];
        if (!firstChar) {
            return segment;
        }
        const firstCharIndices = findCharacterIndices(reviewText, firstChar);
        if (!firstCharIndices.length) {
            return segment;
        }
        const firstIndex = firstCharIndices.shift()!;

        let candidate = reviewText.slice(firstIndex, segment.length + firstIndex);
        let maxDistance = levenshtein.get(candidate, segment);
        let originalSegment = candidate;

        for (const index of firstCharIndices) {
            if (segment.length + index >= reviewText.length) {
                return originalSegment;
            }
            candidate = reviewText.slice(index, segment.length + index);
            const distance = levenshtein.get(candidate, segment);
            if (distance < maxDistance) {
                originalSegment = candidate;
                maxDistance = distance;
            }
        }
        return originalSegment.trim();
    };

    export const finalizeOriginalSegmentSearch = (reviewText: string, originalSegment: string, openaiSegment: string): string => {
        let maxDistance = levenshtein.get(originalSegment, openaiSegment);
        let distance = 0;
        let count = 0;

        let res = originalSegment;
        while (distance <= maxDistance && count < MAX_LOOP_COUNT) {
            const charAfterSubstring = getCharAfterSubstring(reviewText, res);
            if (!charAfterSubstring) {
                return res.trim();
            }
            const finalSegment = appendCharToString(res, charAfterSubstring);
            distance = levenshtein.get(finalSegment, openaiSegment);
            if (distance < maxDistance) {
                res = finalSegment;
                maxDistance = distance;
            }
            count += 1;
        }

        return res.trim();
    };
}
