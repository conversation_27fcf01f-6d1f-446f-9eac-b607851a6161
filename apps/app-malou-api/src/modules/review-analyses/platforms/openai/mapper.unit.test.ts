import { ReviewSemanticAnalysisMapper } from './mapper';

describe('ReviewSemanticAnalysisMapper', () => {
    describe('cleanSegment', () => {
        it('should return the exact segment if it exists in the reviewText', () => {
            const reviewText = 'This is a review text';
            const segment = 'review';

            const result = ReviewSemanticAnalysisMapper.cleanSegment(reviewText, segment);
            const expectedResult = segment;

            expect(result).toEqual(expectedResult);
        });

        it.each([
            {
                reviewText: 'This is a revew text',
                segment: 'review',
                originalSegment: 'revew',
            },
            {
                reviewText:
                    "c'était génial. Petit restaurint dans le 2e arrondissement.t de Paris;spécialités algériennes,délicieuses,personnel très accueillant  souriant.A visiter..",
                segment: 'Petit restaurant dans le 2e arrondissement de Paris',
                originalSegment: 'Petit restaurint dans le 2e arrondissement.t de Paris',
            },
            {
                reviewText:
                    "The meal was corrected, but the <PERSON> never apologized or came to the table again. He went out for a smoke and didn't return. We had to check out with a much friendly colleague. With so many great places to eat in Paris, this is one to skip",
                segment: 'we had to check out with a much friendly colleague',
                originalSegment: 'We had to check out with a much friendly colleague',
            },
            {
                reviewText:
                    "La salle esr jolie mais cela reste très bruyant. Nous avons attendu 30 min pour que l'on prenne notre commande et encore c'est parce que j'ai appelé un serveur. Pas d'excuse, pas l'air même désolés de la situation. Les pâtes sont bonnes mais trop al dente,",
                segment: 'La salle est jolie',
                originalSegment: 'La salle esr jolie',
            },
            {
                reviewText: 'suoer restaurant dnas le 11éme arr A refaire vite',
                segment: 'super restaurant dans le 11éme',
                originalSegment: 'suoer restaurant dnas le 11éme',
            },
        ])('should return a substring from the reviewText', ({ reviewText, segment, originalSegment }) => {
            const result = ReviewSemanticAnalysisMapper.cleanSegment(reviewText, segment);
            const expectedResult = originalSegment;
            expect(result).toEqual(expectedResult);
        });
    });

    describe('findOriginalSegment', () => {
        it('should return the exact segment if it exists in the reviewText', () => {
            const reviewText = 'This is a review text';
            const segment = 'review';

            const result = ReviewSemanticAnalysisMapper.findOriginalSegment(reviewText, segment);
            const expectedResult = segment;
            expect(result).toEqual(expectedResult);
        });

        it('should return a segment with the same starting character as the input if no exact match exists in the reviewText', () => {
            const reviewText = 'This is a revew text';
            const segment = 'review';
            const originalSegment = 'revew';

            const result = ReviewSemanticAnalysisMapper.findOriginalSegment(reviewText, segment);
            const expectedResult = originalSegment;
            expect(result).toEqual(expectedResult);
        });
    });

    describe('finalizeOriginalSegmentSearch', () => {
        it('should return the exact segment if it is already a match', () => {
            const reviewText = 'This is a review text';
            const originalSegment = 'review';
            const openaiSegment = 'review';

            const result = ReviewSemanticAnalysisMapper.finalizeOriginalSegmentSearch(reviewText, originalSegment, openaiSegment);
            const expectedResult = originalSegment;
            expect(result).toEqual(expectedResult);
        });

        it.each([
            {
                reviewText: 'This is a review text',
                originalSegment: 'revie',
                openaiSegment: 'review',
            },
            {
                reviewText: 'This is a review text',
                originalSegment: 'revie',
                openaiSegment: 'review text',
            },
        ])('should return a complete segment that is a match', ({ reviewText, originalSegment, openaiSegment }) => {
            const result = ReviewSemanticAnalysisMapper.finalizeOriginalSegmentSearch(reviewText, originalSegment, openaiSegment);
            const expectedResult = openaiSegment;
            expect(result).toEqual(expectedResult);
        });
    });
});
