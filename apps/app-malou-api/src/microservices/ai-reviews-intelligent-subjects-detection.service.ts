import { singleton } from 'tsyringe';

import { Config } from ':config';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { DetectIntelligentSubjectsPayload, DetectIntelligentSubjectsResponse } from ':modules/ai/interfaces/ai.interfaces';

@singleton()
export class ReviewsIntelligentSubjectsDetectionService {
    async detectIntelligentSubjects(
        payload: DetectIntelligentSubjectsPayload
    ): Promise<GenericAiServiceResponseType<DetectIntelligentSubjectsResponse>> {
        const AiService = new GenericAiService<DetectIntelligentSubjectsPayload, DetectIntelligentSubjectsResponse>({
            lambdaUrl: Config.services.aiSemanticAnalysisService.functionName,
        });
        return AiService.generateCompletion(payload);
    }
}
