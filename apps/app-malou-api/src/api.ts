import express, { Router } from 'express';
import { container } from 'tsyringe';

import AiInteractionsRouter from ':modules/ai-interactions/ai-interactions.router';
import AiRouter from ':modules/ai/ai.router';
import ApiKeysRouter from ':modules/api-keys/api-keys.router';
import AttributesRouter from ':modules/attributes/attributes.router';
import AutomationsRouter from ':modules/automations/automations.router';
import CalendarEventsRouter from ':modules/calendar-events/calendar-events.router';
import CampaignsRouter from ':modules/campaigns/campaigns.router';
import CategoriesRouter from ':modules/categories/categories.router';
import ChecklistsRouter from ':modules/checklists/checklists.router';
import ClientsRouter from ':modules/clients/clients.router';
import CommentsRouter from ':modules/comments/comments.router';
import CredentialsRouter from ':modules/credentials/credentials.router';
import DeliverooCredentialsRouter from ':modules/credentials/platforms/deliveroo/deliveroo.router';
import FacebookCredentialsRouter from ':modules/credentials/platforms/facebook/facebook.router';
import GmbCredentialsRouter from ':modules/credentials/platforms/gmb/gmb.router';
import InstagramCredentialsRouter from ':modules/credentials/platforms/instagram/instagram.router';
import TiktokCredentialsRouter from ':modules/credentials/platforms/tiktok/tiktok.router';
import UberEatsCredentialsRouter from ':modules/credentials/platforms/ubereats/ubereats.router';
import YelpCredentialsRouter from ':modules/credentials/platforms/yelp/api';
import ZeltyCredentialsRouter from ':modules/credentials/platforms/zelty/zelty.router';
import ZenchefCredentialsRouter from ':modules/credentials/platforms/zenchef/zenchef.router';
import EmailVerificationRouter from ':modules/email-verification/email-verification.router';
import FeedbacksRouter from ':modules/feedbacks/feedback.router';
import FoldersRouter from ':modules/folders/folders.router';
import GiftDrawsRouter from ':modules/gift-draws/gift-draws.router';
import HashtagsRouter from ':modules/hashtags/hashtags.router';
import HourTypesRouter from ':modules/hour-types/hour-types.router';
import InformationUpdatesRouter from ':modules/information-updates/information-updates.router';
import KeywordSearchImpressionsRouter from ':modules/keyword-search-impressions/keyword-search-impressions.router';
import KeywordsRouter from ':modules/keywords/keywords.router';
import LabelsRouter from ':modules/labels/labels.router';
import MaintenanceRouter from ':modules/maintenance/maintenance.router';
import MediaRouter from ':modules/media/medias.router';
import MentionsRouter from ':modules/mentions/mentions.router';
import MessagesRouter from ':modules/messages/messages.router';
import NfcsRouter from ':modules/nfc/nfcs.router';
import NotificationsRouter from ':modules/notifications/notifications.router';
import OrganizationsRouter from ':modules/organizations/organizations.router';
import PlatformInsightsRouter from ':modules/platform-insights/platform-insights.router';
import PlatformsRouter from ':modules/platforms/platforms.router';
import PostInsightsRouter from ':modules/post-insights/post-insights.router';
import PostInsightsRouterV2 from ':modules/post-insights/v2/post-insights.router';
import PostsRouter from ':modules/posts/posts.router';
import PostsRouterV2 from ':modules/posts/v2/posts.router';
import PrivateReviewsRouter from ':modules/private-reviews/private-reviews.router';
import ProcessingMediasRouter from ':modules/processing-medias/processing-medias.router';
import PublishersRouter from ':modules/publishers/publishers.router';
import ReportsRouter from ':modules/reports/reports.router';
import RestaurantAiSettingsRouter from ':modules/restaurant-ai-settings/restaurant-ai-settings.router';
import RestaurantsRouter from ':modules/restaurants/restaurants.router';
import ReviewAnalysesRouter from ':modules/review-analyses/review-analyses.router';
import ReviewsRouter from ':modules/reviews/reviews.router';
import RoiSettingsRouter from ':modules/roi-settings/roi-settings.router';
import RoiRouter from ':modules/roi/roi.router';
import ScansRouter from ':modules/scans/scans.router';
import SegmentAnalysesRouter from ':modules/segment-analyses/segment-analyses.router';
import SegmentAnalysisParentTopicsRouter from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.router';
import ShiftsRouter from ':modules/shifts/shifts.router';
import SimilarRestaurantsRouter from ':modules/similar-restaurants/similar-restaurants.router';
import SocialAccountsRouter from ':modules/social-accounts/social-accounts.router';
import StoreLocatorRouter from ':modules/store-locator/store-locator.router';
import StoriesRouter from ':modules/stories/stories.router';
import SuggestionsRouter from ':modules/suggestions/suggestions.router';
import TemplatesRouter from ':modules/templates/templates.router';
import UserFiltersRouter from ':modules/user-filters/user-filters.router';
import UsersRouter from ':modules/users/users.router';
import Watchers from ':modules/watchers/watchers.router';
import WebhooksRouter from ':modules/webhooks/webhooks.router';
import WheelsOfFortuneRouter from ':modules/wheels-of-fortune/wheels-of-fortune.router';

export const api = (): Router => {
    const router = express.Router();

    // please sort alphabetically
    router.use('/ai-interactions', container.resolve(AiInteractionsRouter).init());
    router.use('/ai', container.resolve(AiRouter).init());
    router.use('/api-keys', container.resolve(ApiKeysRouter).init());
    router.use('/attributes', container.resolve(AttributesRouter).init());
    router.use('/automations', container.resolve(AutomationsRouter).init());
    router.use('/calendar-events', container.resolve(CalendarEventsRouter).init());
    router.use('/campaigns', container.resolve(CampaignsRouter).init());
    router.use('/categories', container.resolve(CategoriesRouter).init());
    router.use('/checklists', container.resolve(ChecklistsRouter).init());
    router.use('/clients', container.resolve(ClientsRouter).init());
    router.use('/comments', container.resolve(CommentsRouter).init());
    router.use('/credentials', container.resolve(CredentialsRouter).init());
    router.use('/deliveroo', container.resolve(DeliverooCredentialsRouter).init());
    container.resolve(EmailVerificationRouter).init(router);
    router.use('/facebook', container.resolve(FacebookCredentialsRouter).init());
    container.resolve(InstagramCredentialsRouter).init(router);
    container.resolve(FeedbacksRouter).init(router);
    router.use('/folders', container.resolve(FoldersRouter).init());
    container.resolve(GiftDrawsRouter).init(router);
    router.use('/google', container.resolve(GmbCredentialsRouter).init());
    router.use('/hashtags', container.resolve(HashtagsRouter).init());
    router.use('/hour-types', container.resolve(HourTypesRouter).init());
    router.use('/information-updates', container.resolve(InformationUpdatesRouter).init());
    container.resolve(KeywordsRouter).init(router);
    container.resolve(KeywordSearchImpressionsRouter).init(router);
    router.use('/labels', container.resolve(LabelsRouter).init());
    router.use('/maintenance', container.resolve(MaintenanceRouter).init());
    router.use('/media', container.resolve(MediaRouter).init());
    router.use('/mentions', container.resolve(MentionsRouter).init());
    router.use('/messages', container.resolve(MessagesRouter).init());
    router.use('/nfc', container.resolve(NfcsRouter).init());
    router.use('/notifications', container.resolve(NotificationsRouter).init());
    router.use('/organizations', container.resolve(OrganizationsRouter).init());
    container.resolve(PlatformInsightsRouter).init(router);
    router.use('/platforms', container.resolve(PlatformsRouter).init());
    router.use('/post-insights', container.resolve(PostInsightsRouter).init());
    container.resolve(PostInsightsRouterV2).init(router);
    container.resolve(PostsRouterV2).init(router);
    container.resolve(PostsRouter).init(router);
    router.use('/media', container.resolve(MediaRouter).init());
    router.use('/private-reviews', container.resolve(PrivateReviewsRouter).init());
    container.resolve(ProcessingMediasRouter).init(router);
    router.use('/publishers', container.resolve(PublishersRouter).init());
    router.use('/reports', container.resolve(ReportsRouter).init());
    router.use('/restaurants', container.resolve(RestaurantsRouter).init());
    router.use('/restaurant-ai-settings', container.resolve(RestaurantAiSettingsRouter).init());
    container.resolve(ReviewsRouter).init(router);
    container.resolve(ReviewAnalysesRouter).init(router);
    container.resolve(RoiRouter).init(router);
    router.use('/roi-settings', container.resolve(RoiSettingsRouter).init());
    container.resolve(ScansRouter).init(router);
    container.resolve(StoreLocatorRouter).init(router);
    container.resolve(SegmentAnalysesRouter).init(router);
    container.resolve(SegmentAnalysisParentTopicsRouter).init(router);
    container.resolve(ShiftsRouter).init(router);
    router.use('/similar-restaurants', container.resolve(SimilarRestaurantsRouter).init());
    router.use('/social-accounts', container.resolve(SocialAccountsRouter).init());
    container.resolve(StoriesRouter).init(router);
    router.use('/suggestions', container.resolve(SuggestionsRouter).init());
    router.use('/templates', container.resolve(TemplatesRouter).init());
    container.resolve(TiktokCredentialsRouter).init(router);
    router.use('/ubereats', container.resolve(UberEatsCredentialsRouter).init());
    router.use('/user-filters', container.resolve(UserFiltersRouter).init());
    container.resolve(UsersRouter).init(router);
    router.use('/webhooks', container.resolve(WebhooksRouter).init());
    router.use('/wheels-of-fortune', container.resolve(WheelsOfFortuneRouter).init());
    router.use('/yelp', container.resolve(YelpCredentialsRouter).init());
    container.resolve(ZeltyCredentialsRouter).init(router);
    router.use('/zenchef', container.resolve(ZenchefCredentialsRouter).init());

    router.use('/watchers', container.resolve(Watchers).init()); // to be removed

    return router;
};
