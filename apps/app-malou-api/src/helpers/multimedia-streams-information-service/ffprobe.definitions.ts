import { z } from 'zod';

/**
 * Ffprobe returns a bunch of data, feel free to add what you want in these validators
 */

const ffprobeAudioStreamValidator = z.object({ codec_type: z.literal('audio') });

const ffprobeVideoStreamValidator = z.object({
    /**
     * The field 'codec_type' is optional in ffprobe but we enforce it here so this field can be used as discriminator
     * in the union type in ffprobeResValidator. This is necessary because we want to be able to differentiate
     * between video and audio streams in the 'streams' array.
     */
    codec_type: z.literal('video'),
    height: z.number().optional(),
    width: z.number().optional(),
    duration: z.coerce.number().optional(),
    nb_frames: z.coerce.number().optional(),
    color_transfer: z.string().optional(),
    color_primaries: z.string().optional(),
    color_space: z.string().optional(),
});

export type FfprobeAudioStream = z.infer<typeof ffprobeAudioStreamValidator>;
export type FfprobeVideoStream = z.infer<typeof ffprobeVideoStreamValidator>;

export const ffprobeResValidator = z.object({
    streams: z.array(z.union([ffprobeAudioStreamValidator, ffprobeVideoStreamValidator])),
});

export type FfprobeResult = z.infer<typeof ffprobeResValidator>;
