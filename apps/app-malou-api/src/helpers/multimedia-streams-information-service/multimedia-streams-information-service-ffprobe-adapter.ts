import assert from 'node:assert/strict';
import { execFile } from 'node:child_process';
import { promisify } from 'node:util';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import {
    FfprobeResult,
    ffprobeResValidator,
    FfprobeVideoStream,
} from ':helpers/multimedia-streams-information-service/ffprobe.definitions';

import { MalouError } from '../classes/malou-error';
import {
    MultimediaStreamsInformationService,
    MultimediaVideoStreamSize,
    MultimediaVideoStreamSizeAndDuration,
} from './multimedia-streams-information-service';

const execFilePromisified = promisify(execFile);

const FFPROBE_PATH = 'ffprobe';

/**
 * This service requires ffmpeg to be installed on the system.
 *
 * It is actually installed in the Dockerfile for the deployed version of the app.
 * If you want test it locally, instal ffmpeg  with your package manager (preferably the same version as specified in the Dockerfile)
 */
@singleton()
export class MultimediaStreamsInformationServiceFfprobeAdapter implements MultimediaStreamsInformationService {
    async getSizeAndDuration(localPathOrUrl: string): Promise<MultimediaVideoStreamSizeAndDuration> {
        const result = await this._run(localPathOrUrl);
        const videoStream = this._getVideoStream(result);
        assert(videoStream);
        if (!videoStream.height || !videoStream.width || !videoStream.duration) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing height, width or duration information',
                metadata: { localPathOrUrl },
            });
        }
        return {
            height: videoStream.height,
            width: videoStream.width,
            durationInSeconds: videoStream.duration,
        };
    }

    async getSize(localPathOrUrl: string): Promise<MultimediaVideoStreamSize> {
        const result = await this._run(localPathOrUrl);
        const videoStream = this._getVideoStream(result);
        assert(videoStream);
        if (!videoStream.height || !videoStream.width) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing width or height information',
                metadata: { localPathOrUrl },
            });
        }
        return {
            height: videoStream.height,
            width: videoStream.width,
        };
    }

    async getDuration(localPathOrUrl: string): Promise<number> {
        const result = await this._run(localPathOrUrl);
        const videoStream = this._getVideoStream(result);
        assert(videoStream);
        if (!videoStream.duration) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing duration information',
                metadata: { localPathOrUrl },
            });
        }
        return videoStream.duration;
    }

    async getFrameCount(localPathOrUrl: string): Promise<number> {
        const result = await this._run(localPathOrUrl);
        const videoStream = this._getVideoStream(result);
        assert(videoStream);
        if (!videoStream.nb_frames) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing nb_frames information',
                metadata: { localPathOrUrl },
            });
        }
        return videoStream.nb_frames;
    }

    async isHdrVideo(localPathOrUrl: string): Promise<boolean> {
        const result = await this._run(localPathOrUrl);
        const videoStream = result.streams.find((stream) => stream.codec_type === 'video');
        assert(videoStream);

        const transfer = videoStream.color_transfer?.toLowerCase();
        const primaries = videoStream.color_primaries?.toLowerCase();
        const colorSpace = videoStream.color_space?.toLowerCase();

        const isHdr =
            transfer === 'smpte2084' || // PQ transfer (HDR10, Dolby Vision)
            transfer === 'arib-std-b67' || // HLG transfer
            !!(primaries === 'bt2020' && colorSpace?.startsWith('bt2020'));

        return isHdr;
    }

    private _getVideoStream(ffprobeResult: FfprobeResult): FfprobeVideoStream | undefined {
        return ffprobeResult.streams.find((stream) => stream.codec_type === 'video');
    }

    private _getArgs(localPathOrUrl: string): string[] {
        const prettyArgs = [
            ['-v', 'error'], // only show errors
            ['-show_streams'], // will show all streams information
            ['-of', 'json'],
            localPathOrUrl,
        ];
        return prettyArgs.flat();
    }

    private async _run(localPathOrUrl: string): Promise<FfprobeResult> {
        const { stdout } = await execFilePromisified(FFPROBE_PATH, this._getArgs(localPathOrUrl));
        const parsed = JSON.parse(stdout);
        const validated = ffprobeResValidator.parse(parsed);
        return validated;
    }
}
