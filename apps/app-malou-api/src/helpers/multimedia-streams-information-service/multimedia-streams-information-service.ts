export interface MultimediaVideoStreamSize {
    height: number;
    width: number;
}

export type MultimediaVideoStreamSizeAndDuration = MultimediaVideoStreamSize & { durationInSeconds: number };

export interface MultimediaStreamsInformationService {
    getSizeAndDuration(localPathOrUrl: string): Promise<MultimediaVideoStreamSizeAndDuration>;
    getSize(localPathOrUrl: string): Promise<MultimediaVideoStreamSize>;
    getDuration(localPathOrUrl: string): Promise<number>;
    getFrameCount(localPathOrUrl: string): Promise<number>;
    isHdrVideo(localPathOrUrl: string): Promise<boolean>;
}
