import { z } from 'zod';

export const configValidator = z
    .object({
        env: z.string(),
        iAmA: z.string().optional(),
        settings: z.object({
            adminEmail: z.string(),
            adminUpdatesNotificationEmail: z.string(),
            sendEmail: z.boolean(),
            sessionExpireTimeMs: z.number(),
            apiRoute: z.object({
                v1: z.string(),
            }),
        }),
        services: z.object({
            aws: z.object({ region: z.string(), key: z.string(), secret: z.string() }),
            browserless: z.object({
                baseEndpoint: z.string(),
                token: z.string(),
            }),
            puppeteer: z.object({
                arn: z.string(),
                authorization: z.string(),
                retries: z.number(),
            }),
            puppeteerV2: z.object({
                functionName: z.string(),
            }),
            github: z.object({
                appId: z.string(),
                installationId: z.string(),
                clientId: z.string(),
                clientSecret: z.string(),
                privateKey: z.string(),
            }),
            platformsScrapper: z.object({
                url: z.string(),
                functionName: z.string(),
                authorization: z.string(),
                retries: z.number(),
            }),
            fixedIpCaller: z.object({
                url: z.string(),
            }),
            nodeCrawlers: z.object({
                apiKey: z.string(),
            }),
            agenda: z.object({
                isConsumingJobs: z.boolean(),
            }),
            slack: z.object({
                webhookUrls: z.object({
                    appAlerts: z.string(),
                    slackAlerts: z.string(),
                }),
                techBot: z
                    .object({
                        botToken: z.string(),
                        signingSecret: z.string(),
                    })
                    .optional(),
            }),
            aiReviewsService: z.object({
                functionName: z.string(),
            }),
            aiKeywordsBreakdownService: z.object({
                functionName: z.string(),
            }),
            aiSemanticAnalysisService: z.object({
                functionName: z.string(),
            }),
            sqs: z.object({
                startSqsConsumer: z.boolean(),
                scrapperApiQueueUrl: z.string(),
                reviewsQueueUrl: z.string(),
                publishPostQueueUrl: z.string(),
                reviewBoosterSnsQueueUrl: z.string(),
                dailyReviewsReportsStartQueueUrl: z.string(),
                dailyReviewsReportsSendQueueUrl: z.string(),
                weeklyReviewsReportsStartQueueUrl: z.string(),
                weeklyReviewsReportsSendQueueUrl: z.string(),
                weeklyPerformanceReportsStartQueueUrl: z.string(),
                weeklyPerformanceReportsSendQueueUrl: z.string(),
                monthlyPerformanceReportsStartQueueUrl: z.string(),
                monthlyPerformanceReportsSendQueueUrl: z.string(),
                reviewsCatchUpQueueUrl: z.string(),
                ratingsCatchUpQueueUrl: z.string(),
                updateReviewRelevantBricksQueueUrl: z.string(),
                previousReviewsAnalysisFifoQueueUrl: z.string(),
                dailyRefreshPostInsightsQueueUrl: z.string(),
                generatePublicBusinessIdFifoQueueUrl: z.string(),
            }),
            s3: z.object({
                bucketName: z.string(),
            }),
            pubsub: z.object({
                startSubscription: z.boolean(),
            }),
            openai: z.object({
                reviewAnalysisPromptInstructions: z.string(),
                apiKey: z.string(),
                requestsPerMinuteLimit: z.number(),
                defaultResult: z.array(
                    z.object({
                        segment: z.string(),
                        tag: z.string(),
                        sentiment: z.string(),
                    })
                ),
            }),
            translator: z.object({
                functionName: z.string(),
                authorization: z.string(),
            }),
            mapstr: z.object({
                bearerToken: z.string(),
            }),
            diagnosticKeywordsGenerator: z.object({
                functionName: z.string(),
            }),
        }),
        cryptoJs: z.object({
            secret: z.string(),
        }),
        platforms: z.object({
            yelp: z.object({
                api: z.object({
                    params: z.object({
                        operationName: z.string(),
                        extensions: z.object({
                            documentId: z.string(),
                        }),
                    }),
                    headers: z.object({
                        'X-Requested-With': z.string(),
                        'content-type': z.string(),
                        'user-agent': z.string(),
                    }),
                }),
            }),
            foursquare: z.object({
                api: z.object({
                    clientId: z.string(),
                    clientSecret: z.string(),
                }),
            }),
            facebook: z.object({
                api: z.object({
                    appId: z.string(),
                    appSecret: z.string(),
                    appToken: z.string(),
                    apiVersion: z.string(),
                    redirectUri: z.string(),
                    timeoutThresholdInMs: z.number(),
                    postsLimit: z.string(),
                    nextCount: z.string(),
                }),
                webhooks: z.object({
                    token: z.string(),
                }),
            }),
            instagram: z.object({
                publicApi: z.object({
                    url: z.string(),
                }),
            }),
            lafourchette: z.object({
                api: z.object({
                    url: z.string(),
                    params: z.object({
                        search: z.string(),
                        overview: z.string(),
                        reviews: z.string(),
                        recentReviews: z.string(),
                    }),
                    headers: z.object({
                        'accept-language': z.string(),
                        'user-agent': z.string(),
                    }),
                }),
                clientApi: z.object({
                    token: z.string(),
                    clientId: z.string(),
                    clientSecret: z.string(),
                }),
                gqlApi: z.object({
                    reviewsLimitPerRequest: z.number(),
                    maxReviewsPerLang: z.number(),
                    minReviewsPerLang: z.number(),
                }),
            }),
            tripadvisor: z.object({
                api: z.object({
                    params: z.object({
                        search: z.string(),
                    }),
                    headers: z.object({
                        'Content-type': z.string(),
                        'X-Requested-With': z.string(),
                        'accept-language': z.string(),
                        Connection: z.string(),
                        origin: z.string(),
                        Cookie: z.string(),
                        'user-agent': z.string(),
                    }),
                }),
            }),
            pagesjaunes: z.object({
                scrapWebsite: z.boolean(),
            }),
            ubereats: z.object({
                api: z.object({
                    client: z.object({
                        baseUri: z.string(),
                        id: z.string(),
                        secret: z.string(),
                        redirectUri: z.string(),
                    }),
                    url: z.string(),
                    headers: z.object({
                        'Content-Type': z.string(),
                        'Accept-Encoding': z.string(),
                        Connection: z.string(),
                        Authority: z.string(),
                        Accept: z.string(),
                        'Accept-Language': z.string(),
                        Origin: z.string(),
                        'User-Agent': z.string(),
                        'X-Csrf-Token': z.string(),
                    }),
                    accountId: z.string(),
                }),
            }),
            gmb: z.object({
                api: z.object({
                    clientId: z.string(),
                    clientSecret: z.string(),
                    redirectUris: z.string(),
                }),
            }),
            tiktok: z.object({
                api: z.object({
                    clientId: z.string(),
                    clientSecret: z.string(),
                    redirectUri: z.string(),
                    baseUri: z.string(),
                    authorizeUri: z.string(),
                }),
            }),
            appleBusinessConnect: z.object({
                api: z.object({
                    url: z.string(),
                    version: z.string(),
                    companyId: z.string(),
                    clientId: z.string(),
                    clientSecret: z.string(),
                }),
            }),
            zelty: z.object({
                api: z.object({
                    version: z.string(),
                }),
            }),
        }),
        multer: z.object({
            limits: z.object({
                fileSize: z.object({
                    image: z.number(),
                    video: z.number(),
                }),
            }),
        }),
        geolocation: z.object({
            modulo: z.number(),
            degreeDecimal: z.number(),
            keywordRadiusSearchMeters: z.number(),
            gmapsUrl: z.string(),
            gmapsHostname: z.string(),
            gmapsEndpoint: z.string(),
            gmapsApiKey: z.string(),
            maloupeGmapsApiKey: z.string(),
            fetchRankings: z.boolean(),
            newGmapsApiRollout: z.number().min(0).max(1).default(0),
        }),
        vendors: z.object({
            keywordVolume: z.object({
                surfer: z.object({
                    url: z.string(),
                    keywordQuery: z.string(),
                }),
            }),
            scrapperProxy: z.object({
                baseUrl: z.string(),
                queryParams: z.string(),
            }),
            proxy: z.object({
                getProxyUrl: z.string(),
                amount: z.number(),
            }),
        }),
        bricks: z.object({
            ttlSeconds: z.number(),
        }),
        tests: z.object({
            mockRestaurantId: z.string(),
            mockUserId: z.string(),
            mockRestaurantIdWithoutPerms: z.string(),
        }),
        businessNotificationsAccountKey: z.object({
            project_id: z.string(),
            private_key: z.string(),
            client_email: z.string(),
        }),
        firebaseAccountKey: z.object({
            project_id: z.string(),
            private_key: z.string(),
            client_email: z.string(),
        }),
        businessCommunications: z.object({
            agentDefaultUpdateMask: z.string(),
        }),
        logger: z.object({
            level: z.string(),
            transports: z.object({
                file: z.object({
                    filename: z.string().optional(),
                    fileLevel: z.string().optional(),
                }),
            }),
        }),
        baseAppUrl: z.string(),
        baseApiUrl: z.string(),
        baseApiMaloupeUrl: z.string(),
        gitCommitSha: z.string(),
        branchName: z.string(),
        cacheImplementation: z.string(),
        redis: z.object({
            host: z.string(),
            port: z.number(),
        }),
        malouInternalApiToken: z.string(),
    })
    .strict();
