import axios, { AxiosInstance } from 'axios';
import http from 'http';
import https from 'https';
import { parse as parseHtml } from 'node-html-parser';
import { singleton } from 'tsyringe';

import { ProviderMetricsService } from ':providers/provider.metrics.service';
import {
    SevenroomsGetPaginatedReviewsResponse,
    SevenroomsGetVenuesResponse,
    SevenroomsPort,
    SevenroomsUser,
} from ':providers/sevenrooms/sevenrooms.provider.interface';

@singleton()
export class SevenroomsProvider implements SevenroomsPort {
    private readonly _axiosInstance: AxiosInstance;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        const axiosInstance = axios.create({
            baseURL: 'https://www.sevenrooms.com/',
            headers: {
                accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'fr,fr-FR;q=0.9,en;q=0.8,en-US;q=0.7',
                priority: 'u=0, i',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'none',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent':
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            },
            httpAgent: new http.Agent({ keepAlive: false }),
            httpsAgent: new https.Agent({ keepAlive: false }),
        });
        this._axiosInstance = axiosInstance;
    }

    async getReservationUrl({ csrfToken, sessionId }: { csrfToken: string; sessionId: string }): Promise<string> {
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'manager.home',
            request: () =>
                this._axiosInstance.get('/manager/home?', {
                    validateStatus: (status: number) => status >= 200 && status < 400,
                    headers: {
                        Cookie: cookie,
                    },
                }),
        });

        return response.request.res.responseUrl;
    }

    async doesUserHaveMarketingAccess({
        sessionId,
        socialId,
        email,
    }: {
        sessionId: string;
        socialId: string;
        email: string;
    }): Promise<boolean> {
        const response = await this._getUsersHtmlPage({
            sessionId,
            socialId,
        });
        const dom = parseHtml(response);
        // It's a table that we iterate line by line so on a specific line we dont know the role of the user
        // So we have to keep track if we are in a "Superuser" section or not
        let isCheckingSuperUser = false;
        return (
            dom.querySelector('.user-list')?.childNodes.some((userNode) => {
                const rawText = userNode.rawText.trim();
                const isRoleRow = rawText.includes('explain permissions');
                isCheckingSuperUser = isRoleRow ? rawText.includes('Superuser') : isCheckingSuperUser;
                const foundUser = rawText.includes(email);
                if (isCheckingSuperUser && foundUser) {
                    // The user is in the superuser section, so has all access
                    return true;
                }

                if (foundUser) {
                    const hasMarketingUserAccess = rawText.includes('Access Marketing Tools');
                    return hasMarketingUserAccess;
                }
                return false;
            }) || false
        );
    }

    async getPaginatedReviews({
        venueId,
        csrfToken,
        sessionId,
        page,
    }: {
        venueId: string;
        csrfToken: string;
        sessionId: string;
        page: number;
    }): Promise<SevenroomsGetPaginatedReviewsResponse> {
        const queryParams = {
            venue: venueId,
            sites: 'sevenrooms',
            limit: 20,
            offset: page * 20,
            sort: '-date',
        };
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'reviews.paginated',
            request: () =>
                this._axiosInstance.get('/api-yoa/reviews?', {
                    params: queryParams,
                    headers: { Cookie: cookie },
                }),
        });
        return response.data;
    }

    async getVenueId({
        sessionId,
        csrfToken,
        socialId,
    }: {
        sessionId: string;
        csrfToken: string;
        socialId: string;
    }): Promise<string | null> {
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'venue.context',
            request: () =>
                this._axiosInstance.get(`/api-yoa/venue/${socialId}/app_context`, {
                    headers: { Cookie: cookie },
                }),
        });
        return response.data?.data?.venue_id || null;
    }

    async getVenues({ sessionId, csrfToken }: { sessionId: string; csrfToken: string }): Promise<SevenroomsGetVenuesResponse> {
        const queryParams = {
            // TODO: @Tanguy check if this id is only for Maslow group or for all locations that gestionclients manages
            venue_group_id: 'ahNzfnNldmVucm9vbXMtc2VjdXJlciELEhRuaWdodGxvb3BfVmVudWVHcm91cBiAgObO4fmyCQw',
        };
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'venue.users',
            request: () =>
                this._axiosInstance.get('/api-yoa/venue/user_venue_users?', {
                    params: queryParams,
                    headers: { Cookie: cookie },
                }),
        });
        if (response.status !== 200) {
            throw new Error(`Failed to fetch reviews: ${response.statusText}`);
        }
        return response.data;
    }

    async postReviewReply({
        csrfToken,
        sessionId,
        venueId,
        conversationId,
        userId,
        replyPayload,
    }: {
        csrfToken: string;
        sessionId: string;
        venueId: string;
        conversationId: string;
        userId: string;
        replyPayload: { comment: string };
    }): Promise<any> {
        const visibility = 'a'; // 'a' for all i guess
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        return await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'manager.message.create',
            request: () =>
                this._axiosInstance.post(
                    `/manager/${venueId}/message/create`,
                    {
                        conversation_id: conversationId,
                        message: replyPayload.comment,
                        visibility,
                        entity_id: userId,
                    },
                    {
                        headers: {
                            Cookie: cookie,
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'x-csrftoken': csrfToken,
                        },
                    }
                ),
        });
    }

    async getUserInformation({
        csrfToken,
        sessionId,
        socialId,
        userId,
    }: {
        csrfToken: string;
        sessionId: string;
        socialId: string;
        userId: string;
    }): Promise<SevenroomsUser | null> {
        const queryParams = {
            rid: userId,
        };
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'user.information',
            request: () =>
                this._axiosInstance.get(`/api-yoa/reservation/${socialId}/actual/${userId}`, {
                    params: queryParams,
                    headers: { Cookie: cookie },
                }),
        });

        if (!response.data) {
            return null;
        }

        return response.data;
    }

    private async _getUsersHtmlPage({ sessionId, socialId }: { sessionId: string; socialId: string }): Promise<string> {
        const cookie = `sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const userAccessListUrl = `/manager/${socialId}/access/user/list`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'manager.users.page',
            request: () =>
                this._axiosInstance.get(userAccessListUrl, {
                    headers: {
                        Cookie: cookie,
                    },
                }),
        });

        return response.data;
    }

    private async _getReviewHtmlPage({
        socialId,
        csrfToken,
        sessionId,
    }: {
        socialId: string;
        csrfToken: string;
        sessionId: string;
    }): Promise<string> {
        const cookie = `csrftoken=${csrfToken}; sessionid=${sessionId}; sessionid_keepalive="<(*^*)>";`;
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'sevenrooms',
            requestId: 'manager2.reviews.page',
            request: () =>
                this._axiosInstance.get(`/manager2/${socialId}/marketing/reviews`, {
                    headers: { Cookie: cookie },
                }),
        });
        return response.data;
    }
}
