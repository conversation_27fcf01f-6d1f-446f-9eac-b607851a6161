export interface SevenroomsPort {
    getReservationUrl({ csrfToken, sessionId }: { csrfToken: string; sessionId: string }): Promise<string>;
    doesUserHaveMarketingAccess({ sessionId, socialId, email }: { sessionId: string; socialId: string; email: string }): Promise<boolean>;
    getPaginatedReviews({
        venueId,
        csrfToken,
        sessionId,
        page,
    }: {
        venueId: string;
        csrfToken: string;
        sessionId: string;
        page: number;
    }): Promise<SevenroomsGetPaginatedReviewsResponse>;
    getVenueId({ sessionId, csrfToken, socialId }: { sessionId: string; csrfToken: string; socialId: string }): Promise<string | null>;
}

export interface SevenroomsGetResponse<T> {
    status: number;
    data: T;
    user: string;
    crsftoken: string | null;
    request_id: string | null;
}

export type SevenroomsGetPaginatedReviewsResponse = SevenroomsGetResponse<SevenroomsReviewResponseData>;

export interface SevenroomsReviewResponseData {
    results: SevenroomsReview[];
    total: number;
    stats: SevenroomsReviewStats;
    last_updated_dt: string;
    search_profile: string | null;
}

export interface SevenroomsReview {
    actual_id: string;
    actual_name: string;
    author: string;
    author_avatar: string | null;
    created: string;
    date: string;
    deleted: boolean | null;
    external_id: string;
    feedback_type: string;
    id: string;
    influences: unknown | null;
    is_negative_rating: boolean;
    is_neutral_rating: boolean;
    is_positive_rating: boolean;
    is_sevenrooms_feedback: boolean;
    link: string | null;
    notes: string | null;
    order_id: string | null;
    parent_id: string | null;
    rating: number;
    reply: SevenroomsReply;
    review_site: string;
    review_site_name: string;
    source: string;
    title: string | null;
    updated: string;
    venue_id: string;
}

export type SevenroomsReply = unknown;

export interface SevenroomsReviewStats {
    sevenrooms: {
        '1': number;
        '2': number;
        '3': number;
        '4': number;
        '5': number;
    };
}

export type SevenroomsGetVenuesResponse = SevenroomsGetResponse<SevenroomsVenuesData>;

export interface SevenroomsVenuesData {
    venues: SevenroomsVenue[];
    venue_search_groups: {
        public: unknown[];
        private: unknown[];
    };
}

export interface SevenroomsVenue {
    // We always have those fields for each venues
    id: string;
    id_short: number;
    venue_group_id: string;
    url_key: string;
    name: string;
    internal_name: string | null;
    venue_class: SevenroomsVenueClass;
    privilege: SevenroomsPrivilege;

    // We only get the following fields for the current venue
    is_full_info: boolean;
    account_id: string;
    always_locked_client_fields: unknown[];
    autoselect_table: boolean;
    booking_res_require_either_phone_email: boolean;
    booking_res_require_email: boolean;
    booking_res_require_lname: boolean;
    booking_res_require_phone: boolean;
    booking_res_require_salutation: boolean;
    client_lookup_hotel_list: unknown[];
    confirmation_include_end_time: boolean;
    connected_setup_intents: boolean;
    cost_options: unknown[];
    country_code: string;
    custom_fields_config: SevenroomsCustomFieldsConfig;
    currency_symbol: string;
    currency_code: string;
    default_seating_area: unknown;
    features: SevenroomsFeatures;
    feedback_send_times_by_shift: SevenroomsFeedbackSendTimesByShift;
    is_freedompay_hpc_enabled: boolean;
    is_orders_integration_enabled: boolean;
    is_private_events_tab_enabled: boolean;
    is_sevenrooms_ordering_enabled: boolean;
    is_sevenrooms_ordering_middleware: boolean;
    is_sizzle_enabled: boolean;
    is_sso_required: boolean;
    sso_enablement_status: number;
    is_ro_insight_enabled: boolean;
    is_reporting_revenue_dashboard_enabled: boolean;
    superhero_ro_access: boolean;
    landing_page_enabled: boolean;
    locale: string;
    lock_contact_info_on_presence_of_this_field: string;
    lockable_client_fields: unknown[];
    menu_integration_source: string;
    ot_gc_enabled: boolean;
    is_resy_reservation_manual_import_enabled: boolean;
    paylink_only: boolean;
    is_moto_enabled: boolean;
    payment_can_save_card: boolean;
    payment_public_token: string;
    payment_system: string;
    payments_enabled: boolean;
    payout_profile_id: string;
    promoter_tally_enabled: boolean;
    reminder_send_times_by_shift: SevenroomsReminderSendTimesByShift;
    reminders_sms_enabled: boolean;
    requests_availability_enabled: boolean;
    enable_bonvoy_widget_login: boolean;
    send_email_confirmations_default: boolean;
    send_reminder_default_method: string;
    send_booking_delivery_method: string;
    sms_booking_notification_enabled: boolean;
    service_statuses: SevenroomsServiceStatuses;
    show_availability_seatingarea_covers: boolean;
    show_perks_field: boolean;
    start_of_day_hour: number;
    tax_groups: unknown[];
    tax_rate: number;
    timezone: string;
}

export enum SevenroomsVenueClass {
    DINING = 'DINING',
}

export enum SevenroomsPrivilege {
    MANAGER = 'MANAGER',
}

export interface SevenroomsUser {
    status: number;
    data: SevenroomsUserData;
    user: string;
    csrftoken: string;
    request_id: string;
}

export interface SevenroomsUserData {
    actual: SevenroomsActual;
    disable_edit: boolean;
    is_past_res: boolean;
}

export interface SevenroomsActual {
    kind: string;
    id: string;
    _id: string;
    date: string;
    venue_group_client: SevenroomsVenueGroupClient;
    reservation_feedback: unknown;
    source_client: unknown;
    venue_url_key: string;
    paylink_auto_cancel_minutes: unknown;
    paylink_auto_cancel_datetime: unknown;
    access_rule: SevenroomsAccessRule;
    payments_expired: boolean;
    is_res_editable: boolean;
    additional_reservation_cards: unknown[];
    has_cancellation_charge: boolean;
    date_arrival_time_dt_sync_dt_formatted: string;
    custom_venue_4: unknown;
    min_price_type: string;
    table_ids_list: string;
    is_concierge_reservation: boolean;
    public_notes: string;
    basicuser_or_concierge_or_client_name: string;
    source_client_tags_group: unknown[];
    perk_instructions_list: unknown[];
    prepayment_gratuity_formatted: string;
    hotel_check_out_date: unknown;
    is_not_completed: boolean;
    non_pos_onsite_gratuity_percentage_formatted: string;
    non_pos_onsite_tax_percentage_formatted: string;
    custom_unindexed: unknown;
    created_timestamp: string;
    table_codes_display: string;
    hotel_check_in_date: unknown;
    send_feedback_email_destination: unknown;
    payments_card_token: unknown;
    via: string;
    client_display_name: string;
    arrival_time_display: string;
    exclude_from_shift_pacing: boolean;
    is_duration_picked: boolean;
    prepayment_tax_percentage_formatted: string;
    comps_price_type: unknown;
    payments_billing_profile: unknown;
    paid_by_name: unknown;
    is_client_promo: boolean;
    send_reminder_sms: boolean;
    prepayment_formatted: string;
    booked_flex_state: unknown;
    min_price: unknown;
    venue_seating_area_name: string;
    is_canceled: boolean;
    is_confirmed: boolean;
    onsite_payment_gratuity_formatted: string;
    source_client_first_name: string;
    system_class: string;
    onsite_payment_formatted: string;
    source_client_email: string;
    min_price_formatted: string;
    client_rating_num: unknown;
    phone_number: string;
    hotel_confirmation: unknown;
    payments_card_type: unknown;
    client_set_photo: unknown;
    venue_seating_area_id: string;
    payments_last_four: unknown;
    is_private_event: boolean;
    client_requests: string;
    duration: number;
    is_auto_assign: boolean;
    client_sort_name: string;
    final_bill_formatted_no_decimals: string;
    source_client_last_name: string;
    onsite_payment_tax_formatted: string;
    full_notes_to_html: string;
    hotel_room: unknown;
    is_non_3p_reservation: boolean;
    date_formatted_long_abbrev_display: string;
    phone_number_locale: string;
    reservation_tag_names: string;
    reservation_tags: SevenroomsReservationTag[];
    mf_ratio_short_display: string;
    price_formatted: string;
    date_urlparam: string;
    has_pos_tickets: boolean;
    disable_auto_assign: boolean;
    perks_freeform_to_html: string;
    full_notes: string;
    gross_total_formatted: string;
    hotel_id: unknown;
    reservation_id: string;
    booked_flex_is_outside_standard_range: unknown;
    received_formatted: string;
    client_tags: SevenroomsClientTag[];
    source_client_phone: string;
    table_id: unknown;
    table_codes_list: unknown[];
    has_chat_recipient_email: boolean;
    total_guests: number;
    served_by_name: unknown;
    payments_card_id: unknown;
    comps_formatted: string;
    summary_notes_html: string;
    send_reminder_sms_destination: unknown;
    source_client_phone_locale: string;
    prepayment_tax_formatted: string;
    automatically_included_upsells: unknown[];
    date_formatted_short: string;
    created_date_formatted: string;
    can_rebook_different_venue: boolean;
    hotel_rate_code: unknown;
    prepayment_gratuity_percentage_formatted: string;
    bookedby_alt_view: unknown[];
    arrival_time_sort_order: number;
    primary_card: unknown;
    pos_aggregate_items: SevenroomsPosAggregateItems;
    is_comp: boolean;
    is_reconciled: boolean;
    status: string;
    prepayment_net_formatted: string;
    send_client_email_destination: unknown;
    client_photo: unknown;
    source_client_id: unknown;
    private_event_name: unknown;
    tailored_communication_opt_in: unknown;
    status_formatted: string;
    max_guests_formatted: string;
    prepayment_service_charge_formatted: string;
    source_client_compunknown: string;
    date_format_mostly_full_no_year: string;
    mf_ratio_females: unknown;
    departure_time_display: string;
    send_feedback_email: boolean;
    mf_ratio_males: unknown;
    using_default_duration: boolean;
    has_charge: boolean;
    end_time_formatted: string;
    custom_venue_0: unknown;
    buffer_mins: number;
    max_guests: number;
    created_time_payment_format: string;
    reservation_sms_opt_in: boolean;
    channel: string;
    booked_with_required_upgrade_override: boolean;
    is_reservation: boolean;
    system_class_display: string;
    source_client_name: string;
    onsite_payment_total_formatted: string;
    phone_number_formatted: string;
    is_client_vip: boolean;
    transaction_type: string;
    shift_persistent_id: string;
    is_nomin: boolean;
    email_address: string;
    billing_profile_expired: boolean;
    num_bottles_sold: unknown;
    reservation_summary_tag_names: string;
    is_guestlist_entry: boolean;
    custom_venue_3: unknown;
    custom_venue_1: unknown;
    attachments: string;
    client_notes: unknown;
    followers: Record<string, SevenroomsFollowersData>;
    custom_group_3: unknown;
    first_name: string;
    custom_group_0: unknown;
    send_client_sms_destination: unknown;
    perks_freeform: string;
    send_reminder_email: boolean;
    comps: unknown;
    duration_display: string;
    custom_group_1: unknown;
    payments_name_on_card: unknown;
    booked_with_override: boolean;
    conversation: string;
    concierge_or_client_name: string;
    reference_code: string;
    access_persistent_id: string;
    final_bill_formatted: string;
    source_client_tags_display: string;
    private_notes: string;
    est_arrival_time_display: string;
    venue_id: string;
    tags_display: SevenroomsTagsDisplay[];
    send_reminder_email_destination: unknown;
    check_numbers: unknown;
    booked_flex_max_table_size: unknown;
    custom_group_2: unknown;
    custom_venue_2: unknown;
    last_name: string;
    created_time_formatted: string;
    payments_charge_amount_formatted: string;
    experience_id: unknown;
    is_custom_assign: boolean;
    custom_group_4: unknown;
    cost_option: number;
}

export interface SevenroomsVenueGroupClient {
    kind: string;
    id: string;
    _id: string;
    custom_venue_0: string;
    custom_venue_1: string;
    custom_venue_2: string;
    custom_venue_3: string;
    custom_venue_4: string;
    credit_cards: unknown[];
    has_venue_in_marketing_opt_in_list: boolean;
    has_venue_in_sizzle_unsubscribed_venues_list: boolean;
    is_phone_alt_viewable: boolean;
    birthday_day: number;
    address_2: unknown;
    is_phone_editable: boolean;
    notes: string;
    is_city_editable: boolean;
    loyalty_tier: unknown;
    preferred_language_code: string;
    gender_display: string;
    birthday_month: number;
    total_noshows: number;
    custom_unindexed: unknown;
    tags_group_display: SevenroomsTagsGroupDisplay[];
    address_formatted_html: string;
    state: unknown;
    city_display: string;
    state_display: string;
    country: unknown;
    is_email_address_editable: boolean;
    billing_profile_key: string;
    gender: string;
    phone_number_alt_locale: string;
    title: unknown;
    title_compunknown_display: string;
    loyalty_id_display: string;
    phone_number: string;
    loyalty_rank_display: string;
    address_2_display: string;
    total_spend_per_visit_formatted: string;
    postal_code: unknown;
    city: unknown;
    phone_number_locale: string;
    member_groups_json: string;
    is_address_editable: boolean;
    is_notes_editable: boolean;
    loyalty_id: unknown;
    birthday_display: string;
    is_email_address_viewable: boolean;
    loyalty_rank: unknown;
    email_address_alt: string;
    is_postal_code_editable: boolean;
    external_user_id: unknown;
    phone_number_alt_formatted: string;
    avg_rating_formatted: string;
    country_display: string;
    total_spend_formatted_no_decimals: string;
    is_state_editable: boolean;
    anniversary_day: unknown;
    is_contact_private: boolean;
    compunknown: unknown;
    status: string;
    loyalty_tier_display: string;
    total_visits: number;
    total_spend_per_cover_formatted: string;
    address_1_display: string;
    name_only_display: string;
    postal_code_display: string;
    status_display: string;
    address: unknown;
    phone_number_formatted: string;
    email_address: string;
    is_country_editable: boolean;
    anniversary_month: unknown;
    salutation: unknown;
    custom_group_3: unknown;
    first_name: string;
    custom_group_0: unknown;
    is_phone_viewable: boolean;
    is_phone_alt_editable: boolean;
    custom_group_1: unknown;
    total_cancellations: number;
    name_display: string;
    tags_display: SevenroomsTagsDisplay[];
    custom_group_2: unknown;
    created_by_display: string;
    last_name: string;
    custom_group_4: unknown;
}

export interface SevenroomsTagsGroupDisplay {
    id: string;
    name: string;
    tag_sort_order: number;
    is_private: boolean;
    is_restricted: number;
    tag_color: string;
    tags: SevenroomsTag[];
}

export interface SevenroomsTag {
    is_private: boolean;
    tag_group_id: string;
    tag_sort_order: number;
    tag_color: string;
    is_restricted: number;
    tag_group_name: string;
    tag_group_name_display: string;
    tag_name: string;
    tag_name_display: string;
    is_global: boolean;
    is_autotag: boolean;
    is_for_marketing_segmentation: boolean;
    perks_by_tag: unknown;
    notes_by_tag: unknown;
    is_source: number;
    is_hot_reservation: number;
    domain: string;
    entity_key: string;
}

export interface SevenroomsTagsDisplay {
    is_private: boolean;
    tag_group_id: string;
    tag_sort_order: number;
    tag_color: string;
    is_restricted: number;
    tag_group_name: string;
    tag_group_name_display: string;
    tag_name: string;
    tag_name_display: string;
    is_global: boolean;
    is_autotag: boolean;
    is_for_marketing_segmentation: boolean;
    perks_by_tag: unknown;
    notes_by_tag: unknown;
    is_source: number;
    is_hot_reservation: number;
    domain: string;
    entity_key: string;
}

export interface SevenroomsAccessRule {
    id: string;
    table_ids: unknown[];
    auto_cutoff_type: unknown;
    apply_service_charge: boolean;
    require_gratuity_charge: boolean;
    persistent_id: string;
    restrict_to_shifts: boolean;
    start_time: unknown;
    cancellation_policy_type: unknown;
    use_shift_payment_and_policy: boolean;
    cc_gratuity: unknown;
    is_using_shift_upsells: boolean;
    end_time_display: unknown;
    public_time_slot_description: string;
    is_pacing_held: boolean;
    shift_categories: string[];
    auto_cutoff_num: unknown;
    require_credit_card: boolean;
    duration_minutes_by_party_size: unknown;
    access_time_type: string;
    default_pacing: number;
    is_location_rule: boolean;
    party_size_min: number;
    is_indefinite: boolean;
    is_party_size_rule: boolean;
    seating_area_ids: string[];
    duration_min: unknown;
    last_edited_by_id: string;
    cancellation_policy: unknown;
    upsell_categories: string[];
    google_reserve_seating_area: string;
    payment_policy_id: unknown;
    end_time_sort_order: number;
    cancel_cutoff_hour: unknown;
    reservation_tags: string[];
    apply_gratuity_charge: boolean;
    service_charge_type: unknown;
    booking_policy_id: unknown;
    end_time: unknown;
    is_duration_rule: boolean;
    cancel_cutoff_type: unknown;
    duration_max: unknown;
    auto_charge_type: string;
    created: string;
    cutoff_type: string;
    cc_cost: unknown;
    cancel_cutoff_num: unknown;
    cutoff_num: number;
    cc_apply_tax_rate: unknown;
    start_time_display: unknown;
    selected_automatic_upsells: unknown[];
    cc_party_size_min: unknown;
    start_time_sort_order: number;
    source_access_rule_id: unknown;
    public_long_form_description: unknown;
    tax_group_id: unknown;
    auto_cutoff_hour: unknown;
    has_pacing_restriction: boolean;
    public_description_title: unknown;
    is_shift_default_party_sizes: boolean;
    policy_type: unknown;
    table_area_selection_display: string;
    start_of_day_hour: number;
    is_held: boolean;
    auto_charge_amount_type: string;
    auto_charge_amount: unknown;
    public_photo: unknown;
    created_date: string;
    inventory_count: unknown;
    party_size_max: number;
    cutoff_hour: unknown;
    ignore_cc_for_3p_bookers: boolean;
    custom_pacing: Record<string, number>;
    service_charge: unknown;
    gratuity_type: unknown;
    updated: string;
    thefork_seating_area: string;
    cc_charge_type: unknown;
    cc_payment_rule: unknown;
    is_override: boolean;
    ignore_descriptions_for_3p_bookers: boolean;
    auto_charge_amount_in_cents: unknown;
    policy: unknown;
    inventory_type: string;
    venue_id: string;
    shift_category: unknown;
    public_photo_sizes: unknown;
    name: string;
    experience_id: unknown;
}

export interface SevenroomsReservationTag {
    id: string;
    name: string;
    tag_sort_order: number;
    is_private: boolean;
    is_restricted: number;
    tag_color: string;
    tags: SevenroomsTag[];
}

export interface SevenroomsClientTag {
    is_private: boolean;
    tag_group_id: string;
    tag_sort_order: number;
    tag_color: string;
    is_restricted: number;
    tag_group_name: string;
    tag_group_name_display: string;
    tag_name: string;
    tag_name_display: string;
    is_global: boolean;
    is_autotag: boolean;
    is_for_marketing_segmentation: boolean;
    perks_by_tag: unknown;
    notes_by_tag: unknown;
    is_source: number;
    is_hot_reservation: number;
    domain: string;
    entity_key: string;
}

export interface SevenroomsPosAggregateItems {
    hasCharges: boolean;
    subtotal: string;
    tax_percentage: string;
    tax: string;
    tax_value: number;
    tip_percentage: string;
    tip: string;
    tip_value: number;
    admin_fee: unknown;
    admin_fee_percentage: string;
    total: string;
    items: unknown[];
    server_name: string;
}

export interface SevenroomsFollowersData {
    id: string;
    full_name: string;
    initials: string;
}

interface SevenroomsCustomFieldsConfig {
    actual: SevenroomsCustomFieldConfig[];
    client: SevenroomsCustomFieldConfig[];
    order: SevenroomsCustomFieldConfig[];
}

interface SevenroomsCustomFieldConfig {
    name: string;
    db_name: string;
    enabled: boolean;
    editable: boolean;
    display_order: number;
}

interface SevenroomsFeatures {
    can_access_client_tab: boolean;
    can_access_insights_page: boolean;
    can_access_operations_tab: boolean;
    can_access_private_events_tab: boolean;
    can_access_requests_tab: boolean;
    can_activate_floorplan_layout: boolean;
    can_administrate_blackout_dates_daily_program: boolean;
    can_administrate_shifts_access_rules: boolean;
    can_charge: boolean;
    can_edit_booking_block: boolean;
    can_edit_floorplan: boolean;
    can_edit_maximum_total_covers_for_shift: boolean;
    can_export_reservations_data: boolean;
    can_manage_private_data: boolean;
    can_manage_room_invites: boolean;
    can_revoke_room_invites: boolean;
    can_manage_subscriptions: boolean;
    can_manage_review_channel: boolean;
    can_overbook: boolean;
    can_overbook_access_blocks: boolean;
    can_overbook_enforced_shift_party_size: boolean;
    can_overbook_larger_tables: boolean;
    can_overbook_no_tables: boolean;
    can_overbook_pacing: boolean;
    can_overbook_smaller_tables: boolean;
    can_overbook_total_shift_covers: boolean;
    can_override_payment_requirement: boolean;
    can_refund: boolean;
    can_send_custom_sms_on_mobile: boolean;
    can_view_client_spend_data: boolean;
    can_view_clients_across_venue_group: boolean;
    can_view_pos_check_data: boolean;
    hide_actuals_report: boolean;
    is_benefits_enabled: boolean;
    make_reservation_booker_follower: boolean;
}

interface SevenroomsFeedbackSendTimesByShift {
    BREAKFAST: string;
    BRUNCH: string;
    LUNCH: string;
    DAY: string;
    DINNER: string;
    LEGACY: string;
    orders: unknown;
}

interface SevenroomsReminderSendTimesByShift {
    BREAKFAST: string;
    BRUNCH: string;
    LUNCH: string;
    DAY: string;
    DINNER: string;
    LEGACY: string;
}

interface SevenroomsServiceStatuses {
    'in-service': SevenroomsService[];
    'pre-service': SevenroomsService[];
}

interface SevenroomsService {
    confirmed: boolean;
    deleted: boolean;
    enabled: boolean;
    editable: boolean;
    dead: boolean;
    custom: boolean;
    color: string;
    db_name: string;
    display: string;
}
