import { randomUUID } from 'node:crypto';

import { CountryC<PERSON>, YextAddRequestStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { randomString } from ':helpers/utils';
import { YextLocation } from ':modules/publishers/yext/entities/yext-location.entity';
import { IYextProvider } from ':providers/yext/yext.provider.interface';

import {
    YextAddServiceResponseBody,
    YextCancelServiceResponseBody,
    YextCreateAddRequestResponseBody,
    YextEntity,
    YextGetAddRequestResponseBody,
    YextGetListingsResponseBody,
    YextGetServicesResponseBody,
    YextServiceStatus,
} from './yext.provider.interfaces';

export class YextProviderMock implements IYextProvider {
    async createAddRequestForNewLocation(
        _accountId: string,
        _accountName: string | undefined,
        _entity: YextEntity
    ): Promise<{ partnerLocationId: string; responseBody: YextCreateAddRequestResponseBody }> {
        return {
            partnerLocationId: randomUUID(),
            responseBody: {
                meta: {
                    uuid: randomUUID(),
                },
                response: {
                    id: randomUUID(),
                    status: YextAddRequestStatus.SUBMITTED,
                    newLocationAccountId: randomUUID(),
                },
            },
        };
    }

    async getAddRequestForLocation(addRequestId: string): Promise<YextGetAddRequestResponseBody> {
        return {
            meta: {
                uuid: randomUUID(),
            },
            response: {
                id: addRequestId,
                status: YextAddRequestStatus.COMPLETE,
            },
        };
    }

    async addServicesForLocation({
        accountId: _accountId,
        locationId,
        skus,
    }: {
        accountId: string;
        locationId: string;
        skus: string[];
    }): Promise<YextAddServiceResponseBody> {
        return {
            response: {
                id: randomUUID(),
                existingLocationId: locationId,
                locationMode: 'EXISTING',
                skus,
                status: YextAddRequestStatus.COMPLETE,
                dateSubmitted: new Date().toISOString(),
                dateCompleted: new Date().toISOString(),
                statusDetail:
                    'The request has been submitted for processing. Updated status should be available soon, usually within seconds.',
            },
        };
    }

    async deleteEntity(_accountId: string, _locationId: string): Promise<void> {
        logger.info('YextProviderMock.deleteEntity empty');
    }

    async getLocationServices(
        _location: Pick<YextLocation, 'partnerLocationId'>,
        _yextAccountId: string
    ): Promise<YextGetServicesResponseBody> {
        const uuid = randomUUID();
        return {
            response: {
                count: 2,
                services: [
                    {
                        id: 626889,
                        sku: randomString(12),
                        serviceDescription: 'Location Cloud Starter',
                        agreementId: 11690,
                        locationId: uuid,
                        status: YextServiceStatus.ACTIVE,
                        started: '2024-06-05',
                    },
                    {
                        id: 626888,
                        sku: randomString(11),
                        serviceDescription: 'Yext for Food (KE)',
                        agreementId: 11690,
                        locationId: uuid,
                        status: YextServiceStatus.ACTIVE,
                        started: '2024-06-05',
                    },
                ],
            },
        };
    }

    async cancelServicesForLocation(_accountId: string, _locationId: string): Promise<YextCancelServiceResponseBody> {
        return {
            response: [],
        };
    }

    async getListingsForLocation(_accountId: string, _locationIds: string[]): Promise<YextGetListingsResponseBody> {
        return {
            meta: {
                uuid: randomUUID(),
            },
            response: {
                count: 0,
                listings: [],
                pageToken: '',
            },
        };
    }

    getSkusByCountryCode(_countryCode: CountryCode | undefined): string[] {
        return ['yext-for-food'];
    }
}
