import { CountryCode } from '@malou-io/package-utils';

import { YextLocation } from ':modules/publishers/yext/entities/yext-location.entity';

import {
    YextAddServiceResponseBody,
    YextCancelServiceResponseBody,
    YextCreateAddRequestResponseBody,
    YextEntity,
    YextGetAddRequestResponseBody,
    YextGetListingsResponseBody,
    YextGetServicesResponseBody,
} from './yext.provider.interfaces';

export interface IYextProvider {
    createAddRequestForNewLocation(
        accountId: string,
        accountName: string | undefined,
        entity: YextEntity
    ): Promise<{ partnerLocationId: string; responseBody: YextCreateAddRequestResponseBody }>;

    getAddRequestForLocation(addRequestId: string): Promise<YextGetAddRequestResponseBody>;

    addServicesForLocation(params: { accountId: string; locationId: string; skus: string[] }): Promise<YextAddServiceResponseBody>;

    getLocationServices(location: Pick<YextLocation, 'partnerLocationId'>, accountId: string): Promise<YextGetServicesResponseBody>;

    deleteEntity(accountId: string, locationId: string): Promise<void>;

    cancelServicesForLocation(accountId: string, locationId: string): Promise<YextCancelServiceResponseBody>;

    getListingsForLocation(accountId: string, locationIds: string[]): Promise<YextGetListingsResponseBody>;

    getSkusByCountryCode(countryCode: CountryCode | undefined): string[];
}
